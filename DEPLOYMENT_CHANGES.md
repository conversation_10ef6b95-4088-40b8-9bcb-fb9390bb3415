# 部署脚本修改说明

## 修改概述

已成功修改部署脚本，去掉对sshpass的依赖，改用rsync进行远程文件同步，并删除了密码配置。现在脚本使用SSH密钥认证进行安全连接。

## 主要修改内容

### 1. 删除密码相关配置

- **deploy.sh**: 删除了 `REMOTE_PASSWORD` 默认变量
- **deploy.config**: 删除了 `REMOTE_PASSWORD=K#L5J/Wk|f!7-$` 配置行
- **命令行参数**: 移除了 `-p|--password` 参数选项

### 2. 替换sshpass为SSH密钥认证

- 删除了 `install_sshpass()` 函数
- 删除了sshpass相关的依赖检查
- 更新了SSH连接函数，移除密码认证逻辑
- 添加了SSH密钥认证检查功能

### 3. 使用rsync替代scp

- 将 `get_scp_cmd()` 函数替换为 `get_rsync_cmd()`
- 将 `upload_to_remote()` 函数重命名为 `sync_to_remote()`
- 使用rsync的优势：
  - 增量同步，只传输变化的部分
  - 显示传输进度
  - 更好的错误处理
  - 支持断点续传

### 4. 增强的依赖检查

- 添加了rsync安装检查
- 添加了SSH密钥认证测试
- 移除了scp依赖检查（不再需要）

### 5. 更新的帮助信息

- 更新了使用说明，移除密码相关选项
- 添加了SSH密钥认证的提示信息

## 新增功能

### SSH密钥认证检查
脚本现在会在执行前自动检查SSH密钥认证是否正常工作：

```bash
# 检查SSH密钥认证
if ! ssh -o BatchMode=yes -o ConnectTimeout=5 -p $REMOTE_PORT ${REMOTE_USER}@${REMOTE_HOST} exit 2>/dev/null; then
    log_error "SSH密钥认证失败，请确保："
    log_error "1. 已将本地公钥添加到远程服务器的 ~/.ssh/authorized_keys"
    log_error "2. 远程服务器SSH服务正常运行"
    log_error "3. 网络连接正常"
    exit 1
fi
```

### rsync同步功能
使用rsync替代scp，提供更好的文件同步体验：

```bash
# 使用rsync同步，显示进度
rsync -avz --progress -e 'ssh -o StrictHostKeyChecking=no -p $REMOTE_PORT' \
    "build/libs/${JAR_NAME}" ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/
```

## 安全性提升

1. **移除密码存储**: 不再在配置文件中存储明文密码
2. **SSH密钥认证**: 使用更安全的密钥认证方式
3. **配置文件权限检查**: 继续检查配置文件权限安全性

## 使用方法

### 前置条件
1. 确保已配置SSH密钥到远程服务器（参考 SSH_SETUP.md）
2. 确保本地安装了rsync

### 运行部署
```bash
# 使用配置文件
./deploy.sh

# 使用命令行参数
./deploy.sh -u root -s ***************
```

## 兼容性说明

- **向后兼容**: 现有的配置文件仍然可用（除了密码配置被忽略）
- **系统要求**: 需要rsync和SSH客户端
- **网络要求**: 需要SSH密钥认证已配置

## 故障排除

如果遇到问题，请参考：
1. `SSH_SETUP.md` - SSH密钥配置指南
2. 脚本会自动检查SSH连接并提供详细错误信息
3. 使用 `ssh -v` 命令进行详细的连接调试

## 文件清单

修改的文件：
- `deploy.sh` - 主部署脚本
- `deploy.config` - 配置文件

新增的文件：
- `SSH_SETUP.md` - SSH密钥配置指南
- `DEPLOYMENT_CHANGES.md` - 本修改说明文档
