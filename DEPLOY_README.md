# Spring Boot 应用部署脚本

这个脚本用于自动化部署Spring Boot应用到远程服务器。

## 功能特性

- 🏗️ **自动构建**: 使用Gradle清理并构建项目
- 📦 **智能备份**: 自动备份当前运行的jar文件
- 🚀 **无缝部署**: 上传新版本到远程服务器
- 🔄 **服务管理**: 优雅停止旧服务并启动新服务
- 🧹 **自动清理**: 清理旧日志和临时文件
- ⚙️ **灵活配置**: 支持配置文件和命令行参数

## 快速开始

### 1. 初始化配置文件

首次使用时，运行配置初始化脚本：

```bash
./setup-config.sh
```

或者手动创建配置文件：

```bash
cp deploy.config.example deploy.config
chmod 600 deploy.config
```

### 2. 配置部署参数

编辑 `deploy.config` 文件：

```bash
# 远程服务器配置
REMOTE_USER=ubuntu
REMOTE_HOST=*************
REMOTE_PORT=22
REMOTE_PASSWORD=your_server_password  # 可选，建议使用SSH密钥

# 部署路径
REMOTE_PATH=/home/<USER>

# Java应用配置
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
SERVER_PORT=8080
SPRING_PROFILE=prod
```

### 3. 执行部署

```bash
# 使用配置文件
./deploy.sh

# 或使用命令行参数（支持密码）
./deploy.sh -u ubuntu -s ************* -p your_password

# 或使用SSH密钥（推荐）
./deploy.sh -u ubuntu -s *************
```

## 详细配置

### deploy.config 配置文件说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `REMOTE_USER` | 远程服务器用户名 | your_username |
| `REMOTE_HOST` | 远程服务器IP/域名 | your_server_ip |
| `REMOTE_PORT` | SSH端口 | 22 |
| `REMOTE_PASSWORD` | 远程服务器密码 | your_password |
| `REMOTE_PATH` | 远程部署路径 | /home/<USER>
| `JAVA_OPTS` | Java启动参数 | -Xms512m -Xmx1024m -XX:+UseG1GC |
| `SERVER_PORT` | 应用端口 | 8080 |
| `SPRING_PROFILE` | Spring配置文件 | prod |
| `LOG_RETENTION_DAYS` | 日志保留天数 | 7 |
| `BACKUP_ENABLED` | 是否启用备份 | true |
| `BACKUP_COUNT` | 备份文件保留数量 | 3 |

## 使用方法

### 命令行选项

```bash
./deploy.sh [选项]

选项:
  -h, --help       显示帮助信息
  -u, --user       指定远程用户名
  -s, --server     指定远程服务器IP
  -p, --password   指定远程服务器密码

示例:
  ./deploy.sh -u ubuntu -s ************* -p your_password
  ./deploy.sh --user=root --server=example.com --password=secret
  ./deploy.sh  # 使用deploy.config配置文件
```

### 部署流程

脚本按以下顺序执行：

1. **检查依赖** - 验证必要的工具（ssh, scp, sshpass, gradle）
2. **构建Spring Boot jar** - 执行 `./gradlew clean bootJar`
3. **备份远程** - 备份当前运行的jar文件
4. **上传文件** - 上传新构建的jar到远程服务器
5. **清理环境** - 删除旧日志和临时文件
6. **停止服务** - 优雅停止当前运行的Java服务
7. **启动服务** - 启动新版本的Java服务

## 前置条件

### 本地环境
- Java 21+ 和 Gradle
- SSH客户端工具 (`ssh`, `scp`)
- `sshpass` 工具（密码认证时需要，脚本会自动安装）
- 项目根目录下有 `gradlew` 文件

### 远程服务器
- 已安装Java 21+ 运行环境
- SSH服务已启用
- 用户有相应目录的读写权限
- 防火墙已开放应用端口（默认8080）

### 认证方式配置

#### 方式1：SSH密钥认证（推荐）

```bash
# 1. 生成SSH密钥（如果还没有）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 2. 复制公钥到远程服务器
ssh-copy-id username@server_ip

# 3. 测试连接
ssh username@server_ip

# 4. 在配置文件中不设置密码或设置为空
REMOTE_PASSWORD=
```

#### 方式2：密码认证

```bash
# 1. 在配置文件中设置密码
REMOTE_PASSWORD=your_server_password

# 2. 确保配置文件权限安全
chmod 600 deploy.config

# 3. 脚本会自动安装sshpass工具
# macOS: brew install hudochenkov/sshpass/sshpass
# Ubuntu: sudo apt-get install sshpass
```

## 监控和日志

### 查看应用日志
```bash
# 实时查看日志
ssh username@server_ip 'tail -f /home/<USER>/logs/app.log'

# 查看最近的日志
ssh username@server_ip 'tail -100 /home/<USER>/logs/app.log'
```

### 检查服务状态
```bash
# 检查Java进程
ssh username@server_ip 'ps aux | grep java'

# 检查端口占用
ssh username@server_ip 'netstat -tlnp | grep :8080'
```

### 服务管理
```bash
# 手动停止服务
ssh username@server_ip 'pkill -f "java.*ContentManager"'

# 手动启动服务
ssh username@server_ip 'cd /home/<USER>/app.log 2>&1 &'
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查服务器IP和端口
   - 确认SSH服务是否启动
   - 验证用户名和认证方式

2. **构建失败**
   - 检查Java版本是否匹配
   - 确认依赖是否正确下载
   - 查看构建日志详细错误信息

3. **服务启动失败**
   - 检查端口是否被占用
   - 查看应用日志文件
   - 确认Java内存设置是否合理

4. **权限问题**
   - 确认用户对部署目录有写权限
   - 检查jar文件的执行权限

### 回滚操作

如果新版本出现问题，可以手动回滚到备份版本：

```bash
# 1. 连接到远程服务器
ssh username@server_ip

# 2. 进入部署目录
cd /home/<USER>

# 3. 停止当前服务
pkill -f "java.*ContentManager"

# 4. 查看可用备份
ls -la backup/

# 5. 恢复备份版本
cp backup/ContentManager-0.0.4-SNAPSHOT.jar.20231221_143022 ContentManager-0.0.4-SNAPSHOT.jar

# 6. 重启服务
nohup java -Xms512m -Xmx1024m -XX:+UseG1GC -jar ContentManager-0.0.4-SNAPSHOT.jar --server.port=8080 --spring.profiles.active=prod > logs/app.log 2>&1 &
```

## 安全注意事项

### 认证安全
- 🔐 **优先使用SSH密钥认证**而不是密码
- 🔒 如果使用密码，确保配置文件权限为600
- 🚫 **不要将包含密码的配置文件提交到版本控制**
- 🔄 定期更换密码和密钥

### 网络安全
- 🛡️ 限制SSH访问的IP范围
- 🔥 配置防火墙规则
- 🛠️ 使用非标准SSH端口

### 系统安全
- 📝 定期检查和更新服务器安全补丁
- 🔍 监控应用日志中的异常活动
- 💾 定期备份重要数据和配置
- 👤 使用专用的部署用户账户

### 配置文件安全
```bash
# 设置配置文件安全权限
chmod 600 deploy.config

# 添加到.gitignore
echo "deploy.config" >> .gitignore

# 使用环境变量（可选）
export DEPLOY_PASSWORD="your_password"
```

## 许可证

此脚本遵循MIT许可证。
