# docx4j读取Word标题级别分析

## 问题回答

**问题**: 假设Word文档中包含1级标题、2级标题、3级标题、4级标题、5级标题、6级标题，docx4j读出来的Word文档，是否会包含完整的heading1、heading2、heading3、heading4、heading5、heading6呢？

**答案**: **是的，docx4j完全可以读取Word文档中的所有6个级别的标题样式。**

## 技术原理

### 1. Word文档中的标题样式存储

在Word文档(.docx)中，标题样式信息存储在段落属性中：
- 每个段落(P)都有段落属性(PPr)
- 段落属性中包含样式信息(PStyle)
- 样式ID通常为: `Heading1`, `Heading2`, `Heading3`, `Heading4`, `Heading5`, `Heading6`

### 2. docx4j读取方式

```java
// 获取段落的样式ID
if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
    String styleId = para.getPPr().getPStyle().getVal();
    // styleId 可能的值: "Heading1", "Heading2", ..., "Heading6"
}
```

### 3. 实际实现

我们已经在`Docx4jSplitterService`中实现了完整的6级标题检测：

```java
// 单独的检测方法
private boolean isHeading1(P para) { ... }
private boolean isHeading2(P para) { ... }
private boolean isHeading3(P para) { ... }
private boolean isHeading4(P para) { ... }
private boolean isHeading5(P para) { ... }
private boolean isHeading6(P para) { ... }

// 通用检测方法
private boolean isHeadingLevel(P para, int level) { ... }

// 获取标题级别
private int getHeadingLevel(P para) { ... }
```

## 可能的样式变体

不同的Word版本或语言环境可能使用略有不同的样式名称：

### 标准格式
- `Heading1`, `Heading2`, `Heading3`, `Heading4`, `Heading5`, `Heading6`

### 可能的变体
- `heading 1`, `heading 2` (小写+空格)
- `heading1`, `heading2` (小写无空格)
- `Heading 1`, `Heading 2` (标准+空格)
- `HEADING1`, `HEADING2` (全大写)
- `HEADING 1`, `HEADING 2` (全大写+空格)

### 中文环境
- `标题1`, `标题2`, `标题3`, `标题4`, `标题5`, `标题6`

## 验证工具

我们提供了`HeadingLevelAnalyzer`工具来分析任何Word文档中的标题结构：

```java
@Autowired
private HeadingLevelAnalyzer analyzer;

// 分析文档中的所有标题级别
analyzer.analyzeHeadingLevels("your-document.docx");
```

输出示例：
```
=== 分析文档标题级别: document.docx ===

📋 标题级别统计:
✅ Heading1: 5个
   1. 第一章 概述
   2. 第二章 技术架构
   3. 第三章 实现方案
   ...

✅ Heading2: 12个
   1. 1.1 项目背景
   2. 1.2 技术选型
   3. 2.1 系统架构
   ...

✅ Heading3: 8个
✅ Heading4: 3个
❌ Heading5: 未找到
❌ Heading6: 未找到

📊 所有段落样式统计:
🏷️ Heading1: 5个
🏷️ Heading2: 12个
🏷️ Heading3: 8个
🏷️ Heading4: 3个
📄 Normal: 156个
📄 List Paragraph: 23个
```

## 使用建议

### 1. 创建测试文档
建议创建一个包含所有6级标题的测试文档来验证：

```
Heading1: 第一章 概述
  Heading2: 1.1 项目背景
    Heading3: 1.1.1 技术需求
      Heading4: 1.1.1.1 功能需求
        Heading5: 1.1.1.1.1 基础功能
          Heading6: 1.1.1.1.1.1 详细说明
```

### 2. 运行分析工具
```java
@Test
public void testYourDocument() throws Exception {
    HeadingLevelAnalyzer analyzer = new HeadingLevelAnalyzer();
    analyzer.analyzeHeadingLevels("your-test-document.docx");
}
```

### 3. 检查结果
- 确认所有预期的标题级别都被正确识别
- 检查是否有意外的样式变体
- 验证标题文本内容是否正确提取

## 结论

**docx4j完全支持读取Word文档中的所有6个标题级别**，你可以：

1. ✅ 使用我们实现的`isHeading1()`到`isHeading6()`方法
2. ✅ 使用通用的`isHeadingLevel(para, level)`方法
3. ✅ 使用`getHeadingLevel(para)`获取任意段落的标题级别
4. ✅ 处理各种可能的样式名称变体
5. ✅ 使用分析工具验证具体文档的标题结构

现在你的分割功能已经具备了处理所有6级标题的能力！
