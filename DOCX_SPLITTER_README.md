# DOCX文件智能分割功能

## 功能概述

改进后的`Docx4jSplitterService`提供了基于字数限制的智能文档分割功能，能够根据指定的字数阈值自动进行多级分割：

1. **一级分割**：首先按照Heading1（一级标题）进行分割
2. **二级分割**：如果一级分割后的章节字数仍超过限制，则按照Heading2（二级标题）进一步分割
3. **段落分割**：如果二级分割后仍超过限制，则按段落进行最终分割

## 主要方法

### 1. splitByHeading(String sourceFilePath, String outputDirPath)
- **功能**：按Heading1分割文档（不限制字数）
- **参数**：
  - `sourceFilePath`：源DOCX文件路径
  - `outputDirPath`：输出目录路径
- **返回值**：`List<File>` 分割后的文件列表

### 2. splitByHeading(String sourceFilePath, String outputDirPath, int maxWordCount)
- **功能**：按指定字数限制智能分割文档
- **参数**：
  - `sourceFilePath`：源DOCX文件路径
  - `outputDirPath`：输出目录路径
  - `maxWordCount`：最大字数限制
- **返回值**：`List<File>` 分割后的文件列表

## 使用示例

### 基本使用
```java
@Autowired
private Docx4jSplitterService docx4jSplitterService;

// 按1000字限制分割文档
List<File> files = docx4jSplitterService.splitByHeading(
    "input/document.docx", 
    "output/", 
    1000
);
```

### 不同字数限制的效果

#### 大字数限制（如5000字）
- 主要按Heading1分割
- 只有极长的章节才会进一步分割
- 文件命名：`chapter_01_章节标题.docx`

#### 中等字数限制（如1000字）
- 按Heading1分割后，超过限制的章节按Heading2分割
- 文件命名：`chapter_01_02_章节标题_子章节标题.docx`

#### 小字数限制（如500字）
- 多级分割，最终可能按段落分割
- 文件命名：`chapter_01_02_03_章节标题.docx`

## 文件命名规则

1. **一级分割**：`chapter_{章节序号}_{章节标题}.docx`
   - 示例：`chapter_01_第一章概述.docx`

2. **二级分割**：`chapter_{章节序号}_{子章节序号}_{章节标题}_{子章节标题}.docx`
   - 示例：`chapter_01_02_第一章概述_基本概念.docx`

3. **段落分割**：`chapter_{章节序号}_{子章节序号}_{片段序号}_{章节标题}.docx`
   - 示例：`chapter_01_02_03_第一章概述.docx`

## 字数统计方式

- 采用去除空白字符后的字符长度作为字数统计标准
- 适用于中文文档的字数统计
- 计算公式：`text.replaceAll("\\s+", "").length()`

## 注意事项

1. **标题样式要求**：
   - 一级标题必须使用"Heading1"样式
   - 二级标题必须使用"Heading2"样式

2. **文件名处理**：
   - 自动过滤文件名中的非法字符：`\/:*?"<>|`
   - 长标题会被截断以避免文件名过长

3. **异常处理**：
   - 如果文档中没有标题，所有内容会归入第一个章节
   - 空文档或无内容段落会被跳过

4. **性能考虑**：
   - 大文档分割可能需要较长时间
   - 建议在后台异步执行分割任务

## 测试用例

参考 `Docx4jSplitterServiceTest.java` 中的测试用例：
- `testSplitByHeadingWithWordLimit()`：测试带字数限制的分割
- `testSplitByHeadingWithoutWordLimit()`：测试不限制字数的分割
- `testSplitByHeadingWithSmallWordLimit()`：测试小字数限制的多级分割
