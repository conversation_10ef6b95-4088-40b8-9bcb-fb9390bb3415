# DOCX 文件智能分割功能

## 功能概述

改进后的`Docx4jSplitterService`提供了基于字数限制的智能文档分割功能，能够根据指定的字数阈值自动进行多级分割：

1. **一级分割**：首先按照 Heading1（一级标题）进行分割
2. **二级分割**：如果一级分割后的章节字数仍超过限制，则按照 Heading2（二级标题）进一步分割
3. **段落分割**：如果二级分割后仍超过限制，则按段落进行最终分割

## 主要方法

### 1. splitByHeading(String sourceFilePath, String outputDirPath)

- **功能**：按 Heading1 分割文档（不限制字数）
- **参数**：
  - `sourceFilePath`：源 DOCX 文件路径
  - `outputDirPath`：输出目录路径
- **返回值**：`List<File>` 分割后的文件列表

### 2. splitByHeading(String sourceFilePath, String outputDirPath, int maxWordCount)

- **功能**：按指定字数限制智能分割文档
- **参数**：
  - `sourceFilePath`：源 DOCX 文件路径
  - `outputDirPath`：输出目录路径
  - `maxWordCount`：最大字数限制
- **返回值**：`List<File>` 分割后的文件列表

### 3. splitByParagraphsOnly(String sourceFilePath, String outputDirPath, int maxWordCount)

- **功能**：纯段落分割（适用于没有标题结构的文档）
- **参数**：
  - `sourceFilePath`：源 DOCX 文件路径
  - `outputDirPath`：输出目录路径
  - `maxWordCount`：最大字数限制
- **返回值**：`List<File>` 分割后的文件列表

## 使用示例

### 基本使用

```java
@Autowired
private Docx4jSplitterService docx4jSplitterService;

// 按1000字限制分割文档
List<File> files = docx4jSplitterService.splitByHeading(
    "input/document.docx",
    "output/",
    1000
);
```

### 不同字数限制的效果

#### 大字数限制（如 5000 字）

- 主要按 Heading1 分割
- 只有极长的章节才会进一步分割
- 文件命名：`chapter_01_章节标题.docx`

#### 中等字数限制（如 1000 字）

- 按 Heading1 分割后，超过限制的章节按 Heading2 分割
- 文件命名：`chapter_01_02_章节标题_子章节标题.docx`

#### 小字数限制（如 500 字）

- 多级分割，最终可能按段落分割
- 文件命名：`chapter_01_02_03_章节标题.docx`

## 文件命名规则

1. **一级分割**：`chapter_{章节序号}_{章节标题}.docx`

   - 示例：`chapter_01_第一章概述.docx`

2. **二级分割**：`chapter_{章节序号}_{子章节序号}_{章节标题}_{子章节标题}.docx`

   - 示例：`chapter_01_02_第一章概述_基本概念.docx`

3. **段落分割**：`chapter_{章节序号}_{子章节序号}_{片段序号}_{章节标题}.docx`
   - 示例：`chapter_01_02_03_第一章概述.docx`

## 字数统计方式

- 采用去除空白字符后的字符长度作为字数统计标准
- 适用于中文文档的字数统计
- 计算公式：`text.replaceAll("\\s+", "").length()`

## 异常情况处理

### 1. 无标题文档处理

- **没有 Heading1**：所有内容归入一个章节，使用第一个段落内容作为标题
- **没有 Heading2**：直接按段落分割，不进行二级分割
- **完全没有标题**：可使用`splitByParagraphsOnly()`方法进行纯段落分割

### 2. 特殊内容处理

- **空段落**：自动跳过，不计入字数统计
- **超长段落**：单个段落超过字数限制时，单独保存为一个文件（文件名添加"\_long_para"标识）
- **空文档**：返回空的文件列表，不会产生任何输出文件

### 3. 标题生成规则

- **有标题**：使用实际标题内容
- **无标题**：使用段落开头内容（限制 30 字符）
- **空内容**：使用默认名称（如"chapter_1", "section"等）

## 注意事项

1. **标题样式要求**：

   - 一级标题建议使用"Heading1"样式
   - 二级标题建议使用"Heading2"样式
   - 如果没有标题样式，系统会自动按段落分割

2. **文件名处理**：

   - 自动过滤文件名中的非法字符：`\/:*?"<>|`
   - 长标题会被截断以避免文件名过长
   - 空标题会使用默认命名规则

3. **字数限制建议**：

   - 建议设置合理的字数限制（如 500-2000 字）
   - 过小的限制会产生大量小文件
   - 过大的限制可能无法有效分割

4. **性能考虑**：
   - 大文档分割可能需要较长时间
   - 建议在后台异步执行分割任务
   - 字数限制越小，生成的文件数量越多

## 测试用例

参考 `Docx4jSplitterServiceTest.java` 中的测试用例：

- `testSplitByHeadingWithWordLimit()`：测试带字数限制的分割
- `testSplitByHeadingWithoutWordLimit()`：测试不限制字数的分割
- `testSplitByHeadingWithSmallWordLimit()`：测试小字数限制的多级分割
- `testSplitDocumentWithoutHeadings()`：测试没有标题的文档分割
- `testSplitByParagraphsOnly()`：测试纯段落分割功能
- `testSplitEmptyDocument()`：测试空文档的异常处理

## 使用建议

1. **首次使用**：建议先用小文档测试，确认分割效果符合预期
2. **字数设置**：根据实际需求设置合适的字数限制
3. **文档准备**：确保文档使用标准的标题样式以获得最佳分割效果
4. **异常处理**：对于特殊格式的文档，可能需要预处理或使用纯段落分割模式
