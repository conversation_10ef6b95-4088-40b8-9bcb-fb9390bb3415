# SSH密钥配置指南

本部署脚本已更新为使用SSH密钥认证，不再依赖密码认证。请按照以下步骤配置SSH密钥。

## 1. 生成SSH密钥对（如果还没有）

在本地机器上运行：

```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 按提示操作：
# - 选择密钥保存位置（默认：~/.ssh/id_rsa）
# - 设置密码短语（可选，建议设置）
```

## 2. 将公钥复制到远程服务器

### 方法一：使用ssh-copy-id（推荐）

```bash
# 将公钥复制到远程服务器
ssh-copy-id -p 22 root@140.143.236.199

# 输入远程服务器密码（最后一次）
```

### 方法二：手动复制

```bash
# 1. 查看本地公钥内容
cat ~/.ssh/id_rsa.pub

# 2. 登录到远程服务器
ssh -p 22 root@140.143.236.199

# 3. 在远程服务器上创建.ssh目录（如果不存在）
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 4. 将公钥内容添加到authorized_keys文件
echo "你的公钥内容" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# 5. 退出远程服务器
exit
```

## 3. 测试SSH密钥认证

```bash
# 测试SSH连接（应该不需要输入密码）
ssh -p 22 root@140.143.236.199

# 如果成功连接且不需要密码，说明配置正确
```

## 4. 运行部署脚本

现在可以运行部署脚本，它将使用SSH密钥进行认证：

```bash
# 使用配置文件
./deploy.sh

# 或者使用命令行参数
./deploy.sh -u root -s 140.143.236.199
```

## 故障排除

### 如果SSH连接失败：

1. **检查SSH服务状态**：
   ```bash
   # 在远程服务器上检查SSH服务
   sudo systemctl status ssh
   # 或
   sudo systemctl status sshd
   ```

2. **检查防火墙设置**：
   ```bash
   # 确保SSH端口（默认22）未被防火墙阻止
   sudo ufw status
   ```

3. **检查SSH配置**：
   ```bash
   # 检查SSH配置文件
   sudo nano /etc/ssh/sshd_config
   
   # 确保以下设置：
   # PubkeyAuthentication yes
   # AuthorizedKeysFile .ssh/authorized_keys
   ```

4. **检查文件权限**：
   ```bash
   # 在远程服务器上检查权限
   ls -la ~/.ssh/
   
   # 正确的权限应该是：
   # ~/.ssh/ 目录: 700
   # ~/.ssh/authorized_keys 文件: 600
   ```

5. **查看SSH连接日志**：
   ```bash
   # 使用详细模式连接以查看错误信息
   ssh -v -p 22 root@140.143.236.199
   ```

## 安全建议

1. **禁用密码认证**（可选）：
   在远程服务器的 `/etc/ssh/sshd_config` 中设置：
   ```
   PasswordAuthentication no
   ```
   然后重启SSH服务：
   ```bash
   sudo systemctl restart ssh
   ```

2. **使用非标准SSH端口**（可选）：
   修改 `/etc/ssh/sshd_config` 中的端口设置，并相应更新部署配置。

3. **定期更新SSH密钥**：
   建议定期更换SSH密钥以提高安全性。
