plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.0'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.swyx.api'
version = '0.0.4-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

configurations {
    mbgen
}


dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'

    implementation 'org.springframework:spring-jdbc'
    implementation 'org.springframework:spring-core'
    implementation 'org.aspectj:aspectjrt'
    implementation 'org.aspectj:aspectjweaver'
    implementation 'org.mybatis:mybatis:3.5.10'
    implementation 'org.mybatis:mybatis-spring:3.0.3'
    implementation 'com.alibaba:druid:1.2.21'
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'org.mybatis.generator:mybatis-generator-core:1.4.1'
    implementation 'jakarta.inject:jakarta.inject-api:2.0.1'
    implementation 'org.crac:crac:1.4.0'
    implementation 'mysql:mysql-connector-java:8.0.33'

    implementation 'com.qcloud:cos_api:5.6.227'

    implementation 'com.openai:openai-java:0.32.0'
    implementation 'com.squareup.okio:okio:3.5.0'
    implementation 'org.apache.commons:commons-lang3:3.12.0'

    // docx4j核心依赖
    implementation 'org.docx4j:docx4j-core:8.3.3'
    implementation 'org.docx4j:docx4j-JAXB-Internal:8.3.3'
    implementation 'org.docx4j:docx4j-JAXB-ReferenceImpl:8.3.3'

    // JAXB运行时依赖 (Java 9+需要) - 使用与docx4j兼容的版本
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'org.glassfish.jaxb:jaxb-runtime:2.3.3'
    implementation 'org.glassfish.jaxb:jaxb-core:2.3.0.1'

    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    mbgen 'org.mybatis.generator:mybatis-generator-core:1.4.1'
    mbgen 'mysql:mysql-connector-java:8.0.33'

    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
}

tasks.named('test') {
    useJUnitPlatform()
}

// mybatis代码自动生成task
//tasks.register('mbgen', DefaultTask) {
//   ant.properties['src_main_resources'] = sourceSets.main.resources.srcDirs[0].path
//   ant.properties['src_main_java'] = sourceSets.main.java.srcDirs[0].path
//   ant.taskdef(
//           name: 'mbgen',
//           classname: 'org.mybatis.generator.ant.GeneratorAntTask',
//           classpath: configurations.mbgen.asPath
//   )
//   ant.mbgen(overwrite: true, configfile: projectDir.path + '/src/test/resources/mybatis-generator-config.xml', verbose: true) {
//       propertyset {
//           propertyref(name: 'src_main_java')
//           propertyref(name: 'src_main_resources')
//       }
//   }
//}


