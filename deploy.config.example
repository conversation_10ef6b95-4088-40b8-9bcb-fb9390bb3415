# 部署配置文件模板
# 请复制此文件为 deploy.config 并修改相应配置

# ================================
# 远程服务器配置
# ================================
REMOTE_USER=ubuntu
REMOTE_HOST=*************
REMOTE_PORT=22
REMOTE_PASSWORD=your_server_password

# ================================
# 部署路径配置
# ================================
REMOTE_PATH=/home/<USER>

# ================================
# Java应用配置
# ================================
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
SERVER_PORT=8080
SPRING_PROFILE=prod

# ================================
# 应用信息（自动从build.gradle读取）
# ================================
APP_NAME=ContentManager
APP_VERSION=0.0.4-SNAPSHOT
JAR_NAME=ContentManager-0.0.4-SNAPSHOT.jar

# ================================
# 日志配置
# ================================
LOG_RETENTION_DAYS=7
LOG_MAX_SIZE=100M

# ================================
# 备份配置
# ================================
BACKUP_ENABLED=true
BACKUP_COUNT=3

# ================================
# 安全提示
# ================================
# 1. 建议使用SSH密钥而不是密码认证
# 2. 如果必须使用密码，请确保此文件权限为600
# 3. 不要将此文件提交到版本控制系统
# 4. 生产环境建议使用专用的部署用户
