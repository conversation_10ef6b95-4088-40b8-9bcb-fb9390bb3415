#!/bin/bash

# Spring Boot应用部署脚本
# 功能：编译构建、上传到远程服务器、停止旧服务、启动新服务

set -e  # 遇到错误立即退出

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/deploy.config"

# 默认配置变量
REMOTE_USER="your_username"
REMOTE_HOST="your_server_ip"
REMOTE_PORT=22
REMOTE_PATH="/home/<USER>"
JAR_NAME="ContentManager-0.0.4-SNAPSHOT.jar"
SERVICE_NAME="content-manager"
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
SERVER_PORT=8080
SPRING_PROFILE=prod
LOG_RETENTION_DAYS=7
BACKUP_ENABLED=true
BACKUP_COUNT=3

# 加载配置文件
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        # 首先声明日志函数可能还没定义，所以直接echo
        echo -e "\033[0;32m[INFO]\033[0m 加载配置文件: $CONFIG_FILE"
        # 只加载非注释行
        while IFS='=' read -r key value; do
            # 跳过注释行和空行
            [[ $key =~ ^#.*$ ]] && continue
            [[ -z $key ]] && continue
            # 移除值两端的引号
            value=$(echo "$value" | sed 's/^"\|"$//g')
            # 设置变量（移除-g选项以兼容旧版bash）
            eval "$key='$value'"
        done < "$CONFIG_FILE"
    else
        echo -e "\033[1;33m[WARN]\033[0m 配置文件未找到: $CONFIG_FILE"
        echo -e "\033[1;33m[WARN]\033[0m 将使用默认配置或命令行参数"
    fi
}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查并安装rsync（如果需要）
install_rsync() {
    if ! command -v rsync &> /dev/null; then
        log_info "rsync 未安装，正在安装..."
        if command -v brew &> /dev/null; then
            brew install rsync
        elif command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y rsync
        elif command -v yum &> /dev/null; then
            sudo yum install -y rsync
        else
            log_error "无法自动安装rsync，请手动安装后重试"
            log_error "macOS: brew install rsync"
            log_error "Ubuntu/Debian: sudo apt-get install rsync"
            log_error "CentOS/RHEL: sudo yum install rsync"
            exit 1
        fi
    fi
}

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."

    if ! command -v ssh &> /dev/null; then
        log_error "ssh 命令未找到，请安装openssh-client"
        exit 1
    fi

    # 检查并安装rsync
    install_rsync

    if [[ ! -f "./gradlew" ]]; then
        log_error "gradlew 文件未找到，请确保在项目根目录执行此脚本"
        exit 1
    fi

    # 检查SSH密钥认证
    log_info "检查SSH密钥认证..."
    if ! ssh -o BatchMode=yes -o ConnectTimeout=5 -p $REMOTE_PORT ${REMOTE_USER}@${REMOTE_HOST} exit 2>/dev/null; then
        log_error "SSH密钥认证失败，请确保："
        log_error "1. 已将本地公钥添加到远程服务器的 ~/.ssh/authorized_keys"
        log_error "2. 远程服务器SSH服务正常运行"
        log_error "3. 网络连接正常"
        exit 1
    fi
    log_info "SSH密钥认证成功"
}

# SSH连接辅助函数
get_ssh_cmd() {
    echo "ssh -o StrictHostKeyChecking=no -p $REMOTE_PORT"
}

# 清理并构建项目
build_project() {
    log_info "开始清理并构建项目..."
    
    # 清理之前的构建
    log_info "执行清理任务: ./gradlew clean"
    ./gradlew clean
    
    # 构建Spring Boot可执行jar
    log_info "构建Spring Boot可执行jar: ./gradlew bootJar"
    ./gradlew bootJar
    
    # 检查构建产物是否存在
    if [[ ! -f "build/libs/${JAR_NAME}" ]]; then
        log_error "构建失败，jar文件不存在: build/libs/${JAR_NAME}"
        log_info "正在检查build/libs目录中的文件..."
        if [[ -d "build/libs/" ]]; then
            ls -la build/libs/
        else
            log_warn "build/libs目录不存在"
        fi
        exit 1
    fi
    
    # 显示构建产物信息
    local jar_size=$(du -h "build/libs/${JAR_NAME}" | cut -f1)
    log_info "Spring Boot jar构建成功!"
    log_info "  文件: build/libs/${JAR_NAME}"
    log_info "  大小: ${jar_size}"
    log_info "  修改时间: $(stat -f "%Sm" "build/libs/${JAR_NAME}" 2>/dev/null || stat -c "%y" "build/libs/${JAR_NAME}" 2>/dev/null)"
}

# 使用rsync同步文件到远程服务器
sync_to_remote() {
    log_info "使用rsync同步构建产物到远程服务器..."

    local SSH_CMD=$(get_ssh_cmd)

    log_info "开始创建远程目录..."
    # 创建远程目录（如果不存在）
    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "mkdir -p ${REMOTE_PATH}"

    log_info "开始同步jar文件..."
    # 使用rsync同步jar文件，显示进度
    rsync -avz --progress \
        -e "ssh -o StrictHostKeyChecking=no -p ${REMOTE_PORT}" \
        "build/libs/${JAR_NAME}" \
        ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/

    if [[ $? -eq 0 ]]; then
        log_info "文件同步完成"
    else
        log_error "文件同步失败"
        exit 1
    fi
}

# 备份当前运行的jar文件
backup_remote() {
    if [[ "$BACKUP_ENABLED" == "true" ]]; then
        log_info "备份当前运行的jar文件..."
        
        local SSH_CMD=$(get_ssh_cmd)
        
        $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
            cd ${REMOTE_PATH}
            
            # 创建备份目录
            mkdir -p backup
            
            # 备份当前运行的jar（如果存在）
            if [[ -f ${JAR_NAME} ]]; then
                TIMESTAMP=\$(date +%Y%m%d_%H%M%S)
                cp ${JAR_NAME} backup/${JAR_NAME}.\${TIMESTAMP}
                echo \"已备份当前jar文件为: backup/${JAR_NAME}.\${TIMESTAMP}\"
                
                # 只保留最近的备份文件
                cd backup
                ls -t ${JAR_NAME}.* 2>/dev/null | tail -n +$((${BACKUP_COUNT} + 1)) | xargs rm -f
            fi
        "
        
        log_info "备份完成"
    fi
}

# 清理远程服务器旧文件
cleanup_remote() {
    log_info "清理远程服务器旧的构建产物..."
    
    local SSH_CMD=$(get_ssh_cmd)
    
    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_PATH}
        
        echo \"正在清理旧文件...\"
        
        # 清理除了当前版本以外的旧jar文件
        echo \"清理旧jar文件（保留当前版本: ${JAR_NAME}）\"
        for jar_file in *.jar; do
            if [[ \"\$jar_file\" != \"${JAR_NAME}\" ]] && [[ \"\$jar_file\" != \"*.jar\" ]]; then
                echo \"删除旧jar文件: \$jar_file\"
                rm -f \"\$jar_file\"
            fi
        done
        
        # 清理旧日志文件（保留最近N天的日志）
        echo \"清理过期日志文件（保留${LOG_RETENTION_DAYS}天）\"
        find . -name '*.log' -mtime +${LOG_RETENTION_DAYS} -delete 2>/dev/null || true
        
        # 清理临时文件
        echo \"清理临时文件\"
        find . -name '*.tmp' -delete 2>/dev/null || true
        find . -name 'nohup.out' -delete 2>/dev/null || true
        find . -name 'hs_err_pid*.log' -delete 2>/dev/null || true
        
        # 清理空目录（保留主要目录）
        find . -type d -empty ! -name '.' ! -name 'logs' ! -name 'backup' -delete 2>/dev/null || true
        
        echo \"清理完成。当前目录内容:\"
        ls -la
    "
    
    log_info "远程清理完成"
}

# 停止远程Java服务
stop_remote_service() {
    log_info "停止远程Java服务..."
    
    local SSH_CMD=$(get_ssh_cmd)
    
    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
        # 优先使用PID文件停止服务
        if [[ -f app.pid ]]; then
            OLD_PID=\$(cat app.pid)
            echo \"从PID文件读取到进程ID: \$OLD_PID\"

            if kill -0 \$OLD_PID 2>/dev/null; then
                echo \"正在停止进程 PID: \$OLD_PID\"
                kill \$OLD_PID

                # 等待进程优雅关闭
                echo \"等待进程优雅关闭...\"
                sleep 8

                # 检查进程是否还在运行
                if kill -0 \$OLD_PID 2>/dev/null; then
                    echo \"强制停止进程 \$OLD_PID\"
                    kill -9 \$OLD_PID 2>/dev/null || true
                    sleep 2
                fi

                # 删除PID文件
                rm -f app.pid
                echo \"✓ Java服务已停止\"
            else
                echo \"PID文件中的进程已不存在，删除PID文件\"
                rm -f app.pid
            fi
        fi

        # 查找并停止其他可能的Java进程
        JAVA_PIDS=\$(ps aux | grep 'java.*${JAR_NAME}' | grep -v grep | awk '{print \$2}')
        if [[ -n \"\$JAVA_PIDS\" ]]; then
            echo \"发现其他Java进程: \$JAVA_PIDS\"

            for PID in \$JAVA_PIDS; do
                echo \"停止进程 PID: \$PID\"
                kill -9 \$PID 2>/dev/null || true
            done

            echo \"✓ 所有Java进程已停止\"
        else
            echo \"未找到其他运行中的Java服务\"
        fi
    "
    
    log_info "Java服务停止完成"
}

# 启动远程Java服务
start_remote_service() {
    log_info "启动远程Java服务..."
    
    local SSH_CMD=$(get_ssh_cmd)
    
    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_PATH}
        
        # 创建日志目录
        mkdir -p logs
        
        # 简化启动命令，与您成功的命令保持一致
        echo \"启动命令: nohup java -jar ${JAR_NAME} > logs/app.log 2>&1 &\"

        # 后台启动Java服务（使用简化的成功命令）
        nohup java -jar ${JAR_NAME} > logs/app.log 2>&1 &

        JAVA_PID=\$!
        echo \"Java服务已启动，PID: \$JAVA_PID\"

        # 将PID写入文件，方便后续管理
        echo \$JAVA_PID > app.pid
        
        # 等待应用启动
        echo \"等待应用启动...\"
        sleep 5
        
        # 等待服务启动并检查状态
        echo \"等待Java服务完全启动...\"
        sleep 15

        # 多重检查服务是否正常启动
        JAVA_RUNNING=false

        # 1. 检查Java进程是否存在
        if ps aux | grep -v grep | grep \"java.*${JAR_NAME}\" > /dev/null; then
            echo \"✓ Java进程已启动\"
            JAVA_RUNNING=true
        else
            echo \"✗ Java进程未找到\"
        fi

        # 2. 检查端口是否被监听
        if netstat -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null || ss -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null; then
            echo \"✓ 端口 ${SERVER_PORT} 正在监听\"
        else
            echo \"✗ 端口 ${SERVER_PORT} 未被监听\"
            JAVA_RUNNING=false
        fi

        # 3. 检查日志文件
        echo \"检查应用日志...\"
        if [[ -f logs/app.log ]]; then
            echo \"日志文件存在，检查启动状态...\"

            # 显示最新的日志内容
            echo \"最新日志内容（最后10行）:\"
            tail -10 logs/app.log

            # 检查Spring Boot启动成功标志
            if grep -i \"started.*application.*in\" logs/app.log > /dev/null; then
                echo \"✓ 发现Spring Boot应用启动成功标志\"
            elif grep -i \"tomcat.*started.*on.*port\" logs/app.log > /dev/null; then
                echo \"✓ 发现Tomcat服务器启动成功标志\"
            elif grep -i \"started.*in.*seconds\" logs/app.log > /dev/null; then
                echo \"✓ 发现应用启动完成标志\"
            else
                echo \"⚠ 未找到明确的启动成功标志，但进程可能正在启动中\"
            fi

            # 检查是否有严重错误
            if grep -i \"error.*failed to start\" logs/app.log > /dev/null; then
                echo \"✗ 发现应用启动失败错误\"
                JAVA_RUNNING=false
            elif grep -i \"exception.*main\" logs/app.log > /dev/null; then
                echo \"✗ 发现主程序异常\"
                JAVA_RUNNING=false
            fi
        else
            echo \"⚠ 日志文件不存在，可能还在启动中\"
        fi

        # 最终状态报告
        if [[ \"\$JAVA_RUNNING\" == \"true\" ]]; then
            echo \"\"
            echo \"🎉 Java服务启动成功！\"
            echo \"   服务端口: ${SERVER_PORT}\"
            echo \"   日志文件: ${REMOTE_PATH}/logs/app.log\"
            echo \"   进程信息: \$(ps aux | grep -v grep | grep 'java.*${JAR_NAME}' | awk '{print \"PID:\" \$2 \" CPU:\" \$3 \"% MEM:\" \$4 \"%\"}')\"
        else
            echo \"\"
            echo \"❌ Java服务启动失败或异常\"
            echo \"最近的日志内容:\"
            tail -30 logs/app.log
            echo \"\"
            echo \"请检查以上日志信息排查问题\"
        fi
    "
    
    log_info "Java服务启动完成"
}

# 验证服务状态
verify_service_status() {
    log_info "验证远程服务状态..."

    local SSH_CMD=$(get_ssh_cmd)

    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_PATH}

        echo \"=== 服务状态检查 ===\"

        # 初始化检查结果
        CHECK_RESULT=0

        # 检查Java进程
        JAVA_PROCESS=\$(ps aux | grep 'java.*${JAR_NAME}' | grep -v grep)
        if [[ -n \"\$JAVA_PROCESS\" ]]; then
            echo \"✓ Java进程运行中:\"
            echo \"  \$JAVA_PROCESS\"
        else
            echo \"✗ Java进程未运行\"
            CHECK_RESULT=1
        fi

        # 检查端口监听
        if netstat -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null || ss -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null; then
            echo \"✓ 端口 ${SERVER_PORT} 正在监听\"
        else
            echo \"✗ 端口 ${SERVER_PORT} 未监听\"
            CHECK_RESULT=1
        fi

        # 检查日志文件
        if [[ -f logs/app.log ]]; then
            echo \"✓ 日志文件存在\"
            echo \"最新日志 (最后10行):\"
            tail -10 logs/app.log
        else
            echo \"✗ 日志文件不存在\"
        fi

        echo \"\"
        echo \"=== 系统资源使用情况 ===\"
        echo \"内存使用:\"
        free -h | head -2
        echo \"磁盘使用:\"
        df -h ${REMOTE_PATH}

        # 退出时使用检查结果
        exit \$CHECK_RESULT
    "

    if [[ $? -eq 0 ]]; then
        log_info "服务状态验证通过"
        return 0
    else
        log_error "服务状态验证失败"
        return 1
    fi
}

# 检查配置文件安全性
check_config_security() {
    if [[ -f "$CONFIG_FILE" ]]; then
        # 检查文件权限
        local file_perms=$(stat -f "%A" "$CONFIG_FILE" 2>/dev/null || stat -c "%a" "$CONFIG_FILE" 2>/dev/null)
        if [[ "$file_perms" != "600" ]]; then
            log_warn "配置文件权限不安全 (${file_perms})"
            log_warn "建议设置权限为600: chmod 600 $CONFIG_FILE"
            read -p "是否继续？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
}

# 主函数
main() {
    # 首先加载配置
    load_config
    
    # 检查配置安全性
    check_config_security
    
    log_info "开始部署流程..."
    log_info "目标服务器: ${REMOTE_USER}@${REMOTE_HOST}"
    log_info "部署路径: ${REMOTE_PATH}"
    log_info "JAR文件: ${JAR_NAME}"
    
    # 检查配置
    if [[ "$REMOTE_USER" == "your_username" ]] || [[ "$REMOTE_HOST" == "your_server_ip" ]]; then
        log_error "请先配置 deploy.config 文件或使用命令行参数指定 REMOTE_USER 和 REMOTE_HOST"
        log_error "示例: ./deploy.sh -u username -s server_ip"
        exit 1
    fi
    
    check_dependencies
    build_project
#    backup_remote
    sync_to_remote
    cleanup_remote
    stop_remote_service
    start_remote_service

    # 部署完成提示
    log_info "🎉 部署流程完成！"
    log_info ""
    log_info "服务信息："
    log_info "  目标服务器: ${REMOTE_USER}@${REMOTE_HOST}"
    log_info "  部署路径: ${REMOTE_PATH}"
    log_info "  服务端口: ${SERVER_PORT}"
    log_info "  JAR文件: ${JAR_NAME}"
    log_info ""
    log_info "监控和检查命令："
    log_info "  查看日志: ssh ${REMOTE_USER}@${REMOTE_HOST} 'tail -f ${REMOTE_PATH}/logs/app.log'"
    log_info "  检查状态: ./check_service.sh"
    log_info "  访问服务: http://${REMOTE_HOST}:${SERVER_PORT}"
    log_info ""
    log_info "如需验证服务状态，请运行: ./check_service.sh"

    # 可选：快速检查服务是否启动
    log_info ""
    read -p "是否现在快速检查服务状态？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "正在快速检查服务状态..."
        ./check_service.sh
    fi
}

# 脚本使用说明
usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help       显示帮助信息"
    echo "  -u, --user       指定远程用户名"
    echo "  -s, --server     指定远程服务器IP"
    echo ""
    echo "注意: 此脚本使用SSH密钥认证，请确保已配置SSH公钥到远程服务器"
    echo ""
    echo "示例:"
    echo "  $0 -u ubuntu -s *************"
    echo "  $0 --user=root --server=example.com"
    echo "  $0  # 使用deploy.config配置文件"
}

# 命令行参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -u|--user)
            REMOTE_USER="$2"
            shift 2
            ;;
        -s|--server)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --user=*)
            REMOTE_USER="${1#*=}"
            shift
            ;;
        --server=*)
            REMOTE_HOST="${1#*=}"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            usage
            exit 1
            ;;
    esac
done

# 执行主函数
main
