=== 详细调试分割逻辑 ===
测试时间: <PERSON><PERSON> Jun 24 10:37:11 CST 2025

文档总字数: 137
段落总数: 29

=== 段落详细分析 ===
段落 1: 样式=a3, 字数=9, H1=否, 内容=这是这篇文章的标题
段落 2: 样式=1, 字数=11, H1=是, 内容=这是我的第一个一级标题
段落 3: 样式=Normal, 字数=12, H1=否, 内容=这是我的一级标题下的内容
段落 4: 样式=Normal, 字数=0, H1=否, 内容=
段落 5: 样式=Normal, 字数=0, H1=否, 内容=
段落 6: 样式=2, 字数=8, H1=否, 内容=这是我的二级标题
段落 7: 样式=Normal, 字数=6, H1=否, 内容=二级标题内容
段落 8: 样式=Normal, 字数=0, H1=否, 内容=
段落 9: 样式=Normal, 字数=0, H1=否, 内容=
段落 10: 样式=3, 字数=8, H1=否, 内容=这是我的三级标题
段落 11: 样式=Normal, 字数=0, H1=否, 内容=
段落 12: 样式=Normal, 字数=6, H1=否, 内容=三级标题内容
段落 13: 样式=Normal, 字数=0, H1=否, 内容=
段落 14: 样式=4, 字数=8, H1=否, 内容=这是我的四级标题
段落 15: 样式=Normal, 字数=0, H1=否, 内容=
段落 16: 样式=Normal, 字数=6, H1=否, 内容=四级标题内容
段落 17: 样式=Normal, 字数=0, H1=否, 内容=
段落 18: 样式=Normal, 字数=0, H1=否, 内容=
段落 19: 样式=Normal, 字数=0, H1=否, 内容=
段落 20: 样式=1, 字数=11, H1=是, 内容=这是我的第二个一级标题
段落 21: 样式=Normal, 字数=12, H1=否, 内容=这是我的一级标题下的内容
段落 22: 样式=Normal, 字数=0, H1=否, 内容=
段落 23: 样式=Normal, 字数=0, H1=否, 内容=
段落 24: 样式=2, 字数=11, H1=否, 内容=这是我的第二个二级标题
段落 25: 样式=Normal, 字数=6, H1=否, 内容=二级标题内容
段落 26: 样式=Normal, 字数=0, H1=否, 内容=
段落 27: 样式=1, 字数=11, H1=是, 内容=这是我的第三个一级标题
段落 28: 样式=Normal, 字数=12, H1=否, 内容=这是我的一级标题下的内容
段落 29: 样式=Normal, 字数=0, H1=否, 内容=

=== 模拟分割逻辑（字数限制: 50）===
❌ 总字数 > 限制，需要分割

按Heading1分割过程:
  段落 1: 跳过（chapterIndex = 0）
  段落 2: 发现Heading1，章节索引 = 1
  段落 2: 添加到章节 1
  段落 3: 添加到章节 1
  段落 4: 添加到章节 1
  段落 5: 添加到章节 1
  段落 6: 添加到章节 1
  段落 7: 添加到章节 1
  段落 8: 添加到章节 1
  段落 9: 添加到章节 1
  段落 10: 添加到章节 1
  段落 11: 添加到章节 1
  段落 12: 添加到章节 1
  段落 13: 添加到章节 1
  段落 14: 添加到章节 1
  段落 15: 添加到章节 1
  段落 16: 添加到章节 1
  段落 17: 添加到章节 1
  段落 18: 添加到章节 1
  段落 19: 添加到章节 1
  段落 20: 发现Heading1，章节索引 = 2
  段落 20: 添加到章节 2
  段落 21: 添加到章节 2
  段落 22: 添加到章节 2
  段落 23: 添加到章节 2
  段落 24: 添加到章节 2
  段落 25: 添加到章节 2
  段落 26: 添加到章节 2
  段落 27: 发现Heading1，章节索引 = 3
  段落 27: 添加到章节 3
  段落 28: 添加到章节 3
  段落 29: 添加到章节 3

章节分割结果: 3个章节

=== 章节分析 ===
章节 1:
  段落数: 18
  字数: 65
  是否超过限制: 是
  内容概要:
    - [1] 这是我的第一个一级标题
    - [Normal] 这是我的一级标题下的内容
    - [Normal] 
    ... 还有 15 个段落

章节 2:
  段落数: 7
  字数: 40
  是否超过限制: 否
  内容概要:
    - [1] 这是我的第二个一级标题
    - [Normal] 这是我的一级标题下的内容
    - [Normal] 
    ... 还有 4 个段落

章节 3:
  段落数: 3
  字数: 23
  是否超过限制: 否
  内容概要:
    - [1] 这是我的第三个一级标题
    - [Normal] 这是我的一级标题下的内容
    - [Normal] 

=== 实际分割测试 ===
实际生成文件数: 1
  - multi-level-headings_complete.docx (15033 字节)

=== 问题诊断 ===
❌ 问题确认: 应该分割但输出了完整文档
可能原因:
1. 段落获取有问题
2. 章节分割逻辑有bug
3. 字数计算不一致

============================================================
