=== 标题检测测试结果 ===
文档路径: src/test/resources/multi-level-headings.docx
测试时间: <PERSON><PERSON> 24 10:18:19 CST 2025

✅ 文档加载成功
📄 文档元素总数: 31

段落 1: 样式=a3, 文本=这是这篇文章的标题
段落 2: 样式=1, 文本=这是我的第一个一级标题
  ✅ 检测为 Heading1
段落 3: 样式=Normal, 文本=这是我的一级标题下的内容
段落 4: 样式=Normal, 文本=[空]
段落 5: 样式=Normal, 文本=[空]
段落 6: 样式=2, 文本=这是我的二级标题
  ✅ 检测为 Heading2
段落 7: 样式=Normal, 文本=二级标题内容
段落 8: 样式=Normal, 文本=[空]
段落 9: 样式=Normal, 文本=[空]
段落 10: 样式=3, 文本=这是我的三级标题
  ✅ 检测为 Heading3
段落 11: 样式=Normal, 文本=[空]
段落 12: 样式=Normal, 文本=三级标题内容
段落 13: 样式=Normal, 文本=[空]
段落 14: 样式=4, 文本=这是我的四级标题
  ✅ 检测为 Heading4
段落 15: 样式=Normal, 文本=[空]
段落 16: 样式=Normal, 文本=四级标题内容
段落 17: 样式=Normal, 文本=[空]
段落 18: 样式=Normal, 文本=[空]
段落 19: 样式=Normal, 文本=[空]
段落 20: 样式=1, 文本=这是我的第二个一级标题
  ✅ 检测为 Heading1
段落 21: 样式=Normal, 文本=这是我的一级标题下的内容
段落 22: 样式=Normal, 文本=[空]
段落 23: 样式=Normal, 文本=[空]
段落 24: 样式=2, 文本=这是我的第二个二级标题
  ✅ 检测为 Heading2
段落 25: 样式=Normal, 文本=二级标题内容
段落 26: 样式=Normal, 文本=[空]
段落 27: 样式=1, 文本=这是我的第三个一级标题
  ✅ 检测为 Heading1
段落 28: 样式=Normal, 文本=这是我的一级标题下的内容
段落 29: 样式=Normal, 文本=[空]

📊 标题检测统计:
----------------------------------------
✅ Heading1: 3个 (示例: 这是我的第一个一级标题)
✅ Heading2: 2个 (示例: 这是我的二级标题)
✅ Heading3: 1个 (示例: 这是我的三级标题)
✅ Heading4: 1个 (示例: 这是我的四级标题)
❌ Heading5: 未找到
❌ Heading6: 未找到

🎉 成功！现在可以正确识别你文档中的标题了！

🔧 测试分割功能...
✅ 分割成功！生成了 3 个文件:
  - chapter_01_chapter_1.docx
  - chapter_02_chapter_2.docx
  - chapter_03_chapter_3.docx

============================================================
