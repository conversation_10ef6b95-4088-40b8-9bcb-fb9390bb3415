#!/bin/bash

# 配置文件初始化脚本
# 用于安全地创建和配置部署配置文件

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/deploy.config"
EXAMPLE_FILE="${SCRIPT_DIR}/deploy.config.example"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否已存在配置文件
if [[ -f "$CONFIG_FILE" ]]; then
    log_warn "配置文件已存在: $CONFIG_FILE"
    read -p "是否要覆盖现有配置文件？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "保持现有配置文件不变"
        exit 0
    fi
fi

# 复制示例配置文件
if [[ -f "$EXAMPLE_FILE" ]]; then
    cp "$EXAMPLE_FILE" "$CONFIG_FILE"
    log_info "已创建配置文件: $CONFIG_FILE"
else
    log_error "示例配置文件不存在: $EXAMPLE_FILE"
    exit 1
fi

# 设置安全权限
chmod 600 "$CONFIG_FILE"
log_info "已设置配置文件权限为600（仅所有者可读写）"

# 提示用户编辑配置
log_info ""
log_info "🎉 配置文件创建成功！"
log_info ""
log_info "接下来请编辑配置文件并设置你的服务器信息："
log_info "  nano $CONFIG_FILE"
log_info "或者:"
log_info "  vim $CONFIG_FILE"
log_info ""
log_info "需要修改的主要配置项："
log_info "  - REMOTE_USER: 远程服务器用户名"
log_info "  - REMOTE_HOST: 远程服务器IP或域名"
log_info "  - REMOTE_PASSWORD: 远程服务器密码（或配置SSH密钥）"
log_info ""
log_info "配置完成后运行部署："
log_info "  ./deploy.sh"
log_info ""
log_warn "⚠️  安全提示："
log_warn "  1. 生产环境建议使用SSH密钥而不是密码"
log_warn "  2. 不要将包含密码的配置文件提交到版本控制"
log_warn "  3. 定期更新服务器密码和访问权限"

# 询问是否立即编辑配置文件
echo
read -p "是否现在就编辑配置文件？(Y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    log_info "稍后记得编辑配置文件哦！"
else
    # 检查可用的编辑器
    if command -v nano &> /dev/null; then
        nano "$CONFIG_FILE"
    elif command -v vim &> /dev/null; then
        vim "$CONFIG_FILE"
    elif command -v vi &> /dev/null; then
        vi "$CONFIG_FILE"
    else
        log_warn "未找到可用的文本编辑器，请手动编辑配置文件"
    fi
fi
