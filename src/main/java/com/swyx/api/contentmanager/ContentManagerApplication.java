package com.swyx.api.contentmanager;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScan({"com.swyx.api"})
@MapperScan({"com.swyx.api.contentmanager.dao.mapper"})
@EnableAsync
public class ContentManagerApplication {

    public static void main(String[] args) {
        SpringApplication.run(ContentManagerApplication.class, args);
    }

}
