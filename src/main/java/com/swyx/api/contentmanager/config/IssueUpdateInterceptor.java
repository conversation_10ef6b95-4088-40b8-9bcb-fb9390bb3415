package com.swyx.api.contentmanager.config;

import com.swyx.api.contentmanager.dao.AIIssueEntity;
import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.TaskEntityExample;
import com.swyx.api.contentmanager.dao.mapper.AIIssueEntityMapper;
import com.swyx.api.contentmanager.dao.mapper.TaskEntityMapper;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import org.apache.ibatis.executor.Executor;

//@Intercepts({
//        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
//})
//@Component
//public class IssueUpdateInterceptor implements Interceptor {
//    @Autowired
//    private TaskEntityMapper taskEntityMapper;
//
//    @Autowired
//    private AIIssueEntityMapper aiIssueEntityMapper;
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        // 获取方法参数
//        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
//        Object parameter = invocation.getArgs()[1];
//        String mappedStatementId = mappedStatement.getId();
//
//        if (!mappedStatementId.endsWith("AIIssueEntityMapper.updateByExample") && !mappedStatementId.endsWith("AIIssueEntityMapper.updateByPrimaryKey")) {
//            return invocation.proceed();
//        }
//
//        AIIssueEntity newIssue = null;
//        if (parameter instanceof Map) {
//            newIssue = (AIIssueEntity) ((Map<?, ?>) parameter).get("row");
//        } else if (parameter instanceof AIIssueEntity) {
//            newIssue = (AIIssueEntity) parameter;
//        }
//        AIIssueEntity oldIssue = aiIssueEntityMapper.selectByPrimaryKey(newIssue.getId());
//
//        // 判断状态变化规则
//        if (needUpdateProgress(oldIssue.getStatus().intValue(), newIssue.getStatus().intValue())) {
//
//            TaskEntityExample example = new TaskEntityExample();
//            TaskEntityExample.Criteria criteria = example.createCriteria();
//            criteria.andTaskIdEqualTo(newIssue.getTaskId());
//            int change = calculateChange(oldIssue.getStatus().intValue(), newIssue.getStatus().intValue());
//
//            TaskEntity taskEntity = taskEntityMapper.selectByExample(example).getFirst();
//            Integer currentCompleteCount = taskEntity.getCompleteIssueCount();
//            taskEntity.setCompleteIssueCount(currentCompleteCount + change);
////            taskEntityMapper.updateByPrimaryKey(taskEntity);
//
//            taskEntityMapper.updateByExample(taskEntity, example);
//        }
//
//        return  invocation.proceed();
//    }
//
//    // 状态变化规则
//    private boolean needUpdateProgress(Integer oldStatus, Integer newStatus) {
//        return (oldStatus == 0 && newStatus != 0) || (oldStatus != 0 && newStatus == 0);
//    }
//
//    private int calculateChange(Integer oldStatus, Integer newStatus) {
//        if (oldStatus == 0 && newStatus != 0) return 1; // 0→1/2：完成数+1
//        if (oldStatus != 0 && newStatus == 0) return -1; // 1/2→0：完成数-1
//        return 0;
//    }
//}
