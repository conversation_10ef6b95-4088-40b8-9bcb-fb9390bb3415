package com.swyx.api.contentmanager.config;

import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

//@Configuration
//public class MyBatisConfig {
//    @Bean
//    public ConfigurationCustomizer configurationCustomizer(IssueUpdateInterceptor interceptor) {
//        return configuration -> configuration.addInterceptor(interceptor);
//    }
//}
