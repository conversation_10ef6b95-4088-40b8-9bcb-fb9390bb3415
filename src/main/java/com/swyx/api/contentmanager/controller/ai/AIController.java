package com.swyx.api.contentmanager.controller.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.swyx.api.contentmanager.dao.AIModelEntity;
import com.swyx.api.contentmanager.dao.AIPromptEntity;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.service.ai.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("ai")
public class AIController {
    @Autowired
    private AIService aiService;

    @GetMapping("model/list")
    public Response<List<AIModelEntity>> getModels() {
        return Response.ofData(aiService.getModels());
    }

    @PostMapping("model/modify")
    public Response<String> modify(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String modelId = object.getString("modelId");
        String token = object.getString("token");
        String apiurl = object.getString("apiurl");
        Boolean success = aiService.modifyModel(modelId, token, apiurl);
        if (!success) {
            return Response.ofData("failed");
        }
        return Response.ofData("success");
    }

    @GetMapping("prompt/list")
    public Response<List<AIPromptEntity>> getPrompts() {
        return Response.ofData(aiService.getPrompts());
    }

    @PostMapping("prompt/save")
    public Response<String> savePrompt(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String prompt = object.getString("prompt");
        String type = object.getString("type");
        aiService.savePrompt(prompt, type);
        return Response.ofData("保存成功");
    }

    @PostMapping("prompt/modify")
    public Response<String> modifyPrompt(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String prompt = object.getString("prompt");
        String type = object.getString("type");
        String id = object.getString("promptId");
        Boolean success = aiService.modifyPrompt(id, prompt, type);
        if (!success) {
            return Response.ofData("修改失败，找不到对应的提示词");
        }
        return Response.ofData("修改成功");
    }

    @PostMapping("prompt/delete")
    public Response<String> deletePrompt(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String id = object.getString("promptId");
        aiService.deletePrompt(id);
        return Response.ofData("删除成功");
    }

    @PostMapping("issue/update")
    public Response<String> issueUpdate(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String issueId = object.getString("issueId");
        Integer status = object.getInteger("status");
        aiService.updateIssue(issueId, status);
        return  Response.ofData("保存成功");
    }
}
