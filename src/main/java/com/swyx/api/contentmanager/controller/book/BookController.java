package com.swyx.api.contentmanager.controller.book;

import com.swyx.api.contentmanager.dao.BookEntity;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.SaveBookResponse;
import com.swyx.api.contentmanager.service.book.BookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/book")
public class BookController {
    @Autowired
    private BookService bookService;

    @Value("${file.upload-dir}") // 从配置读取存储路径
    private String uploadDir;

    @PostMapping("/saveBook")
    public Response<SaveBookResponse> saveBook(@RequestParam MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.error("上传的文件为空");
            return Response.fail();
        }
        
        String originalFilename = file.getOriginalFilename();
        log.info("接收到文件上传请求，文件名: {}, 文件大小: {} bytes", originalFilename, file.getSize());
        
        try {
            SaveBookResponse book = bookService.saveBook(file);
            if (book == null) {
                return Response.fail();
            }
            return Response.ofData(book);
        } catch (Exception e) {
            log.error("保存书籍失败: {}", e.getMessage(), e);
            return Response.fail();
        }
    }
}
