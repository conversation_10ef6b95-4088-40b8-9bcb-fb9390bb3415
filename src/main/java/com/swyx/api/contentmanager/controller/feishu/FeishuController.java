package com.swyx.api.contentmanager.controller.feishu;

import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.UserInfoResponse;
import com.swyx.api.contentmanager.service.feishu.FeishuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/feishu")
public class FeishuController {

    @Autowired
    private FeishuService feishuService;

    @PostMapping("login")
    public Response<UserInfoResponse> login(@RequestBody String params) {
        String tempCode = JSONObject.parseObject(params).getString("tempCode");
        UserInfoResponse userInfoResponse = feishuService.login(tempCode);
        return Response.ofData(userInfoResponse);
    }
}
