package com.swyx.api.contentmanager.controller.file;

import com.swyx.api.contentmanager.dao.AIModelEntity;
import com.swyx.api.contentmanager.dto.FileResponse;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.SaveBookResponse;
import com.swyx.api.contentmanager.service.ai.OpenAIService;
import com.swyx.api.contentmanager.utils.FileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("file")
public class FileController {

    @Autowired
    private OpenAIService openAIService;

    @PostMapping("/upload")
    public Response<FileResponse> upload(@RequestParam MultipartFile file) {
        FileResponse response = new FileResponse();
        String fileUrl = FileUtil.uploadFile(file);
        response.setFileUrl(fileUrl);
        return Response.ofData(response);
    }

    @PostMapping("batchUpload")
    public Response<List<FileResponse>> batchUpload(@RequestParam("files") MultipartFile[] files) {
        List<String> urls = FileUtil.uploadFiles(files);
        List<FileResponse> filesRes = urls.stream().map(FileResponse::new).collect(Collectors.toList());
        return Response.ofData(filesRes);
    }


    @PostMapping("/uploadToAliyun")
    public Response<FileResponse> uploadToAliyun(@RequestParam MultipartFile file) throws IOException {
        String fileId = openAIService.uploadFile(file);
        FileResponse response = new FileResponse();
        response.setFileId(fileId);
        return Response.ofData(response);
    }
}
