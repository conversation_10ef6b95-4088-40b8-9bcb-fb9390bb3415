package com.swyx.api.contentmanager.controller.project;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.ProjectEntity;
import com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs;
import com.swyx.api.contentmanager.dto.HomeInfoResponse;
import com.swyx.api.contentmanager.dto.ProjectDetailResponse;
import com.swyx.api.contentmanager.dto.ProjectListResponse;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.service.project.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("project")
public class ProjectController {
    @Autowired
    private ProjectService projectService;

    @GetMapping("list")
    public Response<List<ProjectListResponse>> getProjectList() {
        return Response.ofData(projectService.getProjectList());
    }

    @GetMapping("detail")
    public Response<ProjectDetailResponse> getProjectDetail(@RequestParam("id") String id) {
        ProjectDetailResponse res = projectService.getProjectById(id);
        return Response.ofData(res);
    }

    @PostMapping("saveProject")
    public Response<String> saveProject(@RequestBody String  params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONArray tasks = jsonObject.getJSONArray("tasks");

        List<TaskEntityWithBLOBs> taskEntities = tasks.stream()
                .map(task -> (JSONObject) task) // 确保元素是JSONObject
                .map(taskObj -> JSON.toJavaObject(taskObj, TaskEntityWithBLOBs.class))
                .toList();

        jsonObject.remove("tasks");
        ProjectEntity projectEntity = JSON.toJavaObject(jsonObject, ProjectEntity.class);

        projectService.saveProject(projectEntity, taskEntities);
        return Response.ofData("项目创建成功");
    }

    @GetMapping("dashboard")
    public Response<HomeInfoResponse> getHomeInfo(@RequestAttribute("userId") String userId) {
        return Response.ofData(projectService.getHomeInfo(userId));
    }
}
