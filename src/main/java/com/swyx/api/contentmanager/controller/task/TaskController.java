package com.swyx.api.contentmanager.controller.task;

import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.TaskResponse;
import com.swyx.api.contentmanager.service.task.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/task")
public class TaskController {

    @Autowired
    private TaskService taskService;

    @GetMapping(value = {"/list"})
    public Response<List<TaskEntity>> getList() {
        return Response.ofData(taskService.getTasks());
    }

    @GetMapping(value = "/detail")
    public Response<TaskResponse> getTask(@RequestParam("id") String id) {
        return Response.ofData(taskService.getTaskById(id));
    }

    @RequestMapping(value = "/saveTask", method = RequestMethod.POST)
    public Response<TaskEntity> saveTask(@RequestBody String params) {
        TaskEntity taskEntity = JSONObject.parseObject(params, TaskEntityWithBLOBs.class);
        taskService.saveTask(taskEntity);
        return Response.ofData(taskEntity);
    }

    // 发起AI审核
    @PostMapping("/submitAIAudit")
    public Response<String> submitAIAudit(@RequestBody String params) {
        String res = taskService.taskAudit(params);
        return Response.ofData(res);
    }

    @PostMapping("/update/checkFile")
    public Response<String> update(@RequestBody String params) {
        JSONObject object = JSONObject.parseObject(params);
        String taskId = object.getString("taskId");
        String fileUrl = object.getString("fileUrl");
        taskService.updateTaskCheckFile(taskId, fileUrl);
        return Response.ofData("success");
    }
}


