package com.swyx.api.contentmanager.controller.user;

import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.UserEntity;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.UserInfo;
import com.swyx.api.contentmanager.dto.UserInfoResponse;
import com.swyx.api.contentmanager.dto.UserLoginRequest;
import com.swyx.api.contentmanager.service.feishu.FeishuService;
import com.swyx.api.contentmanager.service.task.TaskService;
import com.swyx.api.contentmanager.service.user.UserService;
import com.swyx.api.contentmanager.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("user")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private FeishuService feishuService;

    @PostMapping("login")
    public Response<UserInfoResponse> login(@RequestBody String params) {
        String tempCode = JSONObject.parseObject(params).getString("tempCode");
        UserInfoResponse userInfoResponse = feishuService.login(tempCode);

        // 返回系统自己的accessToken
        String token = JwtUtil.generateToken(Map.of("userId", userInfoResponse.getUserId(), "username", userInfoResponse.getName()), userInfoResponse.getUserId());
        userInfoResponse.setAccessToken(token);
        return Response.ofData(userInfoResponse);
    }

    @PostMapping("loginByPassword")
    public Response<UserInfoResponse> loginByPassword(@RequestBody UserLoginRequest request) {
        UserEntity user = userService.getUserByUsernameAndPassword(request.getUsername(), request.getPassword());
        if (user == null) {
            return Response.fail();
        }

        UserInfoResponse resp = userService.getUserRoleAndPermissonsByEntity(user);
        // 生成JWT
        String token = JwtUtil.generateToken(Map.of("userId", user.getUserId(), "username", user.getName()), user.getUserId());
        resp.setAccessToken(token);
        return Response.ofData(resp);
    }

    @GetMapping("list")
    public Response<List<UserInfoResponse>> list() {
        return Response.ofData(userService.getUsers());
    }
}
