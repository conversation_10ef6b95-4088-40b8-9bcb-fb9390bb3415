package com.swyx.api.contentmanager.dao;

public class AIIssueEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.issue_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String issueId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.project_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String projectId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.task_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.prompt_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String promptId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.model_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String modelId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.issue_type
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String issueType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.status
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.position
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String position;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.id
     *
     * @return the value of ai_issue.id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.id
     *
     * @param id the value for ai_issue.id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.issue_id
     *
     * @return the value of ai_issue.issue_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getIssueId() {
        return issueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.issue_id
     *
     * @param issueId the value for ai_issue.issue_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.project_id
     *
     * @return the value of ai_issue.project_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getProjectId() {
        return projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.project_id
     *
     * @param projectId the value for ai_issue.project_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.task_id
     *
     * @return the value of ai_issue.task_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.task_id
     *
     * @param taskId the value for ai_issue.task_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.prompt_id
     *
     * @return the value of ai_issue.prompt_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getPromptId() {
        return promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.prompt_id
     *
     * @param promptId the value for ai_issue.prompt_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.model_id
     *
     * @return the value of ai_issue.model_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getModelId() {
        return modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.model_id
     *
     * @param modelId the value for ai_issue.model_id
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.issue_type
     *
     * @return the value of ai_issue.issue_type
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getIssueType() {
        return issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.issue_type
     *
     * @param issueType the value for ai_issue.issue_type
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.status
     *
     * @return the value of ai_issue.status
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.status
     *
     * @param status the value for ai_issue.status
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.position
     *
     * @return the value of ai_issue.position
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getPosition() {
        return position;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.position
     *
     * @param position the value for ai_issue.position
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setPosition(String position) {
        this.position = position;
    }
}