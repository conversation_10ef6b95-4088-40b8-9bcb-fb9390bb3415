package com.swyx.api.contentmanager.dao;

public class AIIssueEntityWithBLOBs extends AIIssueEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.origin_text
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String originText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.short_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String shortSuggestion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_issue.full_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    private String fullSuggestion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.origin_text
     *
     * @return the value of ai_issue.origin_text
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getOriginText() {
        return originText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.origin_text
     *
     * @param originText the value for ai_issue.origin_text
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setOriginText(String originText) {
        this.originText = originText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.short_suggestion
     *
     * @return the value of ai_issue.short_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getShortSuggestion() {
        return shortSuggestion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.short_suggestion
     *
     * @param shortSuggestion the value for ai_issue.short_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setShortSuggestion(String shortSuggestion) {
        this.shortSuggestion = shortSuggestion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_issue.full_suggestion
     *
     * @return the value of ai_issue.full_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public String getFullSuggestion() {
        return fullSuggestion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_issue.full_suggestion
     *
     * @param fullSuggestion the value for ai_issue.full_suggestion
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    public void setFullSuggestion(String fullSuggestion) {
        this.fullSuggestion = fullSuggestion;
    }
}