package com.swyx.api.contentmanager.dao;

import java.util.Date;

public class AIModelEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.model_id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private String modelId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.name
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.token
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private String token;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.apiurl
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private String apiurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.orgnizer
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private String orgnizer;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.created_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_model.updated_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    private Date updatedTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.id
     *
     * @return the value of ai_model.id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.id
     *
     * @param id the value for ai_model.id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.model_id
     *
     * @return the value of ai_model.model_id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public String getModelId() {
        return modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.model_id
     *
     * @param modelId the value for ai_model.model_id
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.name
     *
     * @return the value of ai_model.name
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.name
     *
     * @param name the value for ai_model.name
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.token
     *
     * @return the value of ai_model.token
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public String getToken() {
        return token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.token
     *
     * @param token the value for ai_model.token
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.apiurl
     *
     * @return the value of ai_model.apiurl
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public String getApiurl() {
        return apiurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.apiurl
     *
     * @param apiurl the value for ai_model.apiurl
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setApiurl(String apiurl) {
        this.apiurl = apiurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.orgnizer
     *
     * @return the value of ai_model.orgnizer
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public String getOrgnizer() {
        return orgnizer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.orgnizer
     *
     * @param orgnizer the value for ai_model.orgnizer
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setOrgnizer(String orgnizer) {
        this.orgnizer = orgnizer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.created_time
     *
     * @return the value of ai_model.created_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.created_time
     *
     * @param createdTime the value for ai_model.created_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_model.updated_time
     *
     * @return the value of ai_model.updated_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_model.updated_time
     *
     * @param updatedTime the value for ai_model.updated_time
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
}