package com.swyx.api.contentmanager.dao;

public class AIPromptEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.prompt_id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private String promptId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.score
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private Float score;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.count
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private Integer count;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.type
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ai_prompt.text
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    private String text;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.id
     *
     * @return the value of ai_prompt.id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.id
     *
     * @param id the value for ai_prompt.id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.prompt_id
     *
     * @return the value of ai_prompt.prompt_id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public String getPromptId() {
        return promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.prompt_id
     *
     * @param promptId the value for ai_prompt.prompt_id
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.score
     *
     * @return the value of ai_prompt.score
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public Float getScore() {
        return score;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.score
     *
     * @param score the value for ai_prompt.score
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setScore(Float score) {
        this.score = score;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.count
     *
     * @return the value of ai_prompt.count
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public Integer getCount() {
        return count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.count
     *
     * @param count the value for ai_prompt.count
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setCount(Integer count) {
        this.count = count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.type
     *
     * @return the value of ai_prompt.type
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.type
     *
     * @param type the value for ai_prompt.type
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ai_prompt.text
     *
     * @return the value of ai_prompt.text
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public String getText() {
        return text;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ai_prompt.text
     *
     * @param text the value for ai_prompt.text
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    public void setText(String text) {
        this.text = text;
    }
}