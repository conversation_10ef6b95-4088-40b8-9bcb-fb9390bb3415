package com.swyx.api.contentmanager.dao;

public class BookEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.book_id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private String bookId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.title
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.author
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private String author;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.isbn
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private String isbn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.total_pages
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private Integer totalPages;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column book.downloadurl
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    private String downloadurl;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.id
     *
     * @return the value of book.id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.id
     *
     * @param id the value for book.id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.book_id
     *
     * @return the value of book.book_id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.book_id
     *
     * @param bookId the value for book.book_id
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.title
     *
     * @return the value of book.title
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.title
     *
     * @param title the value for book.title
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.author
     *
     * @return the value of book.author
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public String getAuthor() {
        return author;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.author
     *
     * @param author the value for book.author
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setAuthor(String author) {
        this.author = author;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.isbn
     *
     * @return the value of book.isbn
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public String getIsbn() {
        return isbn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.isbn
     *
     * @param isbn the value for book.isbn
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.total_pages
     *
     * @return the value of book.total_pages
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public Integer getTotalPages() {
        return totalPages;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.total_pages
     *
     * @param totalPages the value for book.total_pages
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column book.downloadurl
     *
     * @return the value of book.downloadurl
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public String getDownloadurl() {
        return downloadurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column book.downloadurl
     *
     * @param downloadurl the value for book.downloadurl
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    public void setDownloadurl(String downloadurl) {
        this.downloadurl = downloadurl;
    }
}