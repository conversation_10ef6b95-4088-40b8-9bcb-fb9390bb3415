package com.swyx.api.contentmanager.dao;

public class PageEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.page_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private String pageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.book_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private String bookId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.page_num
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private Integer pageNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.content_hash
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private String contentHash;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column page_content.content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.id
     *
     * @return the value of page_content.id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.id
     *
     * @param id the value for page_content.id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.page_id
     *
     * @return the value of page_content.page_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public String getPageId() {
        return pageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.page_id
     *
     * @param pageId the value for page_content.page_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.book_id
     *
     * @return the value of page_content.book_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.book_id
     *
     * @param bookId the value for page_content.book_id
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.page_num
     *
     * @return the value of page_content.page_num
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.page_num
     *
     * @param pageNum the value for page_content.page_num
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.content_hash
     *
     * @return the value of page_content.content_hash
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public String getContentHash() {
        return contentHash;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.content_hash
     *
     * @param contentHash the value for page_content.content_hash
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setContentHash(String contentHash) {
        this.contentHash = contentHash;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column page_content.content
     *
     * @return the value of page_content.content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column page_content.content
     *
     * @param content the value for page_content.content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    public void setContent(String content) {
        this.content = content;
    }
}