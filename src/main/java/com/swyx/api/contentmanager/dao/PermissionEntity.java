package com.swyx.api.contentmanager.dao;

public class PermissionEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column permission.id
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column permission.name
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column permission.api
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private String api;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column permission.description
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private String description;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column permission.id
     *
     * @return the value of permission.id
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column permission.id
     *
     * @param id the value for permission.id
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column permission.name
     *
     * @return the value of permission.name
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column permission.name
     *
     * @param name the value for permission.name
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column permission.api
     *
     * @return the value of permission.api
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public String getApi() {
        return api;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column permission.api
     *
     * @param api the value for permission.api
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setApi(String api) {
        this.api = api;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column permission.description
     *
     * @return the value of permission.description
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column permission.description
     *
     * @param description the value for permission.description
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setDescription(String description) {
        this.description = description;
    }
}