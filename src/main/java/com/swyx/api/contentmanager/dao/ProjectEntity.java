package com.swyx.api.contentmanager.dao;

import java.util.Date;

public class ProjectEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.project_id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private String projectId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.project_name
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private String projectName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.status
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.priority
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Byte priority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.task_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Integer taskCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.reviewer_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Integer reviewerCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.creator
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.created_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.updated_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Date updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.deadline
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private Date deadline;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column project.description
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    private String description;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.id
     *
     * @return the value of project.id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.id
     *
     * @param id the value for project.id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.project_id
     *
     * @return the value of project.project_id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public String getProjectId() {
        return projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.project_id
     *
     * @param projectId the value for project.project_id
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.project_name
     *
     * @return the value of project.project_name
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.project_name
     *
     * @param projectName the value for project.project_name
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.status
     *
     * @return the value of project.status
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.status
     *
     * @param status the value for project.status
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.priority
     *
     * @return the value of project.priority
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Byte getPriority() {
        return priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.priority
     *
     * @param priority the value for project.priority
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setPriority(Byte priority) {
        this.priority = priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.task_count
     *
     * @return the value of project.task_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Integer getTaskCount() {
        return taskCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.task_count
     *
     * @param taskCount the value for project.task_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setTaskCount(Integer taskCount) {
        this.taskCount = taskCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.reviewer_count
     *
     * @return the value of project.reviewer_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Integer getReviewerCount() {
        return reviewerCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.reviewer_count
     *
     * @param reviewerCount the value for project.reviewer_count
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setReviewerCount(Integer reviewerCount) {
        this.reviewerCount = reviewerCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.creator
     *
     * @return the value of project.creator
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.creator
     *
     * @param creator the value for project.creator
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.created_time
     *
     * @return the value of project.created_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.created_time
     *
     * @param createdTime the value for project.created_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.updated_time
     *
     * @return the value of project.updated_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.updated_time
     *
     * @param updatedTime the value for project.updated_time
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.deadline
     *
     * @return the value of project.deadline
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public Date getDeadline() {
        return deadline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.deadline
     *
     * @param deadline the value for project.deadline
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column project.description
     *
     * @return the value of project.description
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column project.description
     *
     * @param description the value for project.description
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    public void setDescription(String description) {
        this.description = description;
    }
}