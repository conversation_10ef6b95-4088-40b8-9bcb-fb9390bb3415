package com.swyx.api.contentmanager.dao;

public class RolePermissionEntityKey {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column role_permission.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private Integer roleid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column role_permission.permissionId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private Integer permissionid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column role_permission.roleId
     *
     * @return the value of role_permission.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public Integer getRoleid() {
        return roleid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column role_permission.roleId
     *
     * @param roleid the value for role_permission.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setRoleid(Integer roleid) {
        this.roleid = roleid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column role_permission.permissionId
     *
     * @return the value of role_permission.permissionId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public Integer getPermissionid() {
        return permissionid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column role_permission.permissionId
     *
     * @param permissionid the value for role_permission.permissionId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setPermissionid(Integer permissionid) {
        this.permissionid = permissionid;
    }
}