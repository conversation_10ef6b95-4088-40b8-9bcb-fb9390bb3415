package com.swyx.api.contentmanager.dao;

import java.util.Date;

public class TaskEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.task_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.task_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String taskName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.status
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.reviewer
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String reviewer;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.book_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String bookUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.created_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.updated_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Date updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.project_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String projectId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.reviewer_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String reviewerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.priority
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Byte priority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.deadline
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Date deadline;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.file_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String fileName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.prompt_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String promptId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.model_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String modelId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.total_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Integer totalIssueCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.complete_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private Integer completeIssueCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.file_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String fileId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.check_file_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String checkFileUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.project_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String projectName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.id
     *
     * @return the value of task.id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.id
     *
     * @param id the value for task.id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.task_id
     *
     * @return the value of task.task_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.task_id
     *
     * @param taskId the value for task.task_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.task_name
     *
     * @return the value of task.task_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.task_name
     *
     * @param taskName the value for task.task_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.status
     *
     * @return the value of task.status
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.status
     *
     * @param status the value for task.status
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.reviewer
     *
     * @return the value of task.reviewer
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getReviewer() {
        return reviewer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.reviewer
     *
     * @param reviewer the value for task.reviewer
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.book_url
     *
     * @return the value of task.book_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getBookUrl() {
        return bookUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.book_url
     *
     * @param bookUrl the value for task.book_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setBookUrl(String bookUrl) {
        this.bookUrl = bookUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.created_time
     *
     * @return the value of task.created_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.created_time
     *
     * @param createdTime the value for task.created_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.updated_time
     *
     * @return the value of task.updated_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.updated_time
     *
     * @param updatedTime the value for task.updated_time
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.project_id
     *
     * @return the value of task.project_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getProjectId() {
        return projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.project_id
     *
     * @param projectId the value for task.project_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.reviewer_id
     *
     * @return the value of task.reviewer_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getReviewerId() {
        return reviewerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.reviewer_id
     *
     * @param reviewerId the value for task.reviewer_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setReviewerId(String reviewerId) {
        this.reviewerId = reviewerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.priority
     *
     * @return the value of task.priority
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Byte getPriority() {
        return priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.priority
     *
     * @param priority the value for task.priority
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setPriority(Byte priority) {
        this.priority = priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.deadline
     *
     * @return the value of task.deadline
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Date getDeadline() {
        return deadline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.deadline
     *
     * @param deadline the value for task.deadline
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.file_name
     *
     * @return the value of task.file_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.file_name
     *
     * @param fileName the value for task.file_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.prompt_id
     *
     * @return the value of task.prompt_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getPromptId() {
        return promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.prompt_id
     *
     * @param promptId the value for task.prompt_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.model_id
     *
     * @return the value of task.model_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getModelId() {
        return modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.model_id
     *
     * @param modelId the value for task.model_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.total_issue_count
     *
     * @return the value of task.total_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Integer getTotalIssueCount() {
        return totalIssueCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.total_issue_count
     *
     * @param totalIssueCount the value for task.total_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setTotalIssueCount(Integer totalIssueCount) {
        this.totalIssueCount = totalIssueCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.complete_issue_count
     *
     * @return the value of task.complete_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public Integer getCompleteIssueCount() {
        return completeIssueCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.complete_issue_count
     *
     * @param completeIssueCount the value for task.complete_issue_count
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setCompleteIssueCount(Integer completeIssueCount) {
        this.completeIssueCount = completeIssueCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.file_id
     *
     * @return the value of task.file_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getFileId() {
        return fileId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.file_id
     *
     * @param fileId the value for task.file_id
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.check_file_url
     *
     * @return the value of task.check_file_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getCheckFileUrl() {
        return checkFileUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.check_file_url
     *
     * @param checkFileUrl the value for task.check_file_url
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setCheckFileUrl(String checkFileUrl) {
        this.checkFileUrl = checkFileUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.project_name
     *
     * @return the value of task.project_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.project_name
     *
     * @param projectName the value for task.project_name
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}