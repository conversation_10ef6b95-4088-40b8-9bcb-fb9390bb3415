package com.swyx.api.contentmanager.dao;

public class TaskEntityWithBLOBs extends TaskEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.description
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task.content
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.description
     *
     * @return the value of task.description
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.description
     *
     * @param description the value for task.description
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task.content
     *
     * @return the value of task.content
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task.content
     *
     * @param content the value for task.content
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    public void setContent(String content) {
        this.content = content;
    }
}