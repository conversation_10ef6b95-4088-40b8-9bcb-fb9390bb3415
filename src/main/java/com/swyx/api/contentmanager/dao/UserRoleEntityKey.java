package com.swyx.api.contentmanager.dao;

public class UserRoleEntityKey {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_role.userId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private String userid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_role.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    private Integer roleid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_role.userId
     *
     * @return the value of user_role.userId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public String getUserid() {
        return userid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_role.userId
     *
     * @param userid the value for user_role.userId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setUserid(String userid) {
        this.userid = userid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_role.roleId
     *
     * @return the value of user_role.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public Integer getRoleid() {
        return roleid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_role.roleId
     *
     * @param roleid the value for user_role.roleId
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    public void setRoleid(Integer roleid) {
        this.roleid = roleid;
    }
}