package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.AIIssueEntity;
import com.swyx.api.contentmanager.dao.AIIssueEntityExample;
import com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AIIssueEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    long countByExample(AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int insert(AIIssueEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int insertSelective(AIIssueEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    List<AIIssueEntityWithBLOBs> selectByExampleWithBLOBs(AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    List<AIIssueEntity> selectByExample(AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    AIIssueEntityWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByExampleSelective(@Param("row") AIIssueEntityWithBLOBs row, @Param("example") AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") AIIssueEntityWithBLOBs row, @Param("example") AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByExample(@Param("row") AIIssueEntity row, @Param("example") AIIssueEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByPrimaryKeySelective(AIIssueEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(AIIssueEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_issue
     *
     * @mbg.generated Sun Jun 15 09:46:50 CST 2025
     */
    int updateByPrimaryKey(AIIssueEntity row);
}