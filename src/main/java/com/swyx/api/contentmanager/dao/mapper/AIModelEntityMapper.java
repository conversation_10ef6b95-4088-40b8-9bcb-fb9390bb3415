package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.AIModelEntity;
import com.swyx.api.contentmanager.dao.AIModelEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AIModelEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    long countByExample(AIModelEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int insert(AIModelEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int insertSelective(AIModelEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    List<AIModelEntity> selectByExample(AIModelEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    AIModelEntity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int updateByExampleSelective(@Param("row") AIModelEntity row, @Param("example") AIModelEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int updateByExample(@Param("row") AIModelEntity row, @Param("example") AIModelEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int updateByPrimaryKeySelective(AIModelEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_model
     *
     * @mbg.generated Fri Jun 13 16:24:39 CST 2025
     */
    int updateByPrimaryKey(AIModelEntity row);
}