package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.AIPromptEntity;
import com.swyx.api.contentmanager.dao.AIPromptEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AIPromptEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    long countByExample(AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int insert(AIPromptEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int insertSelective(AIPromptEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    List<AIPromptEntity> selectByExampleWithBLOBs(AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    List<AIPromptEntity> selectByExample(AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    AIPromptEntity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByExampleSelective(@Param("row") AIPromptEntity row, @Param("example") AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") AIPromptEntity row, @Param("example") AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByExample(@Param("row") AIPromptEntity row, @Param("example") AIPromptEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByPrimaryKeySelective(AIPromptEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(AIPromptEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ai_prompt
     *
     * @mbg.generated Sat Jun 14 09:07:01 CST 2025
     */
    int updateByPrimaryKey(AIPromptEntity row);
}