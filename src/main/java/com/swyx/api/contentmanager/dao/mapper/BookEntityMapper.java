package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.BookEntity;
import com.swyx.api.contentmanager.dao.BookEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BookEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    long countByExample(BookEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int insert(BookEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int insertSelective(BookEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    List<BookEntity> selectByExample(BookEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    BookEntity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int updateByExampleSelective(@Param("row") BookEntity row, @Param("example") BookEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int updateByExample(@Param("row") BookEntity row, @Param("example") BookEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int updateByPrimaryKeySelective(BookEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table book
     *
     * @mbg.generated Sun Jun 08 14:43:29 CST 2025
     */
    int updateByPrimaryKey(BookEntity row);
}