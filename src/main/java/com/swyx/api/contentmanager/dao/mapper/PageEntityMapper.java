package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.PageEntity;
import com.swyx.api.contentmanager.dao.PageEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PageEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    long countByExample(PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int insert(PageEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int insertSelective(PageEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    List<PageEntity> selectByExampleWithBLOBs(PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    List<PageEntity> selectByExample(PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    PageEntity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByExampleSelective(@Param("row") PageEntity row, @Param("example") PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") PageEntity row, @Param("example") PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByExample(@Param("row") PageEntity row, @Param("example") PageEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByPrimaryKeySelective(PageEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(PageEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table page_content
     *
     * @mbg.generated Sun Jun 08 15:47:26 CST 2025
     */
    int updateByPrimaryKey(PageEntity row);

    /**
     * 批量插入页面数据
     * @param list 页面实体列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<PageEntity> list);
}