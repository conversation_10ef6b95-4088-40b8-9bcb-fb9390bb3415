package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.PermissionEntity;
import com.swyx.api.contentmanager.dao.PermissionEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PermissionEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    long countByExample(PermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insert(PermissionEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insertSelective(PermissionEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    List<PermissionEntity> selectByExample(PermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    PermissionEntity selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExampleSelective(@Param("row") PermissionEntity row, @Param("example") PermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExample(@Param("row") PermissionEntity row, @Param("example") PermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByPrimaryKeySelective(PermissionEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByPrimaryKey(PermissionEntity row);
}