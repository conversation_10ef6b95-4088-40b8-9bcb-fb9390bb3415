package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.ProjectEntity;
import com.swyx.api.contentmanager.dao.ProjectEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    long countByExample(ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int insert(ProjectEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int insertSelective(ProjectEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    List<ProjectEntity> selectByExampleWithBLOBs(ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    List<ProjectEntity> selectByExample(ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    ProjectEntity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByExampleSelective(@Param("row") ProjectEntity row, @Param("example") ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") ProjectEntity row, @Param("example") ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByExample(@Param("row") ProjectEntity row, @Param("example") ProjectEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByPrimaryKeySelective(ProjectEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(ProjectEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table project
     *
     * @mbg.generated Mon Jun 16 14:39:17 CST 2025
     */
    int updateByPrimaryKey(ProjectEntity row);
}