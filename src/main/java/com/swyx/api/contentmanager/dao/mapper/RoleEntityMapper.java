package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.RoleEntity;
import com.swyx.api.contentmanager.dao.RoleEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RoleEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    long countByExample(RoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insert(RoleEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insertSelective(RoleEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    List<RoleEntity> selectByExample(RoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    RoleEntity selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExampleSelective(@Param("row") RoleEntity row, @Param("example") RoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExample(@Param("row") RoleEntity row, @Param("example") RoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByPrimaryKeySelective(RoleEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByPrimaryKey(RoleEntity row);
}