package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.RolePermissionEntityExample;
import com.swyx.api.contentmanager.dao.RolePermissionEntityKey;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RolePermissionEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    long countByExample(RolePermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int deleteByPrimaryKey(RolePermissionEntityKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insert(RolePermissionEntityKey row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insertSelective(RolePermissionEntityKey row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    List<RolePermissionEntityKey> selectByExample(RolePermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExampleSelective(@Param("row") RolePermissionEntityKey row, @Param("example") RolePermissionEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table role_permission
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExample(@Param("row") RolePermissionEntityKey row, @Param("example") RolePermissionEntityExample example);
}