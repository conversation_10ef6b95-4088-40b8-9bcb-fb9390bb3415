package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.TaskEntityExample;
import com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TaskEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    long countByExample(TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int insert(TaskEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int insertSelective(TaskEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    List<TaskEntityWithBLOBs> selectByExampleWithBLOBs(TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    List<TaskEntity> selectByExample(TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    TaskEntityWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByExampleSelective(@Param("row") TaskEntityWithBLOBs row, @Param("example") TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") TaskEntityWithBLOBs row, @Param("example") TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByExample(@Param("row") TaskEntity row, @Param("example") TaskEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByPrimaryKeySelective(TaskEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(TaskEntityWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task
     *
     * @mbg.generated Sat Jun 21 16:32:29 CST 2025
     */
    int updateByPrimaryKey(TaskEntity row);
}