package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.UserEntity;
import com.swyx.api.contentmanager.dao.UserEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    long countByExample(UserEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int deleteByPrimaryKey(String userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int insert(UserEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int insertSelective(UserEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    List<UserEntity> selectByExample(UserEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    UserEntity selectByPrimaryKey(String userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int updateByExampleSelective(@Param("row") UserEntity row, @Param("example") UserEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int updateByExample(@Param("row") UserEntity row, @Param("example") UserEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int updateByPrimaryKeySelective(UserEntity row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user
     *
     * @mbg.generated Sat Jun 21 15:54:26 CST 2025
     */
    int updateByPrimaryKey(UserEntity row);
}