package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dao.UserRoleEntityExample;
import com.swyx.api.contentmanager.dao.UserRoleEntityKey;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserRoleEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    long countByExample(UserRoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int deleteByPrimaryKey(UserRoleEntityKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insert(UserRoleEntityKey row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int insertSelective(UserRoleEntityKey row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    List<UserRoleEntityKey> selectByExample(UserRoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExampleSelective(@Param("row") UserRoleEntityKey row, @Param("example") UserRoleEntityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_role
     *
     * @mbg.generated Fri Jun 20 17:14:32 CST 2025
     */
    int updateByExample(@Param("row") UserRoleEntityKey row, @Param("example") UserRoleEntityExample example);
}