package com.swyx.api.contentmanager.dao.mapper;

import com.swyx.api.contentmanager.dto.UserRolePermissionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserRolePermissionCustomMapper {
    /**
     * 根据用户id查询用户角色和权限
     *
     * @param userId
     * @return
     */
    List<UserRolePermissionDTO> selectRolesAndPermissionsByUserId(@Param("userId") String userId);
}
