package com.swyx.api.contentmanager.dto;

import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.UserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectDetailResponse extends ProjectListResponse {
    private List<TaskEntity> tasks;
    private List<UserEntity> reviewers;
    private UserEntity createUser;
}
