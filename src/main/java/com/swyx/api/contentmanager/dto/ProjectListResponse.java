package com.swyx.api.contentmanager.dto;

import com.swyx.api.contentmanager.dao.ProjectEntity;
import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.UserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectListResponse extends ProjectEntity {
    public Integer totalIssueCount;
    public Integer completeIssueCount;
    public List<String> reviewerAvatars;
}
