package com.swyx.api.contentmanager.dto;

import java.util.List;

/**
 * 统一对外响应的实体
 *
 * <AUTHOR>
 * @ClassName: Response
 * @date 2025-06-07 下午2:54
 */
public class Response<T> {
    private int errorCode; //响应码
    private String errorMsg; //响应码对应的内容
    private T data;  //响应数据

    public Response() {
    }

    public Response(Integer code, String msg, T data) {
        this.errorCode = code;
        this.errorMsg = msg;
        this.data = data;
    }

    public static <D> Response<D> ofData(D data) {
        return new Response<D>(10000, "成功", data);
    }

    public static <D> Response<D> fail() {
        return new Response<D>(20000, "失败", null);
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public T getData() {
        return data;
    }

    public boolean isSuccess() {
        return this.errorCode >= 10000 && this.errorCode < 20000;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Response{" +
                "errorCode=" + errorCode +
                ", errorMsg='" + errorMsg + '\'' +
                ", data=" + data +
                '}';
    }
}