package com.swyx.api.contentmanager.dto;

import com.swyx.api.contentmanager.dao.AIIssueEntity;
import com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs;
import com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TaskResponse extends TaskEntityWithBLOBs {
    private List<AIIssueEntityWithBLOBs> issues;
}
