package com.swyx.api.contentmanager.dto;

import com.swyx.api.contentmanager.dao.UserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserInfoResponse extends UserEntity {
    private String accessToken;
    private String refreshToken;
    private List<String> roles;
    private List<String> permissions;
    private long taskCount;
}
