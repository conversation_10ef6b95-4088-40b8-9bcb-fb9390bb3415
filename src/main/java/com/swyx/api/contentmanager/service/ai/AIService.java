package com.swyx.api.contentmanager.service.ai;

import com.swyx.api.contentmanager.dao.*;
import com.swyx.api.contentmanager.dao.mapper.AIIssueEntityMapper;
import com.swyx.api.contentmanager.dao.mapper.AIModelEntityMapper;
import com.swyx.api.contentmanager.dao.mapper.AIPromptEntityMapper;
import com.swyx.api.contentmanager.service.task.TaskService;
import com.swyx.api.contentmanager.utils.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AIService {
    @Autowired
    private AIModelEntityMapper aiModelEntityMapper;

    @Autowired
    private AIPromptEntityMapper aiPromptEntityMapper;

    @Autowired
    private AIIssueEntityMapper aiIssueEntityMapper;

    @Autowired
    private TaskService taskService;

    public List<AIModelEntity> getModels() {
        AIModelEntityExample example = new AIModelEntityExample();
        AIModelEntityExample.Criteria criteria = example.createCriteria();
        return aiModelEntityMapper.selectByExample(example);
    }

    public AIModelEntity getModel(String modelId) {
        AIModelEntityExample example = new AIModelEntityExample();
        AIModelEntityExample.Criteria criteria = example.createCriteria();
        criteria.andModelIdEqualTo(modelId);
        return aiModelEntityMapper.selectByExample(example).getFirst();
    }

    public Boolean modifyModel(String id, String token, String apiurl) {
        AIModelEntityExample example = new AIModelEntityExample();
        AIModelEntityExample.Criteria criteria = example.createCriteria();
        criteria.andModelIdEqualTo(id);
        AIModelEntity aiModelEntity = aiModelEntityMapper.selectByExample(example).getFirst();
        if (aiModelEntity == null) {
            return false;
        }

        aiModelEntity.setToken(token);
        aiModelEntity.setApiurl(apiurl);
        aiModelEntityMapper.updateByPrimaryKeySelective(aiModelEntity);
        return true;
    }

    public List<AIPromptEntity> getPrompts() {
        AIPromptEntityExample example = new AIPromptEntityExample();
        AIPromptEntityExample.Criteria criteria = example.createCriteria();
        return aiPromptEntityMapper.selectByExampleWithBLOBs(example);
    }

    public AIPromptEntity getPrompt(String promptId) {
        AIPromptEntityExample example = new AIPromptEntityExample();
        AIPromptEntityExample.Criteria criteria = example.createCriteria();
        criteria.andPromptIdEqualTo(promptId);
        return aiPromptEntityMapper.selectByExampleWithBLOBs(example).getFirst();
    }

    public AIPromptEntity savePrompt(String prompt, String type) {
        AIPromptEntity aiPromptEntity = new AIPromptEntity();
        aiPromptEntity.setText(prompt);
        aiPromptEntity.setType(type);
        aiPromptEntity.setCount(0);
        aiPromptEntity.setScore(0.0f);
        aiPromptEntity.setPromptId("prompt-" + IdGenerator.generateRadomId());
        aiPromptEntityMapper.insert(aiPromptEntity);
        return aiPromptEntity;
    }

    public Boolean modifyPrompt(String id, String prompt, String type) {
        AIPromptEntityExample example = new AIPromptEntityExample();
        AIPromptEntityExample.Criteria criteria = example.createCriteria();
        criteria.andPromptIdEqualTo(id);
        AIPromptEntity aiPromptEntity = aiPromptEntityMapper.selectByExample(example).getFirst();
        if (aiPromptEntity == null) {
            return false;
        }

        aiPromptEntity.setText(prompt);
        aiPromptEntity.setType(type);
        aiPromptEntityMapper.updateByPrimaryKeySelective(aiPromptEntity);
        return true;
    }

    public void deletePrompt(String id) {
        AIPromptEntityExample example = new AIPromptEntityExample();
        AIPromptEntityExample.Criteria criteria = example.createCriteria();
        criteria.andPromptIdEqualTo(id);
        AIPromptEntity aiPromptEntity = aiPromptEntityMapper.selectByExample(example).getFirst();
        if (aiPromptEntity == null) {
            return;
        }
        aiPromptEntityMapper.deleteByPrimaryKey(aiPromptEntity.getId());
    }

    public void deleteIssueByTaskId(String taskId) {
        AIIssueEntityExample example = new AIIssueEntityExample();
        AIIssueEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        List<AIIssueEntity> issueEntities = aiIssueEntityMapper.selectByExample(example);
        for (AIIssueEntity issueEntity : issueEntities) {
            aiIssueEntityMapper.deleteByPrimaryKey(issueEntity.getId());
        }
    }

    public void saveIssues(List<AIIssueEntityWithBLOBs> issues, String taskId, String projectId, String modelId, String promptId) {
        for (AIIssueEntityWithBLOBs aiIssueEntity : issues) {
            aiIssueEntity.setIssueId("Issue-" + IdGenerator.generateRadomId());
            aiIssueEntity.setProjectId(projectId);
            aiIssueEntity.setTaskId(taskId);
            aiIssueEntity.setModelId(modelId);
            aiIssueEntity.setPromptId(promptId);
            aiIssueEntity.setStatus((byte) 0);
            aiIssueEntityMapper.insert(aiIssueEntity);
        }
    }

    /**
     * 根据 taskId 查询Issue 列表
     * @param taskId 任务id
     * @return AIIssueEntityWithBLOBs 问题列表
     */
    public List<AIIssueEntityWithBLOBs> getIssuesByTaskId(String taskId) {
        AIIssueEntityExample example = new AIIssueEntityExample();
        AIIssueEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        return aiIssueEntityMapper.selectByExampleWithBLOBs(example);
    }

    @Transactional
    public void updateIssue(String issueId, Integer status) {
        AIIssueEntityExample example = new AIIssueEntityExample();
        AIIssueEntityExample.Criteria criteria = example.createCriteria();
        criteria.andIssueIdEqualTo(issueId);
        AIIssueEntity issueEntity = aiIssueEntityMapper.selectByExample(example).getFirst();
        if (issueEntity == null) {
            return;
        }
        Integer oldStatus = issueEntity.getStatus().intValue();
        if (needUpdateTaskProgress(oldStatus, status)) {
            TaskEntity taskEntity = taskService.getTaskEntityById(issueEntity.getTaskId());
            int change = calculateTaskChange(oldStatus, status);
            Integer currentCompleteCount = taskEntity.getCompleteIssueCount();
            Integer totalIssueCount = taskEntity.getTotalIssueCount();
            if (currentCompleteCount == null) {
                currentCompleteCount = 0;
            }
            taskEntity.setCompleteIssueCount(currentCompleteCount + change);
            if (totalIssueCount.equals(currentCompleteCount)) {
                taskEntity.setStatus(Byte.valueOf("2"));
            }
            if (change != 0) {
                taskService.updateTask(taskEntity);
            }
        }

        issueEntity.setStatus(status.byteValue());
        aiIssueEntityMapper.updateByExample(issueEntity, example);
    }

    // issue状态变化规则
    private boolean needUpdateTaskProgress(Integer oldStatus, Integer newStatus) {
        return (oldStatus == 0 && newStatus != 0) || (oldStatus != 0 && newStatus == 0);
    }

    private int calculateTaskChange(Integer oldStatus, Integer newStatus) {
        if (oldStatus == 0 && newStatus != 0) return 1; // 0→1/2：完成数+1
        if (oldStatus != 0 && newStatus == 0) return -1; // 1/2→0：完成数-1
        return 0;
    }
}
