package com.swyx.api.contentmanager.service.ai;
import com.alibaba.fastjson.JSONArray;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.core.JsonObject;
import com.openai.core.JsonField;
import com.openai.core.JsonValue;
import com.openai.models.*;
import com.swyx.api.contentmanager.dao.AIModelEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Objects;

@Service
public class OpenAIService {
    private final static String uploadDir = "/tmp";

    @Autowired
    private AIService aiService;

    // 上传文件至阿里云百炼平台获取文件id
    public String uploadFile(MultipartFile file) throws IOException {
        AIModelEntity modelEntity = aiService.getModel("qwen-plus");
        String apiurl = modelEntity.getApiurl();
        String token = modelEntity.getToken();
        OpenAIClient openAIClient = OpenAIOkHttpClient.builder()
                .apiKey(token)
                .baseUrl(apiurl)
                .build();

        Path path = Paths.get(uploadDir);
        if (!Files.exists(path)) {
            Files.createDirectories(path); // 创建目录
        }
        Path filePath = path.resolve(Objects.requireNonNull(file.getOriginalFilename()));
        file.transferTo(filePath); // 保存文件

        FileCreateParams fileParams = FileCreateParams.builder()
                .file(filePath)
                .purpose(FilePurpose.of("file-extract"))
                .build();

        FileObject fileObject = openAIClient.files().create(fileParams);
        System.out.println(fileObject.id());
        return fileObject.id();
    }

    public JSONArray creatMessage(AIModelEntity modelEntity, String prompt, String message) {
        String modelId = modelEntity.getModelId();
        String apiurl = modelEntity.getApiurl();
        String token = modelEntity.getToken();
        OpenAIClient openAIClient = OpenAIOkHttpClient.builder()
                .apiKey(token)
                .baseUrl(apiurl)
                .build();

        ChatCompletionCreateParams params;
        ResponseFormatJsonObject responseFormat = ResponseFormatJsonObject.builder().build();

        if (message.startsWith("file-fe-") && modelId.equals("qwen-long")) {
            message = "fileid://" + message;
            params = ChatCompletionCreateParams.builder()
                    .addSystemMessage(prompt)
                    .addSystemMessage(message)
                    .addUserMessage("帮我分析文中的问题，并用JSON格式输出")
                    .model(modelId)
                    .temperature(0.1)
                    .responseFormat(responseFormat)
                    .build();
        } else {
            params = ChatCompletionCreateParams.builder()
                    .addSystemMessage(prompt)
                    .addUserMessage(message)
                    .addUserMessage("帮我分析文中的问题，并用JSON格式输出")
                    .model(modelId)
                    .temperature(0.1)
                    .responseFormat(responseFormat)
                    .build();
        }

        try {
            ChatCompletion chatCompletion = openAIClient.chat().completions().create(params);
            String response = chatCompletion.choices().getFirst().message().content().get();
            return JSONArray.parseArray(response);

        } catch (Exception e) {
            System.err.println("Error occurred: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
