package com.swyx.api.contentmanager.service.book;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.*;
import com.swyx.api.contentmanager.dao.mapper.BookEntityMapper;
import com.swyx.api.contentmanager.dao.mapper.PageEntityMapper;
import com.swyx.api.contentmanager.dao.mapper.TaskEntityMapper;
import com.swyx.api.contentmanager.dto.Response;
import com.swyx.api.contentmanager.dto.SaveBookResponse;
import com.swyx.api.contentmanager.service.page.PageService;
import com.swyx.api.contentmanager.utils.FileUtil;
import com.swyx.api.contentmanager.utils.IdGenerator;
import com.swyx.api.contentmanager.utils.UploadCOS;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class BookService {

    @Autowired
    private BookEntityMapper bookEntityMapper;

    @Autowired
    private TextParserService textParserService;

    @Autowired
    private PageService pageService;

    @Value("${file.upload-dir}") // 从配置读取存储路径
    private String uploadDir;
    @Autowired
    private PageEntityMapper pageEntityMapper;


    public static void main(String[] args) {
        BookService bookService = new BookService();
        bookService.saveBook(null);
    }

    public BookEntity getBook(String bookId) {
        BookEntityExample example = new BookEntityExample();
        BookEntityExample.Criteria criteria = example.createCriteria();
        criteria.andBookIdEqualTo(bookId);

        List<BookEntity> books = bookEntityMapper.selectByExample(example);
        if (books == null || books.isEmpty()) {
            return null;
        }
        return books.getFirst();
    }

    public void updateBook(BookEntity bookEntity) {
        BookEntityExample example = new BookEntityExample();
        BookEntityExample.Criteria criteria = example.createCriteria();
        criteria.andBookIdEqualTo(bookEntity.getBookId());
        bookEntityMapper.updateByExampleSelective(bookEntity, example);
    }

    public SaveBookResponse saveBook(MultipartFile file) {
        log.info("--- SaveBookResponse 进入");
        String bookUrl = FileUtil.uploadFile(file);
        String bookId = "book-" + IdGenerator.generateRadomId();

        BookEntity bookEntity = new BookEntity();
        bookEntity.setBookId(bookId);
        bookEntity.setDownloadurl(bookUrl);
        bookEntity.setAuthor("装呀鸟");
        bookEntity.setIsbn(bookId);
        bookEntity.setTitle("第一本书");

        // 解析文件
        JSONObject content = textParserService.parseTaskBookContent(bookUrl);

        log.info("--- SaveBookResponse TextIN解析 {}", content);

        JSONObject result = content.getJSONObject("result");
        String markdown = result.getString("markdown");
        Integer pageNumber = result.getInteger("total_page_number");
        JSONArray pages = result.getJSONArray("pages");

        List<PageEntity> pageEntities = new ArrayList<>();
        for (Object page : pages) {
            try {
                // 修复类型转换错误
                JSONObject pageJson;
                if (page instanceof JSONObject) {
                    pageJson = (JSONObject) page;
                } else if (page instanceof java.util.Map) {
                    pageJson = new JSONObject((java.util.Map<String, Object>) page);
                } else {
                    // 如果既不是JSONObject也不是Map，则尝试将其转换为JSONObject
                    pageJson = JSONObject.parseObject(JSONObject.toJSONString(page));
                }

                Integer pageId = pageJson.getInteger("page_id");

                // 修复JSONArray的类型转换问题
                JSONArray contentsObj = pageJson.getJSONArray("content");
                String contentStr = contentsObj.toJSONString();

                PageEntity pageEntity = new PageEntity();
                pageEntity.setBookId(bookId);
                pageEntity.setPageId("page-" + IdGenerator.generateRadomId());
                pageEntity.setPageNum(pageId);
                pageEntity.setContent(contentStr);
                pageEntity.setContentHash(contentStr.hashCode() + "");

                pageEntities.add(pageEntity);
            } catch (Exception e) {
                System.out.println("处理页面数据时出错: " + e.getMessage() + ", 页面数据: " + page);
                // 继续处理下一个页面，不中断整个流程
            }
        }
        bookEntity.setTotalPages(pageNumber);

        // 插入数据库
        bookEntityMapper.insert(bookEntity);
        pageService.savePages(pageEntities);

        SaveBookResponse response = new SaveBookResponse();
        response.setBookId(bookId);
        response.setBookUrl(bookUrl);
        response.setMarkdown(markdown);

        return response;
    }
}
