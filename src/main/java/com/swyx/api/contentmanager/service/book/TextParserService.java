package com.swyx.api.contentmanager.service.book;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.mapper.TaskEntityMapper;
import com.swyx.api.contentmanager.service.common.HttpCilentService;
import com.swyx.api.contentmanager.service.task.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

@Service
public class TextParserService {
    @Autowired
    private HttpCilentService httpCilentService;

    public JSONObject parseTaskBookContent(String bookUrl) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.set("x-ti-app-id", "15aa5974e88b0963d73ded9eb910356d");
        headers.set("x-ti-secret-code", "13d81227a32cebeb7606b6eb6959342f");

        String textInUrl = "https://api.textin.com/ai/service/v1/pdf_to_markdown";
        String requestUrl = textInUrl + "?catalog_details=1&table_flavor=md";

        return httpCilentService.post(requestUrl, headers, bookUrl, JSONObject.class);
    }

    public JSONObject readLocalJson() throws IOException {
        // 步骤1：读取文件字节流
        byte[] jsonBytes = Files.readAllBytes(Paths.get("/Users/<USER>/IdeaProjects/ContentManager/src/test/resources/pdf2md.json"));

        // 步骤2：转换为JSON字符串（显式指定UTF-8编码）
        String jsonString = new String(jsonBytes, "UTF-8");

        // 步骤3：转换为JSONObject对象
        JSONObject jsonObject = JSON.parseObject(jsonString);

        return jsonObject;
    }
}
