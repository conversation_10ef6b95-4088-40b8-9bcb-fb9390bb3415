package com.swyx.api.contentmanager.service.common;

import com.alibaba.fastjson.JSONArray;
import com.swyx.api.contentmanager.dao.*;
import com.swyx.api.contentmanager.service.ai.AIService;
import com.swyx.api.contentmanager.service.ai.OpenAIService;
import com.swyx.api.contentmanager.service.task.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AsyncExService {
    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private AIService aiService;

    @Autowired
    private TaskService taskService;

    @Async("taskExecutor")
    public void doParseContent(TaskEntityWithBLOBs task) {
        String taskId = task.getTaskId();
        String projectId = task.getProjectId();
        String modelId = task.getModelId();
        String promptId = task.getPromptId();
        AIPromptEntity prompt = aiService.getPrompt(promptId);
        AIModelEntity model = aiService.getModel(modelId);

        // 调用AI审核
        String fileId = task.getFileId();
        String content = task.getContent();
        String message = fileId == null ? content : fileId;

        JSONArray res = openAIService.creatMessage(model, prompt.getText(), message);
        List<AIIssueEntityWithBLOBs> issueEntities = res.toJavaList(AIIssueEntityWithBLOBs.class);
        aiService.saveIssues(issueEntities, taskId, projectId, modelId, promptId);

        // 更新TASK状态
        task.setStatus((byte) 1);
        task.setTotalIssueCount(issueEntities.size());
        taskService.updateTask(task);
    }
}
