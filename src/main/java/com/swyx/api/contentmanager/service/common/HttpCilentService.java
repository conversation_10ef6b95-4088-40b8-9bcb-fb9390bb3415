package com.swyx.api.contentmanager.service.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class HttpCilentService {
    
    private final RestTemplate restTemplate;
    
    @Autowired
    public HttpCilentService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    /**
     * 发送GET请求
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public <T> T get(String url, HttpHeaders headers, Class<T> responseType) {
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
        return response.getBody();
    }
    
    /**
     * 发送带参数的GET请求
     * @param url 请求URL
     * @param headers 请求头
     * @param params URL参数
     * @param responseType 返回类型
     * @return 响应结果
     */
    public <T> T get(String url, HttpHeaders headers, Map<String, ?> params, Class<T> responseType) {
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType, params);
        return response.getBody();
    }
    
    /**
     * 发送POST请求
     * @param url 请求URL
     * @param headers 请求头
     * @param body 请求体
     * @param responseType 返回类型
     * @return 响应结果
     */
    public <T> T post(String url, HttpHeaders headers, Object body, Class<T> responseType) {
        HttpEntity<Object> entity = new HttpEntity<>(body, headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, entity, responseType);
        return response.getBody();
    }
    
    /**
     * 发送PUT请求
     * @param url 请求URL
     * @param headers 请求头
     * @param body 请求体
     * @param responseType 返回类型
     * @return 响应结果
     */
    public <T> T put(String url, HttpHeaders headers, Object body, Class<T> responseType) {
        HttpEntity<Object> entity = new HttpEntity<>(body, headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.PUT, entity, responseType);
        return response.getBody();
    }
    
    /**
     * 发送DELETE请求
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public <T> T delete(String url, HttpHeaders headers, Class<T> responseType) {
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, responseType);
        return response.getBody();
    }
}
