package com.swyx.api.contentmanager.service.docx;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * 文档分割使用示例
 * 展示如何处理各种类型的文档和异常情况
 */
@Component
public class DocumentSplitterExample {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    /**
     * 智能分割文档 - 自动处理各种异常情况
     */
    public void smartSplitDocument(String sourceFilePath, String outputDirPath, int maxWordCount) {
        try {
            System.out.println("开始分割文档: " + sourceFilePath);
            System.out.println("字数限制: " + maxWordCount);
            
            // 使用智能分割方法
            List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
            
            if (resultFiles.isEmpty()) {
                System.out.println("警告: 文档为空或无有效内容");
                return;
            }
            
            System.out.println("分割完成! 共生成 " + resultFiles.size() + " 个文件:");
            for (int i = 0; i < resultFiles.size(); i++) {
                File file = resultFiles.get(i);
                System.out.printf("  %d. %s (大小: %.2f KB)%n", 
                    i + 1, file.getName(), file.length() / 1024.0);
            }
            
        } catch (Exception e) {
            System.err.println("分割文档时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理没有标题的文档
     */
    public void splitPlainTextDocument(String sourceFilePath, String outputDirPath, int maxWordCount) {
        try {
            System.out.println("处理纯文本文档: " + sourceFilePath);
            
            // 先尝试智能分割
            List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
            
            if (resultFiles.size() == 1) {
                System.out.println("检测到文档可能没有标题结构，尝试纯段落分割...");
                
                // 使用纯段落分割
                String paragraphOutputDir = outputDirPath + "_paragraphs";
                List<File> paragraphFiles = docx4jSplitterService.splitByParagraphsOnly(
                    sourceFilePath, paragraphOutputDir, maxWordCount);
                
                System.out.println("纯段落分割完成! 共生成 " + paragraphFiles.size() + " 个文件");
            } else {
                System.out.println("智能分割成功! 共生成 " + resultFiles.size() + " 个文件");
            }
            
        } catch (Exception e) {
            System.err.println("处理纯文本文档时发生错误: " + e.getMessage());
        }
    }

    /**
     * 批量处理多个文档
     */
    public void batchSplitDocuments(String[] sourceFilePaths, String baseOutputDir, int maxWordCount) {
        for (int i = 0; i < sourceFilePaths.length; i++) {
            String sourceFile = sourceFilePaths[i];
            String outputDir = baseOutputDir + "/document_" + (i + 1);
            
            System.out.println("\n=== 处理文档 " + (i + 1) + "/" + sourceFilePaths.length + " ===");
            smartSplitDocument(sourceFile, outputDir, maxWordCount);
        }
    }

    /**
     * 根据文档类型选择最佳分割策略
     */
    public void adaptiveSplitDocument(String sourceFilePath, String outputDirPath, int maxWordCount) {
        try {
            // 首先尝试标准分割
            List<File> standardResults = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath + "_standard", maxWordCount);
            
            // 如果只生成了一个文件，可能没有标题结构
            if (standardResults.size() == 1) {
                System.out.println("文档可能缺少标题结构，尝试段落分割...");
                
                List<File> paragraphResults = docx4jSplitterService.splitByParagraphsOnly(
                    sourceFilePath, outputDirPath + "_paragraphs", maxWordCount);
                
                // 比较两种方法的结果，选择更好的
                if (paragraphResults.size() > standardResults.size()) {
                    System.out.println("推荐使用段落分割结果 (" + paragraphResults.size() + " 个文件)");
                } else {
                    System.out.println("推荐使用标准分割结果 (" + standardResults.size() + " 个文件)");
                }
            } else {
                System.out.println("标准分割效果良好，生成了 " + standardResults.size() + " 个文件");
            }
            
        } catch (Exception e) {
            System.err.println("自适应分割时发生错误: " + e.getMessage());
        }
    }

    /**
     * 验证分割结果
     */
    public void validateSplitResults(List<File> resultFiles, int maxWordCount) {
        System.out.println("\n=== 分割结果验证 ===");
        
        for (File file : resultFiles) {
            try {
                // 这里可以添加验证逻辑，比如检查文件大小、字数等
                long fileSize = file.length();
                System.out.printf("文件: %s, 大小: %.2f KB%n", file.getName(), fileSize / 1024.0);
                
                if (fileSize == 0) {
                    System.out.println("  警告: 文件为空");
                } else if (fileSize > maxWordCount * 10) { // 粗略估算
                    System.out.println("  警告: 文件可能超过字数限制");
                }
                
            } catch (Exception e) {
                System.err.println("验证文件 " + file.getName() + " 时出错: " + e.getMessage());
            }
        }
    }
}
