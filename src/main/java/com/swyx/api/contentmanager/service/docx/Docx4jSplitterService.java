package com.swyx.api.contentmanager.service.docx;
import jakarta.xml.bind.JAXBElement;
import org.docx4j.Docx4J;
import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;

import org.springframework.stereotype.Service;
import java.io.*;
import java.util.*;

@Service
public class Docx4jSplitterService {
    public List<File> splitByHeading(String sourceFilePath, String outputDirPath) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        File outputDir = new File(outputDirPath);

        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        WordprocessingMLPackage wordMLPackage = Docx4J.load(new File(sourceFilePath));
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        ObjectFactory factory = new ObjectFactory();

        // 获取所有段落
        List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);
        Map<Integer, List<P>> chapterMap = new LinkedHashMap<>();
        int chapterIndex = 0;

        // 按标题样式拆分
        for (Object obj : paragraphs) {
            P para = (P) obj;
            if (isHeading1(para)) {
                chapterIndex++;
            }

            if (chapterIndex > 0) {
                chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
            }
        }

        // 保存各章节
        for (Map.Entry<Integer, List<P>> entry : chapterMap.entrySet()) {
            String chapterTitle = getParagraphText(entry.getValue().get(0));
            String fileName = String.format("chapter_%02d_%s.docx",
                    entry.getKey(),
                    chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
            File chapterFile = new File(outputDir, fileName);

            WordprocessingMLPackage newPackage = WordprocessingMLPackage.createPackage();
            MainDocumentPart newDocumentPart = newPackage.getMainDocumentPart();

            // 添加章节内容
            for (P para : entry.getValue()) {
                P newPara = (P) XmlUtils.deepCopy(para);
                newDocumentPart.addObject(newPara);
            }

            Docx4J.save(newPackage, chapterFile);
            resultFiles.add(chapterFile);
        }

        return resultFiles;
    }

    // paragraph is heading1
    private boolean isHeading1(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            String styleId = para.getPPr().getPStyle().getVal();
            return styleId != null && styleId.equals("Heading1");
        }
        return false;
    }

    // 获取段落文本内容
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof R) {
                    R run = (R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof Text) {
                            text.append(((Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString();
    }

    // 获取文档中所有指定类型的元素
    private List<Object> getAllElementsFromObject(Object obj, Class<?> toSearch) {
        List<Object> result = new ArrayList<>();
        if (obj instanceof org.w3c.dom.Node) {
            org.w3c.dom.Node node = (org.w3c.dom.Node) obj;
            if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                if (toSearch.isInstance(node)) {
                    result.add(node);
                }
                org.w3c.dom.NodeList children = node.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    result.addAll(getAllElementsFromObject(children.item(i), toSearch));
                }
            }
        } else if (obj instanceof JAXBElement) {
            result.addAll(getAllElementsFromObject(((JAXBElement<?>) obj).getValue(), toSearch));
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object o : list) {
                result.addAll(getAllElementsFromObject(o, toSearch));
            }
        } else if (obj != null) {
            if (toSearch.isInstance(obj)) {
                result.add(obj);
            }
            // 递归处理对象的字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        result.addAll(getAllElementsFromObject(fieldValue, toSearch));
                    }
                } catch (IllegalAccessException e) {
                    // 忽略
                }
            }
        }
        return result;
    }
}
