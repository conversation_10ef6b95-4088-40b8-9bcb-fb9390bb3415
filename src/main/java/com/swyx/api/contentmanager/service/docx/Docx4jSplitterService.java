package com.swyx.api.contentmanager.service.docx;
import jakarta.xml.bind.JAXBElement;
import org.docx4j.Docx4J;
import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;

import org.springframework.stereotype.Service;
import java.io.*;
import java.util.*;

@Service
public class Docx4jSplitterService {
    public List<File> splitByHeading(String sourceFilePath, String outputDirPath) throws Exception {
        return splitByHeading(sourceFilePath, outputDirPath, Integer.MAX_VALUE, false);
    }

    public List<File> splitByHeading(String sourceFilePath, String outputDirPath, int maxWordCount) throws Exception {
        return splitByHeading(sourceFilePath, outputDirPath, maxWordCount, false);
    }

    /**
     * 按标题分割文档
     * @param sourceFilePath 源文件路径
     * @param outputDirPath 输出目录路径
     * @param maxWordCount 最大字数限制
     * @param splitLargeHeadings 是否分割大标题（默认false，即大标题不单独分割）
     * @return 分割后的文件列表
     */
    public List<File> splitByHeading(String sourceFilePath, String outputDirPath, int maxWordCount, boolean splitLargeHeadings) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        File outputDir = new File(outputDirPath);
        File sourceFile = new File(sourceFilePath);

        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // 获取原文件名（不含扩展名）作为前缀
        String originalFileName = sourceFile.getName();
        String fileNamePrefix = originalFileName.substring(0, originalFileName.lastIndexOf('.'));

        WordprocessingMLPackage wordMLPackage = Docx4J.load(sourceFile);
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();

        // 获取所有段落
        List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);

        // 转换为段落列表并计算总字数
        List<P> allParagraphs = new ArrayList<>();
        for (Object obj : paragraphs) {
            if (obj instanceof P) {
                allParagraphs.add((P) obj);
            }
        }

        // 计算文档总字数
        int totalWordCount = calculateWordCount(allParagraphs);

        // 如果总字数小于等于限制，直接复制原文件
        if (totalWordCount <= maxWordCount) {
            String fileName = String.format("%s_complete.docx", fileNamePrefix);
            File outputFile = new File(outputDir, fileName);

            // 直接保存完整文档（保持所有样式和格式）
            Docx4J.save(wordMLPackage, outputFile);
            resultFiles.add(outputFile);

            System.out.println(String.format("文档总字数(%d)未超过限制(%d)，直接输出完整文档: %s",
                totalWordCount, maxWordCount, fileName));
            return resultFiles;
        }

        System.out.println(String.format("文档总字数(%d)超过限制(%d)，开始按标题分割...",
            totalWordCount, maxWordCount));

        Map<Integer, List<P>> chapterMap = new LinkedHashMap<>();
        int chapterIndex = 0;

        // 改进的按标题样式拆分逻辑
        List<P> preamble = new ArrayList<>(); // 文档前言（第一个Heading1之前的内容）
        boolean foundFirstHeading1 = false;

        for (P para : allParagraphs) {
            if (isHeading1(para)) {
                if (!foundFirstHeading1) {
                    // 第一个Heading1：将前言与第一章合并
                    foundFirstHeading1 = true;
                    chapterIndex = 1;
                    // 先添加前言内容
                    for (P preamblePara : preamble) {
                        chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(preamblePara);
                    }
                    // 再添加当前Heading1
                    chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
                } else {
                    // 后续的Heading1：开始新章节
                    chapterIndex++;
                    chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
                }
            } else {
                if (!foundFirstHeading1) {
                    // 还没遇到第一个Heading1，加入前言
                    preamble.add(para);
                } else {
                    // 已经遇到Heading1，加入当前章节
                    chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
                }
            }
        }

        // 如果没有找到任何Heading1，将所有内容（包括前言）归入第一章
        if (!foundFirstHeading1 && !allParagraphs.isEmpty()) {
            chapterIndex = 1;
            chapterMap.put(chapterIndex, new ArrayList<>(allParagraphs));
        }

        // 注意：现在所有段落都已经被正确分配到章节中，不需要额外处理

        // 保存各章节，如果字数超过限制则进一步分割
        for (Map.Entry<Integer, List<P>> entry : chapterMap.entrySet()) {
            List<P> chapterParagraphs = entry.getValue();
            int wordCount = calculateWordCount(chapterParagraphs);

            if (wordCount <= maxWordCount) {
                // 字数未超过限制，直接保存
                String chapterTitle = getChapterTitle(chapterParagraphs, entry.getKey());
                String fileName = String.format("%s_chapter_%02d_%s.docx",
                        fileNamePrefix,
                        entry.getKey(),
                        chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                File chapterFile = new File(outputDir, fileName);

                saveChapterToFileWithStyles(chapterParagraphs, chapterFile, wordMLPackage);
                resultFiles.add(chapterFile);
            } else {
                // 字数超过限制，根据splitLargeHeadings参数决定分割策略
                if (splitLargeHeadings) {
                    // 允许分割大标题：按Heading2进一步分割
                    List<File> subChapterFiles = splitChapterByHeading2(chapterParagraphs, outputDir,
                            entry.getKey(), maxWordCount, wordMLPackage, fileNamePrefix, splitLargeHeadings);
                    resultFiles.addAll(subChapterFiles);
                } else {
                    // 不分割大标题：检查是否有大标题需要特殊处理
                    List<File> processedFiles = handleLargeChapterWithoutSplittingHeadings(
                            chapterParagraphs, outputDir, entry.getKey(), maxWordCount,
                            wordMLPackage, fileNamePrefix);
                    resultFiles.addAll(processedFiles);
                }
            }
        }

        return resultFiles;
    }

    /**
     * 按段落分割文档（适用于没有标题结构的文档）
     * @param sourceFilePath 源文件路径
     * @param outputDirPath 输出目录路径
     * @param maxWordCount 最大字数限制
     * @return 分割后的文件列表
     */
    public List<File> splitByParagraphsOnly(String sourceFilePath, String outputDirPath, int maxWordCount) throws Exception {
        return splitByParagraphsOnly(sourceFilePath, outputDirPath, maxWordCount, false);
    }

    /**
     * 按段落分割文档（适用于没有标题结构的文档）
     * @param sourceFilePath 源文件路径
     * @param outputDirPath 输出目录路径
     * @param maxWordCount 最大字数限制
     * @param splitLargeHeadings 是否分割大标题（对纯段落分割影响较小）
     * @return 分割后的文件列表
     */
    public List<File> splitByParagraphsOnly(String sourceFilePath, String outputDirPath, int maxWordCount, boolean splitLargeHeadings) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        File outputDir = new File(outputDirPath);
        File sourceFile = new File(sourceFilePath);

        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // 获取原文件名前缀
        String originalFileName = sourceFile.getName();
        String fileNamePrefix = originalFileName.substring(0, originalFileName.lastIndexOf('.'));

        WordprocessingMLPackage wordMLPackage = Docx4J.load(sourceFile);
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();

        // 获取所有段落
        List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);
        List<P> allParagraphs = new ArrayList<>();
        for (Object obj : paragraphs) {
            if (obj instanceof P) {
                allParagraphs.add((P) obj);
            }
        }

        if (allParagraphs.isEmpty()) {
            return resultFiles;
        }

        // 直接按段落分割
        return splitByParagraphs(allParagraphs, outputDir, 1, 1, "document", maxWordCount, wordMLPackage, fileNamePrefix);
    }

    // paragraph is heading1
    private boolean isHeading1(P para) {
        return isHeadingLevel(para, 1);
    }

    // paragraph is heading2
    private boolean isHeading2(P para) {
        return isHeadingLevel(para, 2);
    }

    // paragraph is heading3
    private boolean isHeading3(P para) {
        return isHeadingLevel(para, 3);
    }

    // paragraph is heading4
    private boolean isHeading4(P para) {
        return isHeadingLevel(para, 4);
    }

    // paragraph is heading5
    private boolean isHeading5(P para) {
        return isHeadingLevel(para, 5);
    }

    // paragraph is heading6
    private boolean isHeading6(P para) {
        return isHeadingLevel(para, 6);
    }

    // 通用的标题级别检查方法
    private boolean isHeadingLevel(P para, int level) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            String styleId = para.getPPr().getPStyle().getVal();
            if (styleId == null) return false;

            // 检查标准格式 "Heading1", "Heading2", etc.
            String standardStyle = "Heading" + level;
            if (standardStyle.equals(styleId)) return true;

            // 检查数字格式 "1", "2", "3", "4", "5", "6" (你的文档使用的格式)
            if (String.valueOf(level).equals(styleId)) return true;

            // 检查中文标题格式
            String[] chineseFormats = {
                "标题" + level,           // "标题1"
                "标题 " + level,          // "标题 1"
                "标题" + level + "级",     // "标题1级"
            };

            for (String format : chineseFormats) {
                if (format.equals(styleId)) return true;
            }

            // 检查可能的其他格式变体
            String[] possibleFormats = {
                "heading " + level,        // "heading 1"
                "heading" + level,         // "heading1"
                "Heading " + level,        // "Heading 1"
                "HEADING" + level,         // "HEADING1"
                "HEADING " + level,        // "HEADING 1"
                "h" + level,               // "h1", "h2"
                "H" + level,               // "H1", "H2"
                level + "级标题",           // "1级标题"
                level + "级",              // "1级"
            };

            for (String format : possibleFormats) {
                if (format.equals(styleId)) return true;
            }
        }
        return false;
    }

    // 获取段落的标题级别（如果是标题的话）
    private int getHeadingLevel(P para) {
        for (int level = 1; level <= 6; level++) {
            if (isHeadingLevel(para, level)) {
                return level;
            }
        }
        return 0; // 不是标题
    }

    // 获取段落文本内容
    private String getParagraphText(P para) {
        try {
            // 使用docx4j的TextUtils来提取文本
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 如果TextUtils失败，使用手动方法
        }

        // 手动提取文本的备用方法
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            } else if (runContent instanceof JAXBElement) {
                                JAXBElement<?> jaxbElement = (JAXBElement<?>) runContent;
                                if (jaxbElement.getValue() instanceof org.docx4j.wml.Text) {
                                    org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) jaxbElement.getValue();
                                    if (textElement.getValue() != null) {
                                        text.append(textElement.getValue());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }

    // 计算段落列表的总字数
    private int calculateWordCount(List<P> paragraphs) {
        int totalWords = 0;
        for (P para : paragraphs) {
            String text = getParagraphText(para);
            // 简单的字数统计：去除空白字符后计算长度
            totalWords += text.replaceAll("\\s+", "").length();
        }
        return totalWords;
    }

    // 保存章节到文件
    private void saveChapterToFile(List<P> paragraphs, File outputFile) throws Exception {
        saveChapterToFileWithStyles(paragraphs, outputFile, null);
    }

    // 保存章节到文件（带样式支持）
    private void saveChapterToFileWithStyles(List<P> paragraphs, File outputFile, WordprocessingMLPackage sourcePackage) throws Exception {
        WordprocessingMLPackage newPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart newDocumentPart = newPackage.getMainDocumentPart();

        // 如果有源文档，复制样式信息
        if (sourcePackage != null) {
            copyStylesFromSource(sourcePackage, newPackage);
        }

        // 添加章节内容
        for (P para : paragraphs) {
            P newPara = (P) XmlUtils.deepCopy(para);
            newDocumentPart.addObject(newPara);
        }

        Docx4J.save(newPackage, outputFile);
    }

    // 从源文档复制样式到新文档
    private void copyStylesFromSource(WordprocessingMLPackage sourcePackage, WordprocessingMLPackage targetPackage) {
        try {
            // 复制样式部分
            if (sourcePackage.getMainDocumentPart().getStyleDefinitionsPart() != null) {
                org.docx4j.openpackaging.parts.WordprocessingML.StyleDefinitionsPart sourceStyles =
                    sourcePackage.getMainDocumentPart().getStyleDefinitionsPart();

                // 创建新的样式部分
                org.docx4j.openpackaging.parts.WordprocessingML.StyleDefinitionsPart targetStyles =
                    new org.docx4j.openpackaging.parts.WordprocessingML.StyleDefinitionsPart();

                // 深度复制样式内容
                org.docx4j.wml.Styles styles = (org.docx4j.wml.Styles) XmlUtils.deepCopy(sourceStyles.getContents());
                targetStyles.setContents(styles);

                // 添加到目标文档
                targetPackage.getMainDocumentPart().addTargetPart(targetStyles);
            }

            // 注意：暂时跳过主题复制，因为ThemePart类可能在当前版本中不可用
            // 样式信息已经足够保持段落格式

        } catch (Exception e) {
            System.err.println("复制样式时出错: " + e.getMessage());
            // 即使样式复制失败，也继续保存文档
        }
    }

    /**
     * 处理大章节但不分割标题的情况
     * 当splitLargeHeadings=false时，即使章节超过字数限制，也不会将标题单独分割
     * 而是保持标题和内容的完整性，允许文件超过字数限制
     */
    private List<File> handleLargeChapterWithoutSplittingHeadings(List<P> chapterParagraphs, File outputDir,
                                                                  int chapterIndex, int maxWordCount,
                                                                  WordprocessingMLPackage sourcePackage, String fileNamePrefix) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        String chapterTitle = getChapterTitle(chapterParagraphs, chapterIndex);
        int totalWordCount = calculateWordCount(chapterParagraphs);

        // 检查是否有Heading1标题
        P heading1Para = null;
        for (P para : chapterParagraphs) {
            if (isHeading1(para)) {
                heading1Para = para;
                break;
            }
        }

        // 如果章节超过字数限制，给出警告但仍然保存完整章节
        if (totalWordCount > maxWordCount) {
            System.out.println(String.format("警告: 章节 %d 的总字数(%d)超过限制(%d)，但根据设置不分割大标题，保持章节完整性",
                chapterIndex, totalWordCount, maxWordCount));
        }

        // 保存完整章节，不进行分割
        String fileName = String.format("%s_chapter_%02d_%s.docx",
                fileNamePrefix,
                chapterIndex,
                chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
        File chapterFile = new File(outputDir, fileName);

        saveChapterToFileWithStyles(chapterParagraphs, chapterFile, sourcePackage);
        resultFiles.add(chapterFile);

        return resultFiles;
    }

    // 按Heading2进一步分割章节
    private List<File> splitChapterByHeading2(List<P> chapterParagraphs, File outputDir,
                                              int chapterIndex, int maxWordCount, WordprocessingMLPackage sourcePackage, String fileNamePrefix) throws Exception {
        return splitChapterByHeading2(chapterParagraphs, outputDir, chapterIndex, maxWordCount, sourcePackage, fileNamePrefix, true);
    }

    // 按Heading2进一步分割章节（带大标题分割控制）
    private List<File> splitChapterByHeading2(List<P> chapterParagraphs, File outputDir,
                                              int chapterIndex, int maxWordCount, WordprocessingMLPackage sourcePackage,
                                              String fileNamePrefix, boolean splitLargeHeadings) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        Map<Integer, List<P>> subChapterMap = new LinkedHashMap<>();
        int subChapterIndex = 0;
        String chapterTitle = getChapterTitle(chapterParagraphs, chapterIndex);

        // 按Heading2分割
        boolean hasHeading2 = false;
        for (P para : chapterParagraphs) {
            if (isHeading2(para)) {
                hasHeading2 = true;
                subChapterIndex++;
            }

            if (subChapterIndex == 0) {
                // 如果还没有遇到Heading2，归入第一个子章节
                subChapterIndex = 1;
            }

            subChapterMap.computeIfAbsent(subChapterIndex, k -> new ArrayList<>()).add(para);
        }

        // 如果没有找到Heading2，根据splitLargeHeadings参数决定处理方式
        if (!hasHeading2) {
            if (splitLargeHeadings) {
                // 允许分割大标题：直接按段落分割
                return splitByParagraphs(chapterParagraphs, outputDir, chapterIndex, 1, chapterTitle, maxWordCount, sourcePackage, fileNamePrefix);
            } else {
                // 不分割大标题：尝试保持内容完整性
                return handleLargeChapterWithoutSplittingHeadings(chapterParagraphs, outputDir, chapterIndex, maxWordCount, sourcePackage, fileNamePrefix);
            }
        }

        // 处理每个子章节
        for (Map.Entry<Integer, List<P>> entry : subChapterMap.entrySet()) {
            List<P> subChapterParagraphs = entry.getValue();
            int wordCount = calculateWordCount(subChapterParagraphs);

            if (wordCount <= maxWordCount) {
                // 字数未超过限制，直接保存
                String subChapterTitle = getSubChapterTitle(subChapterParagraphs);
                String fileName = String.format("%s_chapter_%02d_%02d_%s_%s.docx",
                        fileNamePrefix,
                        chapterIndex, entry.getKey(),
                        chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"),
                        subChapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                File subChapterFile = new File(outputDir, fileName);

                saveChapterToFileWithStyles(subChapterParagraphs, subChapterFile, sourcePackage);
                resultFiles.add(subChapterFile);
            } else {
                // 字数仍然超过限制，根据splitLargeHeadings参数决定处理方式
                if (splitLargeHeadings) {
                    // 允许分割大标题：按段落进一步分割
                    List<File> fragmentFiles = splitByParagraphs(subChapterParagraphs, outputDir,
                            chapterIndex, entry.getKey(), chapterTitle, maxWordCount, sourcePackage, fileNamePrefix);
                    resultFiles.addAll(fragmentFiles);
                } else {
                    // 不分割大标题：保持子章节完整性，即使超过字数限制
                    String subChapterTitle = getSubChapterTitle(subChapterParagraphs);
                    int subChapterWordCount = calculateWordCount(subChapterParagraphs);

                    if (subChapterWordCount > maxWordCount) {
                        System.out.println(String.format("警告: 章节 %d.%d 的字数(%d)超过限制(%d)，但根据设置不分割大标题，保持子章节完整性",
                            chapterIndex, entry.getKey(), subChapterWordCount, maxWordCount));
                    }

                    // 保存完整子章节，不进行分割
                    String fileName = String.format("%s_chapter_%02d_%02d_%s_%s.docx",
                            fileNamePrefix,
                            chapterIndex, entry.getKey(),
                            chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"),
                            subChapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                    File subChapterFile = new File(outputDir, fileName);

                    saveChapterToFileWithStyles(subChapterParagraphs, subChapterFile, sourcePackage);
                    resultFiles.add(subChapterFile);
                }
            }
        }

        return resultFiles;
    }

    // 获取章节标题（处理没有标题的情况）
    private String getChapterTitle(List<P> paragraphs, int chapterIndex) {
        // 首先查找Heading1
        for (P para : paragraphs) {
            if (isHeading1(para)) {
                String title = getParagraphText(para);
                return title.isEmpty() ? "chapter_" + chapterIndex : title;
            }
        }

        // 如果没有Heading1，使用第一个有内容的段落作为标题
        for (P para : paragraphs) {
            String text = getParagraphText(para).trim();
            if (!text.isEmpty()) {
                // 限制标题长度，避免文件名过长
                String title = text.length() > 30 ? text.substring(0, 30) + "..." : text;
                return title.isEmpty() ? "content_" + chapterIndex : title;
            }
        }

        return "chapter_" + chapterIndex;
    }

    // 获取子章节标题
    private String getSubChapterTitle(List<P> paragraphs) {
        for (P para : paragraphs) {
            if (isHeading2(para)) {
                String title = getParagraphText(para);
                return title.isEmpty() ? "section" : title;
            }
        }
        // 如果没有Heading2，使用第一个段落的部分内容作为标题
        for (P para : paragraphs) {
            String text = getParagraphText(para).trim();
            if (!text.isEmpty()) {
                String title = text.length() > 20 ? text.substring(0, 20) + "..." : text;
                return title.isEmpty() ? "section" : title;
            }
        }
        return "section";
    }

    // 按段落分割（当Heading2分割后仍然超过字数限制时）
    private List<File> splitByParagraphs(List<P> paragraphs, File outputDir,
                                        int chapterIndex, int subChapterIndex,
                                        String chapterTitle, int maxWordCount, WordprocessingMLPackage sourcePackage, String fileNamePrefix) throws Exception {
        List<File> resultFiles = new ArrayList<>();
        List<P> currentFragment = new ArrayList<>();
        int currentWordCount = 0;
        int fragmentIndex = 1;

        for (P para : paragraphs) {
            String paraText = getParagraphText(para);
            int paraWordCount = paraText.replaceAll("\\s+", "").length();

            // 如果单个段落就超过了字数限制
            if (paraWordCount > maxWordCount) {
                // 先保存当前片段（如果有内容）
                if (!currentFragment.isEmpty()) {
                    String fileName = String.format("%s_chapter_%02d_%02d_%02d_%s.docx",
                            fileNamePrefix,
                            chapterIndex, subChapterIndex, fragmentIndex,
                            chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                    File fragmentFile = new File(outputDir, fileName);

                    saveChapterToFileWithStyles(currentFragment, fragmentFile, sourcePackage);
                    resultFiles.add(fragmentFile);

                    currentFragment.clear();
                    currentWordCount = 0;
                    fragmentIndex++;
                }

                // 单独保存这个超长段落
                List<P> singleParaList = new ArrayList<>();
                singleParaList.add(para);
                String fileName = String.format("%s_chapter_%02d_%02d_%02d_%s_long_para.docx",
                        fileNamePrefix,
                        chapterIndex, subChapterIndex, fragmentIndex,
                        chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                File fragmentFile = new File(outputDir, fileName);

                saveChapterToFileWithStyles(singleParaList, fragmentFile, sourcePackage);
                resultFiles.add(fragmentFile);
                fragmentIndex++;
                continue;
            }

            // 如果加上当前段落会超过限制，先保存当前片段
            if (currentWordCount + paraWordCount > maxWordCount && !currentFragment.isEmpty()) {
                String fileName = String.format("%s_chapter_%02d_%02d_%02d_%s.docx",
                        fileNamePrefix,
                        chapterIndex, subChapterIndex, fragmentIndex,
                        chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
                File fragmentFile = new File(outputDir, fileName);

                saveChapterToFileWithStyles(currentFragment, fragmentFile, sourcePackage);
                resultFiles.add(fragmentFile);

                // 重置当前片段
                currentFragment.clear();
                currentWordCount = 0;
                fragmentIndex++;
            }

            currentFragment.add(para);
            currentWordCount += paraWordCount;
        }

        // 保存最后一个片段
        if (!currentFragment.isEmpty()) {
            String fileName = String.format("%s_chapter_%02d_%02d_%02d_%s.docx",
                    fileNamePrefix,
                    chapterIndex, subChapterIndex, fragmentIndex,
                    chapterTitle.replaceAll("[\\\\/:*?\"<>|]", "_"));
            File fragmentFile = new File(outputDir, fileName);

            saveChapterToFileWithStyles(currentFragment, fragmentFile, sourcePackage);
            resultFiles.add(fragmentFile);
        }

        return resultFiles;
    }

    // 获取文档中所有指定类型的元素 (改进版，正确获取所有段落)
    private List<Object> getAllElementsFromObject(Object obj, Class<?> toSearch) {
        List<Object> result = new ArrayList<>();

        if (obj == null) return result;

        // 使用docx4j的TraversalUtil来安全地遍历所有元素
        if (obj instanceof MainDocumentPart) {
            MainDocumentPart documentPart = (MainDocumentPart) obj;
            try {
                // 使用docx4j的内置遍历方法
                org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                    @Override
                    public List<Object> apply(Object o) {
                        if (toSearch.isInstance(o)) {
                            result.add(o);
                        }
                        return null;
                    }
                });
            } catch (Exception e) {
                // 如果TraversalUtil失败，使用简单方法
                List<Object> content = documentPart.getContent();
                for (Object item : content) {
                    if (toSearch.isInstance(item)) {
                        result.add(item);
                    }
                }
            }
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                if (toSearch.isInstance(item)) {
                    result.add(item);
                }
            }
        } else if (toSearch.isInstance(obj)) {
            result.add(obj);
        }

        return result;
    }
}
