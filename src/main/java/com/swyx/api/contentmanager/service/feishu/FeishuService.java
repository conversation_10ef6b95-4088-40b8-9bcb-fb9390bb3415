package com.swyx.api.contentmanager.service.feishu;

import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.UserEntity;
import com.swyx.api.contentmanager.dto.UserInfo;
import com.swyx.api.contentmanager.dto.UserInfoResponse;
import com.swyx.api.contentmanager.service.common.HttpCilentService;
import com.swyx.api.contentmanager.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FeishuService {

    @Autowired
    private HttpCilentService httpCilentService;

    @Autowired
    private UserService userService;

    public UserInfoResponse login(String tempCode) {
        String authUrl = "https://passport.feishu.cn/suite/passport/oauth/token";
        String userInfoUrl = "https://open.feishu.cn/open-apis/authen/v1/user_info";
        String feishuClientId = "cli_a8cc1a00fb6b100e";
        String feishuClientSecret = "ZDy03uqZLcbkkb9OFEAdZdDZBUUQuxaP";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject requestBody = new JSONObject();
        requestBody.put("client_id", feishuClientId);
        requestBody.put("client_secret", feishuClientSecret);
        requestBody.put("grant_type", "authorization_code");
        requestBody.put("code", tempCode);

        try {
            JSONObject tokenRes = httpCilentService.post(authUrl, headers, requestBody, JSONObject.class);
            log.info("feishu token res:{}", tokenRes);
            String accessToken = (String) tokenRes.get("access_token");
            String refreshToken = (String) tokenRes.get("refresh_token");

            HttpHeaders userInfoHeader = new HttpHeaders();
            userInfoHeader.setBearerAuth(accessToken);
            JSONObject userInfoDataRes = httpCilentService.get(userInfoUrl, userInfoHeader, JSONObject.class);
            log.info("feishu user res:{}", userInfoDataRes);
            JSONObject userInfoRes = (JSONObject) userInfoDataRes.getJSONObject("data");
            UserInfoResponse userInfoResponse = new UserInfoResponse();

            UserEntity userEntity = new UserEntity();
            userEntity.setUserId(userInfoRes.getString("union_id"));
            userEntity.setAvatar(userInfoRes.getString("avatar_url"));
            userEntity.setName(userInfoRes.getString("name"));
            userEntity.setMobile(userInfoRes.getString("mobile"));
            userEntity.setEnName(userInfoRes.getString("en_name"));
            userEntity.setEmail(userInfoRes.getString("email"));
            userEntity.setOpenId(userInfoRes.getString("open_id"));
            userEntity.setStatus("active");
            userService.saveOrUpdateUser(userEntity);
            BeanUtils.copyProperties(userEntity, userInfoResponse);

            return userInfoResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
