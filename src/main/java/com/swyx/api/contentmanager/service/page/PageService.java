package com.swyx.api.contentmanager.service.page;

import com.swyx.api.contentmanager.dao.PageEntity;
import com.swyx.api.contentmanager.dao.PageEntityExample;
import com.swyx.api.contentmanager.dao.mapper.PageEntityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PageService {
    @Autowired
    PageEntityMapper pageEntityMapper;

    @Transactional(rollbackFor = Exception.class)
    public void savePages(List<PageEntity> pageEntities) {
        if (pageEntities == null || pageEntities.isEmpty()) {
            return;
        }
        
        for (PageEntity pageEntity : pageEntities) {
            // 插入页面数据
            pageEntityMapper.insert(pageEntity);
        }
    }
}
