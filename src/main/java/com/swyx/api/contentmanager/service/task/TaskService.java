package com.swyx.api.contentmanager.service.task;

import ch.qos.logback.core.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs;
import com.swyx.api.contentmanager.dao.TaskEntity;
import com.swyx.api.contentmanager.dao.TaskEntityExample;
import com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs;
import com.swyx.api.contentmanager.dao.mapper.TaskEntityMapper;
import com.swyx.api.contentmanager.dto.TaskResponse;
import com.swyx.api.contentmanager.service.ai.AIService;
import com.swyx.api.contentmanager.service.ai.OpenAIService;
import com.swyx.api.contentmanager.service.book.TextParserService;
import com.swyx.api.contentmanager.service.common.AsyncExService;
import com.swyx.api.contentmanager.service.project.ProjectService;
import com.swyx.api.contentmanager.utils.FileUtil;
import com.swyx.api.contentmanager.utils.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TaskService {
    @Autowired
    private TaskEntityMapper taskEntityMapper;

    @Autowired
    private TextParserService textParserService;

    @Autowired
    private AsyncExService asyncExService;

    @Autowired
    private AIService aIService;
    @Autowired
    private ProjectService projectService;

    public List<TaskEntity> getTasks() {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        return taskEntityMapper.selectByExample(example);
    }

    public List<TaskEntity> getTasksByProjectId(String projectId) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        return taskEntityMapper.selectByExample(example);
    }

    public List<TaskEntity> getTasksByUserId(String userId) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andReviewerIdEqualTo(userId);
        return taskEntityMapper.selectByExample(example);
    }

    public long getTaskCountByUserId(String userId) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andReviewerIdEqualTo(userId);
        return taskEntityMapper.countByExample(example);
    }

    public TaskEntity getTaskEntityById(String taskId) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        List<TaskEntity> tasks = taskEntityMapper.selectByExample(example);
        if (tasks == null || tasks.isEmpty()) {
            return null;
        }
        return tasks.getFirst();
    }

    public TaskResponse getTaskById(String id) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(id);
        List<TaskEntityWithBLOBs> tasks = taskEntityMapper.selectByExampleWithBLOBs(example);
        if (tasks == null || tasks.isEmpty()) {
            return null;
        }
        TaskEntityWithBLOBs task = tasks.getFirst();
        List<AIIssueEntityWithBLOBs> aiIssueEntityWithBLOBs = aIService.getIssuesByTaskId(id);
        TaskResponse taskResponse = new TaskResponse();
        BeanUtils.copyProperties(task, taskResponse);
        taskResponse.setIssues(aiIssueEntityWithBLOBs);

        return taskResponse;
    }

    public void updateTask(TaskEntity task) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(task.getTaskId());
        if (task instanceof TaskEntityWithBLOBs) {
            taskEntityMapper.updateByExampleSelective((TaskEntityWithBLOBs) task, example);
        } else  {
            taskEntityMapper.updateByExample(task, example);
        }
    }

    public void updateTaskCheckFile(String taskId, String fileUrl) {
        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        TaskEntity task = taskEntityMapper.selectByExample(example).getFirst();
        if (task == null) {
            return;
        }
        task.setCheckFileUrl(fileUrl);
        taskEntityMapper.updateByExample(task, example);
    }

    public void saveTask(TaskEntity taskEntity) {
        TaskEntityWithBLOBs task = (TaskEntityWithBLOBs) taskEntity;

        String id = IdGenerator.generateRadomId();
        task.setTaskId("TASK-" + id);
        task.setStatus((byte)0);

        String projectId = taskEntity.getProjectId();
        String projectName = projectService.getProjectById(projectId).getProjectName();
        task.setProjectName(projectName);

        String bookUrl = taskEntity.getBookUrl();
        if (StringUtils.isNotBlank(bookUrl))  {
            JSONObject parseObject = textParserService.parseTaskBookContent(bookUrl);
            JSONObject result = parseObject.getJSONObject("result");
            String markdown = result.getString("markdown");
            task.setContent(markdown);
        }

        taskEntityMapper.insert(task);

        asyncExService.doParseContent(task);
    }

    public void saveTasks(List<TaskEntityWithBLOBs> taskEntities) {
        for (TaskEntity taskEntity : taskEntities) {
            saveTask(taskEntity);
        }
    }

    public String taskAudit(String params) {
        JSONObject object = JSONObject.parseObject(params);
        String taskId = object.getString("taskId");
        String modelId = object.getString("modelId");
        String promptId = object.getString("promptId");
        String promptText = object.getString("promptText");
        String promptType = object.getString("promptType");
        if (!StringUtils.isBlank(promptText) && !StringUtils.isBlank(promptType)) {
            promptId = aIService.savePrompt(promptText, promptType).getPromptId();
        }

        TaskEntityExample example = new TaskEntityExample();
        TaskEntityExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        TaskEntityWithBLOBs task = taskEntityMapper.selectByExampleWithBLOBs(example).getFirst();
        if (task == null) {
            return "任务异常";
        }
        if (task.getContent() == null && task.getFileId() == null) {
            return "文件内容为空，请上传文件后再试";
        }
        if (!StringUtils.isBlank(promptId) && !StringUtils.isBlank(modelId)) {
            task.setPromptId(promptId);
            task.setModelId(modelId);
            updateTask(task);
        }
        if (task.getModelId() == null) {
            return "请选择模型";
        }
        if (task.getPromptId() == null) {
            return "请选择提示词";
        }

        asyncExService.doParseContent(task);
        return "审核中";
    }
}
