package com.swyx.api.contentmanager.service.user;

import com.swyx.api.contentmanager.dao.*;
import com.swyx.api.contentmanager.dao.mapper.*;
import com.swyx.api.contentmanager.dto.UserInfoResponse;
import com.swyx.api.contentmanager.dto.UserRolePermissionDTO;
import com.swyx.api.contentmanager.service.task.TaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class UserService {

    @Autowired
    private UserEntityMapper userEntityMapper;

    @Autowired
    private TaskService taskService;

    @Autowired
    private UserRolePermissionCustomMapper userRolePermissionCustomMapper;

    public void saveUser(UserEntity userEntity) {
        userEntityMapper.insertSelective(userEntity);
    }
    
    /**
     * 保存或更新用户信息
     * 如果存在相同的userId，则更新数据库记录
     * 如果不存在，则插入新记录
     *
     * @param userEntity 用户实体
     */
    public void saveOrUpdateUser(UserEntity userEntity) {
        if (userEntity == null || userEntity.getUserId() == null) {
            return;
        }
        
        // 查询是否存在相同userId的用户
        UserEntity existingUser = userEntityMapper.selectByPrimaryKey(userEntity.getUserId());
        
        if (existingUser != null) {
            // 存在则更新
            userEntityMapper.updateByPrimaryKeySelective(userEntity);
        } else {
            // 不存在则插入
            userEntityMapper.insertSelective(userEntity);
        }
    }

    public List<UserInfoResponse> getUsers() {
        UserEntityExample example = new UserEntityExample();
        UserEntityExample.Criteria criteria = example.createCriteria();
        List<UserEntity> users = userEntityMapper.selectByExample(example);
        List<UserInfoResponse> userInfoResponses = new ArrayList<>();
        for (UserEntity user : users) {
            UserInfoResponse userInfoResponse = getUserRoleAndPermissonsByEntity(user);

            long taskCount = taskService.getTaskCountByUserId(user.getUserId());
            userInfoResponse.setTaskCount(taskCount);

            userInfoResponses.add(userInfoResponse);
        }

        return userInfoResponses;
    }

    public List<UserEntity> getUsersByIds(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }

        UserEntityExample example = new UserEntityExample();
        UserEntityExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdIn(userIds);
        return userEntityMapper.selectByExample(example);
    }

    public UserEntity getUserById(String id) {
        UserEntityExample example = new UserEntityExample();
        UserEntityExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(id);
        return userEntityMapper.selectByExample(example).getFirst();
    }

    /**
     * 根据用户名和密码查询用户
     */
    public UserEntity getUserByUsernameAndPassword(String username, String password) {
        UserEntityExample example = new UserEntityExample();
        UserEntityExample.Criteria criteria = example.createCriteria();
        criteria.andUsernameEqualTo(username);
        criteria.andPasswordEqualTo(password);
        List<UserEntity> users = userEntityMapper.selectByExample(example);
        if (users != null && !users.isEmpty()) {
            return users.getFirst();
        }
        return null;
    }

    public UserInfoResponse getUserRoleAndPermissonsByEntity(UserEntity userEntity) {
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        List<UserRolePermissionDTO> dtos = userRolePermissionCustomMapper.selectRolesAndPermissionsByUserId(userEntity.getUserId());

        Set<String> roles = new HashSet<>();
        Set<String> permissions = new HashSet<>();
        for (UserRolePermissionDTO dto : dtos) {
            roles.add(dto.getRoleCode());
            if (dto.getPermissionName() != null) {
                permissions.add(dto.getPermissionName());
            }
        }

        BeanUtils.copyProperties(userEntity, userInfoResponse);
        userInfoResponse.setRoles(new ArrayList<>(roles));
        userInfoResponse.setPermissions(new ArrayList<>(permissions));
        return userInfoResponse;
    }
}
