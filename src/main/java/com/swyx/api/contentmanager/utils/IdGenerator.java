package com.swyx.api.contentmanager.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

public class IdGenerator {
    public static String generateRadomId() {
        // 1. 获取当前日期并格式化
        String datePart = LocalDate.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 2. 生成6位随机数（范围：100000-999999）
        int randomNum = ThreadLocalRandom.current()
                .nextInt(100000, 1000000);

        // 3. 拼接最终ID
        return datePart + randomNum;
    }
}
