spring:
  application:
    name: ContentManager
  datasource:
    url: *************************************************************************************
    username: admin
    password: Admin123!
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 50MB # 单个文件最大大小（默认1MB）
      max-request-size: 50MB # 总请求最大大小（默认10MB）
      location: ./uploads # 临时文件存储位置（相对路径）
  main:
    allow-circular-references: true

server:
  servlet:
    context-path: /api

file:
  upload-dir: /tmp

# MyBatis ??
mybatis:
  mapper-locations: classpath:mybatis/*.xml # XML ??????
  type-aliases-package: com.swyx.api.contentmanager.dao # ??????
  configuration:
    map-underscore-to-camel-case: true # ??????