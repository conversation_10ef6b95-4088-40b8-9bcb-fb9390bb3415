<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" strict="true">
    <Properties>
        <Property name="LOG_HOME">～/logs/</Property>
        <Property name="ENCODING">utf-8</Property>
        <Property name="FIELD_FILTER">{"_com_mysql_success":[{"fieldName":"args", "type":"SQL_REPLACE_SELECT_FIELDS"}]}</Property>
    </Properties>
    <Appenders>
        <!-- 定义控制台输出 -->
        <Console name="CONSOLE-LOG" target="SYSTEM_OUT" follow="true">
            <LoanLayout/>
        </Console>
        <!-- 业务日志 -->
        <RollingRandomAccessFile name="BUSINESS-LOG" fileName="${LOG_HOME}/business.log"
                                 filePattern="${LOG_HOME}/business.log.%d{yyyyMMddHH}">
            <LoanLayout/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
        <!-- ERROR日志 -->
        <RollingRandomAccessFile name="ERROR-LOG" fileName="${LOG_HOME}/error.log"
                                 filePattern="${LOG_HOME}/error.log.%d{yyyyMMddHH}">
            <LoanLayout/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <asyncLogger name="com.xiaoju.manhattan.inf.wormhole.server.thrift.processor" level="error" additivity="false" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncLogger>
        <asyncLogger name="com.xiaoju.manhattan.inf.wormhole.control" level="error" additivity="false" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncLogger>
        <asyncLogger name="com.xiaoju.onekey" level="error" additivity="false" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncLogger>
        <asyncLogger name="com.xiaoju" level="debug" additivity="false" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncLogger>
        <asyncLogger name="com.xiaoju.manhattan.dirns.api.utils.client" level="error" additivity="false" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncLogger>
        <asyncRoot level="info" includeLocation="true">
            <AppenderRef ref="BUSINESS-LOG"/>
            <AppenderRef ref="ERROR-LOG"/>
        </asyncRoot>
    </Loggers>
</Configuration>