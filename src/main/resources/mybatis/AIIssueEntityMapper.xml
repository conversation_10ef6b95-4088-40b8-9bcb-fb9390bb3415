<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.AIIssueEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.AIIssueEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="issue_id" jdbcType="VARCHAR" property="issueId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="prompt_id" jdbcType="VARCHAR" property="promptId" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="issue_type" jdbcType="VARCHAR" property="issueType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="position" jdbcType="VARCHAR" property="position" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <result column="origin_text" jdbcType="LONGVARCHAR" property="originText" />
    <result column="short_suggestion" jdbcType="LONGVARCHAR" property="shortSuggestion" />
    <result column="full_suggestion" jdbcType="LONGVARCHAR" property="fullSuggestion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    id, issue_id, project_id, task_id, prompt_id, model_id, issue_type, status, position
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    origin_text, short_suggestion, full_suggestion
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_issue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_issue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_issue
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    delete from ai_issue
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_issue (issue_id, project_id, task_id, 
      prompt_id, model_id, issue_type, 
      status, position, origin_text, 
      short_suggestion, full_suggestion
      )
    values (#{issueId,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, 
      #{promptId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{issueType,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{position,jdbcType=VARCHAR}, #{originText,jdbcType=LONGVARCHAR}, 
      #{shortSuggestion,jdbcType=LONGVARCHAR}, #{fullSuggestion,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_issue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueId != null">
        issue_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="promptId != null">
        prompt_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="issueType != null">
        issue_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="originText != null">
        origin_text,
      </if>
      <if test="shortSuggestion != null">
        short_suggestion,
      </if>
      <if test="fullSuggestion != null">
        full_suggestion,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueId != null">
        #{issueId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="promptId != null">
        #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="issueType != null">
        #{issueType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="originText != null">
        #{originText,jdbcType=LONGVARCHAR},
      </if>
      <if test="shortSuggestion != null">
        #{shortSuggestion,jdbcType=LONGVARCHAR},
      </if>
      <if test="fullSuggestion != null">
        #{fullSuggestion,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    select count(*) from ai_issue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.issueId != null">
        issue_id = #{row.issueId,jdbcType=VARCHAR},
      </if>
      <if test="row.projectId != null">
        project_id = #{row.projectId,jdbcType=VARCHAR},
      </if>
      <if test="row.taskId != null">
        task_id = #{row.taskId,jdbcType=VARCHAR},
      </if>
      <if test="row.promptId != null">
        prompt_id = #{row.promptId,jdbcType=VARCHAR},
      </if>
      <if test="row.modelId != null">
        model_id = #{row.modelId,jdbcType=VARCHAR},
      </if>
      <if test="row.issueType != null">
        issue_type = #{row.issueType,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.position != null">
        position = #{row.position,jdbcType=VARCHAR},
      </if>
      <if test="row.originText != null">
        origin_text = #{row.originText,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.shortSuggestion != null">
        short_suggestion = #{row.shortSuggestion,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.fullSuggestion != null">
        full_suggestion = #{row.fullSuggestion,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    set id = #{row.id,jdbcType=BIGINT},
      issue_id = #{row.issueId,jdbcType=VARCHAR},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      task_id = #{row.taskId,jdbcType=VARCHAR},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      model_id = #{row.modelId,jdbcType=VARCHAR},
      issue_type = #{row.issueType,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      position = #{row.position,jdbcType=VARCHAR},
      origin_text = #{row.originText,jdbcType=LONGVARCHAR},
      short_suggestion = #{row.shortSuggestion,jdbcType=LONGVARCHAR},
      full_suggestion = #{row.fullSuggestion,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    set id = #{row.id,jdbcType=BIGINT},
      issue_id = #{row.issueId,jdbcType=VARCHAR},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      task_id = #{row.taskId,jdbcType=VARCHAR},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      model_id = #{row.modelId,jdbcType=VARCHAR},
      issue_type = #{row.issueType,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      position = #{row.position,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    <set>
      <if test="issueId != null">
        issue_id = #{issueId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="promptId != null">
        prompt_id = #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="issueType != null">
        issue_type = #{issueType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="originText != null">
        origin_text = #{originText,jdbcType=LONGVARCHAR},
      </if>
      <if test="shortSuggestion != null">
        short_suggestion = #{shortSuggestion,jdbcType=LONGVARCHAR},
      </if>
      <if test="fullSuggestion != null">
        full_suggestion = #{fullSuggestion,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    set issue_id = #{issueId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      issue_type = #{issueType,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      position = #{position,jdbcType=VARCHAR},
      origin_text = #{originText,jdbcType=LONGVARCHAR},
      short_suggestion = #{shortSuggestion,jdbcType=LONGVARCHAR},
      full_suggestion = #{fullSuggestion,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.AIIssueEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 15 09:46:50 CST 2025.
    -->
    update ai_issue
    set issue_id = #{issueId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      issue_type = #{issueType,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      position = #{position,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>