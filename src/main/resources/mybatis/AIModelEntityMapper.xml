<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.AIModelEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.AIModelEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="apiurl" jdbcType="VARCHAR" property="apiurl" />
    <result column="orgnizer" jdbcType="VARCHAR" property="orgnizer" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    id, model_id, name, token, apiurl, orgnizer, created_time, updated_time
  </sql>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.AIModelEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from ai_model
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    delete from ai_model
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.AIModelEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_model (model_id, name, token, 
      apiurl, orgnizer, created_time, 
      updated_time)
    values (#{modelId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, 
      #{apiurl,jdbcType=VARCHAR}, #{orgnizer,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.AIModelEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        model_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="apiurl != null">
        apiurl,
      </if>
      <if test="orgnizer != null">
        orgnizer,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="apiurl != null">
        #{apiurl,jdbcType=VARCHAR},
      </if>
      <if test="orgnizer != null">
        #{orgnizer,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.AIModelEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    select count(*) from ai_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    update ai_model
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.modelId != null">
        model_id = #{row.modelId,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.token != null">
        token = #{row.token,jdbcType=VARCHAR},
      </if>
      <if test="row.apiurl != null">
        apiurl = #{row.apiurl,jdbcType=VARCHAR},
      </if>
      <if test="row.orgnizer != null">
        orgnizer = #{row.orgnizer,jdbcType=VARCHAR},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedTime != null">
        updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    update ai_model
    set id = #{row.id,jdbcType=BIGINT},
      model_id = #{row.modelId,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      token = #{row.token,jdbcType=VARCHAR},
      apiurl = #{row.apiurl,jdbcType=VARCHAR},
      orgnizer = #{row.orgnizer,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.AIModelEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    update ai_model
    <set>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="apiurl != null">
        apiurl = #{apiurl,jdbcType=VARCHAR},
      </if>
      <if test="orgnizer != null">
        orgnizer = #{orgnizer,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.AIModelEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 13 16:24:39 CST 2025.
    -->
    update ai_model
    set model_id = #{modelId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      apiurl = #{apiurl,jdbcType=VARCHAR},
      orgnizer = #{orgnizer,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>