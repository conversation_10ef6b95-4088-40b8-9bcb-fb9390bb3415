<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.AIPromptEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="prompt_id" jdbcType="VARCHAR" property="promptId" />
    <result column="score" jdbcType="REAL" property="score" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <result column="text" jdbcType="LONGVARCHAR" property="text" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    id, prompt_id, score, count, type
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    text
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntityExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_prompt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_prompt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_prompt
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    delete from ai_prompt
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_prompt (prompt_id, score, count, 
      type, text)
    values (#{promptId,jdbcType=VARCHAR}, #{score,jdbcType=REAL}, #{count,jdbcType=INTEGER}, 
      #{type,jdbcType=VARCHAR}, #{text,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_prompt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="promptId != null">
        prompt_id,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="text != null">
        text,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="promptId != null">
        #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=REAL},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="text != null">
        #{text,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    select count(*) from ai_prompt
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.promptId != null">
        prompt_id = #{row.promptId,jdbcType=VARCHAR},
      </if>
      <if test="row.score != null">
        score = #{row.score,jdbcType=REAL},
      </if>
      <if test="row.count != null">
        count = #{row.count,jdbcType=INTEGER},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.text != null">
        text = #{row.text,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    set id = #{row.id,jdbcType=BIGINT},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      score = #{row.score,jdbcType=REAL},
      count = #{row.count,jdbcType=INTEGER},
      type = #{row.type,jdbcType=VARCHAR},
      text = #{row.text,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    set id = #{row.id,jdbcType=BIGINT},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      score = #{row.score,jdbcType=REAL},
      count = #{row.count,jdbcType=INTEGER},
      type = #{row.type,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    <set>
      <if test="promptId != null">
        prompt_id = #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=REAL},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="text != null">
        text = #{text,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    set prompt_id = #{promptId,jdbcType=VARCHAR},
      score = #{score,jdbcType=REAL},
      count = #{count,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      text = #{text,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.AIPromptEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 14 09:07:01 CST 2025.
    -->
    update ai_prompt
    set prompt_id = #{promptId,jdbcType=VARCHAR},
      score = #{score,jdbcType=REAL},
      count = #{count,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>