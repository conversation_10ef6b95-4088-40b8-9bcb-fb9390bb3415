<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.BookEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.BookEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_id" jdbcType="VARCHAR" property="bookId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="author" jdbcType="VARCHAR" property="author" />
    <result column="isbn" jdbcType="VARCHAR" property="isbn" />
    <result column="total_pages" jdbcType="INTEGER" property="totalPages" />
    <result column="downloadurl" jdbcType="VARCHAR" property="downloadurl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    id, book_id, title, author, isbn, total_pages, downloadurl
  </sql>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.BookEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from book
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    delete from book
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.BookEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book (book_id, title, author, 
      isbn, total_pages, downloadurl
      )
    values (#{bookId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, 
      #{isbn,jdbcType=VARCHAR}, #{totalPages,jdbcType=INTEGER}, #{downloadurl,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.BookEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        book_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="author != null">
        author,
      </if>
      <if test="isbn != null">
        isbn,
      </if>
      <if test="totalPages != null">
        total_pages,
      </if>
      <if test="downloadurl != null">
        downloadurl,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        #{bookId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="isbn != null">
        #{isbn,jdbcType=VARCHAR},
      </if>
      <if test="totalPages != null">
        #{totalPages,jdbcType=INTEGER},
      </if>
      <if test="downloadurl != null">
        #{downloadurl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.BookEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    select count(*) from book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    update book
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.bookId != null">
        book_id = #{row.bookId,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.author != null">
        author = #{row.author,jdbcType=VARCHAR},
      </if>
      <if test="row.isbn != null">
        isbn = #{row.isbn,jdbcType=VARCHAR},
      </if>
      <if test="row.totalPages != null">
        total_pages = #{row.totalPages,jdbcType=INTEGER},
      </if>
      <if test="row.downloadurl != null">
        downloadurl = #{row.downloadurl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    update book
    set id = #{row.id,jdbcType=BIGINT},
      book_id = #{row.bookId,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      author = #{row.author,jdbcType=VARCHAR},
      isbn = #{row.isbn,jdbcType=VARCHAR},
      total_pages = #{row.totalPages,jdbcType=INTEGER},
      downloadurl = #{row.downloadurl,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.BookEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    update book
    <set>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="isbn != null">
        isbn = #{isbn,jdbcType=VARCHAR},
      </if>
      <if test="totalPages != null">
        total_pages = #{totalPages,jdbcType=INTEGER},
      </if>
      <if test="downloadurl != null">
        downloadurl = #{downloadurl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.BookEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 14:43:29 CST 2025.
    -->
    update book
    set book_id = #{bookId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      isbn = #{isbn,jdbcType=VARCHAR},
      total_pages = #{totalPages,jdbcType=INTEGER},
      downloadurl = #{downloadurl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>