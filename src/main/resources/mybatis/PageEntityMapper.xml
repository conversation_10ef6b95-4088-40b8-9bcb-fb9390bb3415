<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.PageEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="page_id" jdbcType="VARCHAR" property="pageId" />
    <result column="book_id" jdbcType="VARCHAR" property="bookId" />
    <result column="page_num" jdbcType="INTEGER" property="pageNum" />
    <result column="content_hash" jdbcType="CHAR" property="contentHash" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    id, page_id, book_id, page_num, content_hash
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.PageEntityExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from page_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.PageEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from page_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from page_content
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    delete from page_content
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into page_content (page_id, book_id, page_num, 
      content_hash, content)
    values (#{pageId,jdbcType=VARCHAR}, #{bookId,jdbcType=VARCHAR}, #{pageNum,jdbcType=INTEGER}, 
      #{contentHash,jdbcType=CHAR}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into page_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="pageNum != null">
        page_num,
      </if>
      <if test="contentHash != null">
        content_hash,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=VARCHAR},
      </if>
      <if test="pageNum != null">
        #{pageNum,jdbcType=INTEGER},
      </if>
      <if test="contentHash != null">
        #{contentHash,jdbcType=CHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.PageEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    select count(*) from page_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.pageId != null">
        page_id = #{row.pageId,jdbcType=VARCHAR},
      </if>
      <if test="row.bookId != null">
        book_id = #{row.bookId,jdbcType=VARCHAR},
      </if>
      <if test="row.pageNum != null">
        page_num = #{row.pageNum,jdbcType=INTEGER},
      </if>
      <if test="row.contentHash != null">
        content_hash = #{row.contentHash,jdbcType=CHAR},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    set id = #{row.id,jdbcType=BIGINT},
      page_id = #{row.pageId,jdbcType=VARCHAR},
      book_id = #{row.bookId,jdbcType=VARCHAR},
      page_num = #{row.pageNum,jdbcType=INTEGER},
      content_hash = #{row.contentHash,jdbcType=CHAR},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    set id = #{row.id,jdbcType=BIGINT},
      page_id = #{row.pageId,jdbcType=VARCHAR},
      book_id = #{row.bookId,jdbcType=VARCHAR},
      page_num = #{row.pageNum,jdbcType=INTEGER},
      content_hash = #{row.contentHash,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    <set>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=VARCHAR},
      </if>
      <if test="pageNum != null">
        page_num = #{pageNum,jdbcType=INTEGER},
      </if>
      <if test="contentHash != null">
        content_hash = #{contentHash,jdbcType=CHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    set page_id = #{pageId,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=VARCHAR},
      page_num = #{pageNum,jdbcType=INTEGER},
      content_hash = #{contentHash,jdbcType=CHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.PageEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun Jun 08 15:47:26 CST 2025.
    -->
    update page_content
    set page_id = #{pageId,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=VARCHAR},
      page_num = #{pageNum,jdbcType=INTEGER},
      content_hash = #{contentHash,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 批量插入方法 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into page_content (page_id, book_id, page_num, content_hash, content)
    values 
    <foreach collection="list" item="item" separator=",">
      (#{item.pageId,jdbcType=VARCHAR}, 
       #{item.bookId,jdbcType=VARCHAR}, 
       #{item.pageNum,jdbcType=INTEGER}, 
       #{item.contentHash,jdbcType=CHAR}, 
       #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>