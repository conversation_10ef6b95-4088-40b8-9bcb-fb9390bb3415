<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.ProjectEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="priority" jdbcType="TINYINT" property="priority" />
    <result column="task_count" jdbcType="INTEGER" property="taskCount" />
    <result column="reviewer_count" jdbcType="INTEGER" property="reviewerCount" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="deadline" jdbcType="TIMESTAMP" property="deadline" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    id, project_id, project_name, status, priority, task_count, reviewer_count, creator, 
    created_time, updated_time, deadline
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.ProjectEntityExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.ProjectEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    delete from project
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into project (project_id, project_name, status, 
      priority, task_count, reviewer_count, 
      creator, created_time, updated_time, 
      deadline, description)
    values (#{projectId,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{priority,jdbcType=TINYINT}, #{taskCount,jdbcType=INTEGER}, #{reviewerCount,jdbcType=INTEGER}, 
      #{creator,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, 
      #{deadline,jdbcType=TIMESTAMP}, #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="taskCount != null">
        task_count,
      </if>
      <if test="reviewerCount != null">
        reviewer_count,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=TINYINT},
      </if>
      <if test="taskCount != null">
        #{taskCount,jdbcType=INTEGER},
      </if>
      <if test="reviewerCount != null">
        #{reviewerCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.ProjectEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    select count(*) from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.projectId != null">
        project_id = #{row.projectId,jdbcType=VARCHAR},
      </if>
      <if test="row.projectName != null">
        project_name = #{row.projectName,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.priority != null">
        priority = #{row.priority,jdbcType=TINYINT},
      </if>
      <if test="row.taskCount != null">
        task_count = #{row.taskCount,jdbcType=INTEGER},
      </if>
      <if test="row.reviewerCount != null">
        reviewer_count = #{row.reviewerCount,jdbcType=INTEGER},
      </if>
      <if test="row.creator != null">
        creator = #{row.creator,jdbcType=VARCHAR},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedTime != null">
        updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deadline != null">
        deadline = #{row.deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    set id = #{row.id,jdbcType=BIGINT},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      project_name = #{row.projectName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      priority = #{row.priority,jdbcType=TINYINT},
      task_count = #{row.taskCount,jdbcType=INTEGER},
      reviewer_count = #{row.reviewerCount,jdbcType=INTEGER},
      creator = #{row.creator,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      deadline = #{row.deadline,jdbcType=TIMESTAMP},
      description = #{row.description,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    set id = #{row.id,jdbcType=BIGINT},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      project_name = #{row.projectName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      priority = #{row.priority,jdbcType=TINYINT},
      task_count = #{row.taskCount,jdbcType=INTEGER},
      reviewer_count = #{row.reviewerCount,jdbcType=INTEGER},
      creator = #{row.creator,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      deadline = #{row.deadline,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=TINYINT},
      </if>
      <if test="taskCount != null">
        task_count = #{taskCount,jdbcType=INTEGER},
      </if>
      <if test="reviewerCount != null">
        reviewer_count = #{reviewerCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    set project_id = #{projectId,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      priority = #{priority,jdbcType=TINYINT},
      task_count = #{taskCount,jdbcType=INTEGER},
      reviewer_count = #{reviewerCount,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      deadline = #{deadline,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.ProjectEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 14:39:17 CST 2025.
    -->
    update project
    set project_id = #{projectId,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      priority = #{priority,jdbcType=TINYINT},
      task_count = #{taskCount,jdbcType=INTEGER},
      reviewer_count = #{reviewerCount,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      deadline = #{deadline,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>