<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.TaskEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.TaskEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="book_url" jdbcType="VARCHAR" property="bookUrl" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="reviewer_id" jdbcType="VARCHAR" property="reviewerId" />
    <result column="priority" jdbcType="TINYINT" property="priority" />
    <result column="deadline" jdbcType="TIMESTAMP" property="deadline" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="prompt_id" jdbcType="VARCHAR" property="promptId" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="total_issue_count" jdbcType="INTEGER" property="totalIssueCount" />
    <result column="complete_issue_count" jdbcType="INTEGER" property="completeIssueCount" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="check_file_url" jdbcType="VARCHAR" property="checkFileUrl" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    id, task_id, task_name, status, reviewer, book_url, created_time, updated_time, project_id, 
    reviewer_id, priority, deadline, file_name, prompt_id, model_id, total_issue_count, 
    complete_issue_count, file_id, check_file_url, project_name
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    description, content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.TaskEntityExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.TaskEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    delete from task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into task (task_id, task_name, status, 
      reviewer, book_url, created_time, 
      updated_time, project_id, reviewer_id, 
      priority, deadline, file_name, 
      prompt_id, model_id, total_issue_count, 
      complete_issue_count, file_id, check_file_url, 
      project_name, description, content
      )
    values (#{taskId,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{reviewer,jdbcType=VARCHAR}, #{bookUrl,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{projectId,jdbcType=VARCHAR}, #{reviewerId,jdbcType=VARCHAR}, 
      #{priority,jdbcType=TINYINT}, #{deadline,jdbcType=TIMESTAMP}, #{fileName,jdbcType=VARCHAR}, 
      #{promptId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{totalIssueCount,jdbcType=INTEGER}, 
      #{completeIssueCount,jdbcType=INTEGER}, #{fileId,jdbcType=VARCHAR}, #{checkFileUrl,jdbcType=VARCHAR}, 
      #{projectName,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reviewer != null">
        reviewer,
      </if>
      <if test="bookUrl != null">
        book_url,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="reviewerId != null">
        reviewer_id,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="promptId != null">
        prompt_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="totalIssueCount != null">
        total_issue_count,
      </if>
      <if test="completeIssueCount != null">
        complete_issue_count,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="checkFileUrl != null">
        check_file_url,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reviewer != null">
        #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="bookUrl != null">
        #{bookUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="reviewerId != null">
        #{reviewerId,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=TINYINT},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="promptId != null">
        #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="totalIssueCount != null">
        #{totalIssueCount,jdbcType=INTEGER},
      </if>
      <if test="completeIssueCount != null">
        #{completeIssueCount,jdbcType=INTEGER},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="checkFileUrl != null">
        #{checkFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.TaskEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    select count(*) from task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.taskId != null">
        task_id = #{row.taskId,jdbcType=VARCHAR},
      </if>
      <if test="row.taskName != null">
        task_name = #{row.taskName,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.reviewer != null">
        reviewer = #{row.reviewer,jdbcType=VARCHAR},
      </if>
      <if test="row.bookUrl != null">
        book_url = #{row.bookUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedTime != null">
        updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.projectId != null">
        project_id = #{row.projectId,jdbcType=VARCHAR},
      </if>
      <if test="row.reviewerId != null">
        reviewer_id = #{row.reviewerId,jdbcType=VARCHAR},
      </if>
      <if test="row.priority != null">
        priority = #{row.priority,jdbcType=TINYINT},
      </if>
      <if test="row.deadline != null">
        deadline = #{row.deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="row.fileName != null">
        file_name = #{row.fileName,jdbcType=VARCHAR},
      </if>
      <if test="row.promptId != null">
        prompt_id = #{row.promptId,jdbcType=VARCHAR},
      </if>
      <if test="row.modelId != null">
        model_id = #{row.modelId,jdbcType=VARCHAR},
      </if>
      <if test="row.totalIssueCount != null">
        total_issue_count = #{row.totalIssueCount,jdbcType=INTEGER},
      </if>
      <if test="row.completeIssueCount != null">
        complete_issue_count = #{row.completeIssueCount,jdbcType=INTEGER},
      </if>
      <if test="row.fileId != null">
        file_id = #{row.fileId,jdbcType=VARCHAR},
      </if>
      <if test="row.checkFileUrl != null">
        check_file_url = #{row.checkFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.projectName != null">
        project_name = #{row.projectName,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    set id = #{row.id,jdbcType=BIGINT},
      task_id = #{row.taskId,jdbcType=VARCHAR},
      task_name = #{row.taskName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      reviewer = #{row.reviewer,jdbcType=VARCHAR},
      book_url = #{row.bookUrl,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      reviewer_id = #{row.reviewerId,jdbcType=VARCHAR},
      priority = #{row.priority,jdbcType=TINYINT},
      deadline = #{row.deadline,jdbcType=TIMESTAMP},
      file_name = #{row.fileName,jdbcType=VARCHAR},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      model_id = #{row.modelId,jdbcType=VARCHAR},
      total_issue_count = #{row.totalIssueCount,jdbcType=INTEGER},
      complete_issue_count = #{row.completeIssueCount,jdbcType=INTEGER},
      file_id = #{row.fileId,jdbcType=VARCHAR},
      check_file_url = #{row.checkFileUrl,jdbcType=VARCHAR},
      project_name = #{row.projectName,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=LONGVARCHAR},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    set id = #{row.id,jdbcType=BIGINT},
      task_id = #{row.taskId,jdbcType=VARCHAR},
      task_name = #{row.taskName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      reviewer = #{row.reviewer,jdbcType=VARCHAR},
      book_url = #{row.bookUrl,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      project_id = #{row.projectId,jdbcType=VARCHAR},
      reviewer_id = #{row.reviewerId,jdbcType=VARCHAR},
      priority = #{row.priority,jdbcType=TINYINT},
      deadline = #{row.deadline,jdbcType=TIMESTAMP},
      file_name = #{row.fileName,jdbcType=VARCHAR},
      prompt_id = #{row.promptId,jdbcType=VARCHAR},
      model_id = #{row.modelId,jdbcType=VARCHAR},
      total_issue_count = #{row.totalIssueCount,jdbcType=INTEGER},
      complete_issue_count = #{row.completeIssueCount,jdbcType=INTEGER},
      file_id = #{row.fileId,jdbcType=VARCHAR},
      check_file_url = #{row.checkFileUrl,jdbcType=VARCHAR},
      project_name = #{row.projectName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="reviewer != null">
        reviewer = #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="bookUrl != null">
        book_url = #{bookUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="reviewerId != null">
        reviewer_id = #{reviewerId,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=TINYINT},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="promptId != null">
        prompt_id = #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="totalIssueCount != null">
        total_issue_count = #{totalIssueCount,jdbcType=INTEGER},
      </if>
      <if test="completeIssueCount != null">
        complete_issue_count = #{completeIssueCount,jdbcType=INTEGER},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="checkFileUrl != null">
        check_file_url = #{checkFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.swyx.api.contentmanager.dao.TaskEntityWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    set task_id = #{taskId,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      reviewer = #{reviewer,jdbcType=VARCHAR},
      book_url = #{bookUrl,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      project_id = #{projectId,jdbcType=VARCHAR},
      reviewer_id = #{reviewerId,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=TINYINT},
      deadline = #{deadline,jdbcType=TIMESTAMP},
      file_name = #{fileName,jdbcType=VARCHAR},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      total_issue_count = #{totalIssueCount,jdbcType=INTEGER},
      complete_issue_count = #{completeIssueCount,jdbcType=INTEGER},
      file_id = #{fileId,jdbcType=VARCHAR},
      check_file_url = #{checkFileUrl,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.TaskEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 16:32:29 CST 2025.
    -->
    update task
    set task_id = #{taskId,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      reviewer = #{reviewer,jdbcType=VARCHAR},
      book_url = #{bookUrl,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      project_id = #{projectId,jdbcType=VARCHAR},
      reviewer_id = #{reviewerId,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=TINYINT},
      deadline = #{deadline,jdbcType=TIMESTAMP},
      file_name = #{fileName,jdbcType=VARCHAR},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      total_issue_count = #{totalIssueCount,jdbcType=INTEGER},
      complete_issue_count = #{completeIssueCount,jdbcType=INTEGER},
      file_id = #{fileId,jdbcType=VARCHAR},
      check_file_url = #{checkFileUrl,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>