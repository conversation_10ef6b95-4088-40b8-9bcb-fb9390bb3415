<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swyx.api.contentmanager.dao.mapper.UserEntityMapper">
  <resultMap id="BaseResultMap" type="com.swyx.api.contentmanager.dao.UserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="en_name" jdbcType="VARCHAR" property="enName" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="username" jdbcType="VARCHAR" property="username" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    user_id, open_id, name, en_name, avatar, mobile, email, status, created_time, updated_time, 
    password, username
  </sql>
  <select id="selectByExample" parameterType="com.swyx.api.contentmanager.dao.UserEntityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from user
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    delete from user
    where user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.swyx.api.contentmanager.dao.UserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    insert into user (user_id, open_id, name, 
      en_name, avatar, mobile, 
      email, status, created_time, 
      updated_time, password, username
      )
    values (#{userId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{enName,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{password,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.swyx.api.contentmanager.dao.UserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="enName != null">
        en_name,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="username != null">
        username,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        #{enName,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.swyx.api.contentmanager.dao.UserEntityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    select count(*) from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    update user
    <set>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=VARCHAR},
      </if>
      <if test="row.openId != null">
        open_id = #{row.openId,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.enName != null">
        en_name = #{row.enName,jdbcType=VARCHAR},
      </if>
      <if test="row.avatar != null">
        avatar = #{row.avatar,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.email != null">
        email = #{row.email,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=VARCHAR},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedTime != null">
        updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.password != null">
        password = #{row.password,jdbcType=VARCHAR},
      </if>
      <if test="row.username != null">
        username = #{row.username,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    update user
    set user_id = #{row.userId,jdbcType=VARCHAR},
      open_id = #{row.openId,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      en_name = #{row.enName,jdbcType=VARCHAR},
      avatar = #{row.avatar,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      email = #{row.email,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=VARCHAR},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      password = #{row.password,jdbcType=VARCHAR},
      username = #{row.username,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.swyx.api.contentmanager.dao.UserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    update user
    <set>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        en_name = #{enName,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
    </set>
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.swyx.api.contentmanager.dao.UserEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jun 21 15:54:26 CST 2025.
    -->
    update user
    set open_id = #{openId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      en_name = #{enName,jdbcType=VARCHAR},
      avatar = #{avatar,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      password = #{password,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR}
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
</mapper>