<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.swyx.api.contentmanager.dao.mapper.UserRolePermissionCustomMapper">
    <select id="selectRolesAndPermissionsByUserId" resultType="com.swyx.api.contentmanager.dto.UserRolePermissionDTO">
        SELECT
            r.code AS roleCode,
            p.name AS permissionName
        FROM user_role ur
                 JOIN role r ON ur.roleId = r.id
                 LEFT JOIN role_permission rp ON r.id = rp.roleId
                 LEFT JOIN permission p ON rp.permissionId = p.id
        WHERE ur.userId = #{userId}
    </select>
</mapper>