package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 基础文件测试
 */
@SpringBootTest
public class BasicFileTest {

    @Test
    public void testFileAccess() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        
        System.out.println("=== 基础文件访问测试 ===");
        System.out.println("测试文件路径: " + testFilePath);
        
        // 1. 检查文件是否存在
        File file = new File(testFilePath);
        System.out.println("文件是否存在: " + file.exists());
        System.out.println("文件绝对路径: " + file.getAbsolutePath());
        
        if (!file.exists()) {
            System.err.println("❌ 文件不存在，测试终止");
            return;
        }
        
        // 2. 检查文件属性
        System.out.println("文件大小: " + file.length() + " 字节");
        System.out.println("可读: " + file.canRead());
        System.out.println("是文件: " + file.isFile());
        
        // 3. 尝试读取文件
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[100];
            int bytesRead = fis.read(buffer);
            System.out.println("成功读取 " + bytesRead + " 字节");
            
            // 检查文件头（ZIP文件应该以PK开头）
            if (bytesRead >= 2) {
                if (buffer[0] == 'P' && buffer[1] == 'K') {
                    System.out.println("✅ 文件头正确 (ZIP格式)");
                } else {
                    System.out.println("❌ 文件头不正确，不是ZIP格式");
                    System.out.printf("文件头: %02X %02X%n", buffer[0], buffer[1]);
                }
            }
        } catch (IOException e) {
            System.err.println("❌ 读取文件失败: " + e.getMessage());
        }
        
        // 4. 使用NIO检查
        try {
            Path path = Paths.get(testFilePath);
            System.out.println("NIO路径存在: " + Files.exists(path));
            System.out.println("NIO文件大小: " + Files.size(path));
            System.out.println("NIO可读: " + Files.isReadable(path));
        } catch (Exception e) {
            System.err.println("❌ NIO检查失败: " + e.getMessage());
        }
        
        // 5. 检查工作目录
        System.out.println("当前工作目录: " + System.getProperty("user.dir"));
        
        // 6. 列出test/resources目录内容
        File resourcesDir = new File("src/test/resources");
        if (resourcesDir.exists()) {
            System.out.println("test/resources目录内容:");
            String[] files = resourcesDir.list();
            if (files != null) {
                for (String f : files) {
                    System.out.println("  - " + f);
                }
            }
        } else {
            System.out.println("❌ test/resources目录不存在");
        }
        
        System.out.println("=== 基础文件访问测试完成 ===");
    }
}
