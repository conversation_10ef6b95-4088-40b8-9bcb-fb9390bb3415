package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试分割逻辑测试
 */
@SpringBootTest
public class DebugSplitTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void debugSplitLogic() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "debug-split-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 调试分割逻辑 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                
                // 获取所有段落
                List<Object> paragraphs = getAllElementsFromObject(documentPart);
                
                // 转换为段落列表
                List<P> allParagraphs = new ArrayList<>();
                for (Object obj : paragraphs) {
                    if (obj instanceof P) {
                        allParagraphs.add((P) obj);
                    }
                }
                
                // 计算总字数
                int totalWordCount = calculateWordCount(allParagraphs);
                result.append("文档总字数: ").append(totalWordCount).append("\n\n");
                
                // 模拟分割逻辑
                result.append("=== 模拟分割逻辑（字数限制: 50）===\n");
                int maxWordCount = 50;
                
                if (totalWordCount <= maxWordCount) {
                    result.append("✅ 总字数(").append(totalWordCount).append(") <= 限制(").append(maxWordCount).append(")，不分割\n");
                } else {
                    result.append("❌ 总字数(").append(totalWordCount).append(") > 限制(").append(maxWordCount).append(")，需要分割\n");
                    
                    // 按Heading1分割
                    Map<Integer, List<P>> chapterMap = new LinkedHashMap<>();
                    int chapterIndex = 0;
                    
                    for (P para : allParagraphs) {
                        if (isHeadingLevel(para, 1)) {
                            chapterIndex++;
                            result.append("发现Heading1: ").append(getParagraphText(para)).append("\n");
                        }
                        
                        if (chapterIndex > 0) {
                            chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
                        }
                    }
                    
                    result.append("按Heading1分割后的章节数: ").append(chapterMap.size()).append("\n\n");
                    
                    // 分析每个章节
                    for (Map.Entry<Integer, List<P>> entry : chapterMap.entrySet()) {
                        List<P> chapterParagraphs = entry.getValue();
                        int chapterWordCount = calculateWordCount(chapterParagraphs);
                        
                        result.append(String.format("章节 %d: %d个段落, %d字\n", 
                            entry.getKey(), chapterParagraphs.size(), chapterWordCount));
                        
                        if (chapterWordCount <= maxWordCount) {
                            result.append("  ✅ 字数未超过限制，直接保存\n");
                        } else {
                            result.append("  ❌ 字数超过限制，需要进一步分割\n");
                        }
                    }
                }
                
                // 实际测试分割
                result.append("\n=== 实际分割测试 ===\n");
                String outputDir = "debug-split-output";
                List<File> splitFiles = docx4jSplitterService.splitByHeading(testFilePath, outputDir, maxWordCount);
                
                result.append("实际生成文件数: ").append(splitFiles.size()).append("\n");
                for (File file : splitFiles) {
                    result.append("  - ").append(file.getName()).append("\n");
                }
                
                // 分析差异
                result.append("\n=== 差异分析 ===\n");
                if (totalWordCount > maxWordCount && splitFiles.size() == 1) {
                    result.append("❌ 问题: 应该分割但没有分割\n");
                    result.append("可能原因:\n");
                    result.append("1. 字数统计方法不一致\n");
                    result.append("2. 分割逻辑有bug\n");
                    result.append("3. 段落获取方法有问题\n");
                } else if (totalWordCount <= maxWordCount && splitFiles.size() > 1) {
                    result.append("❌ 问题: 不应该分割但进行了分割\n");
                } else {
                    result.append("✅ 分割逻辑正常\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("调试结果已保存到: " + resultFilePath);
    }
    
    private List<Object> getAllElementsFromObject(MainDocumentPart documentPart) {
        List<Object> result = new ArrayList<>();
        
        try {
            // 使用docx4j的TraversalUtil来安全地遍历所有元素
            org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                @Override
                public List<Object> apply(Object o) {
                    if (o instanceof P) {
                        result.add(o);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // 如果TraversalUtil失败，使用简单方法
            List<Object> content = documentPart.getContent();
            for (Object item : content) {
                if (item instanceof P) {
                    result.add(item);
                }
            }
        }
        
        return result;
    }
    
    private int calculateWordCount(List<P> paragraphs) {
        int totalWords = 0;
        for (P para : paragraphs) {
            String text = getParagraphText(para);
            // 简单的字数统计：去除空白字符后计算长度
            totalWords += text.replaceAll("\\s+", "").length();
        }
        return totalWords;
    }
    
    private boolean isHeadingLevel(P para, int level) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            String styleId = para.getPPr().getPStyle().getVal();
            if (styleId == null) return false;
            
            // 检查数字格式 "1", "2", "3", "4", "5", "6"
            if (String.valueOf(level).equals(styleId)) return true;
            
            // 检查标准格式 "Heading1", "Heading2", etc.
            String standardStyle = "Heading" + level;
            if (standardStyle.equals(styleId)) return true;
        }
        return false;
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
