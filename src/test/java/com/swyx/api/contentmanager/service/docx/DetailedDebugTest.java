package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 详细调试分割逻辑
 */
@SpringBootTest
public class DetailedDebugTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void detailedDebugSplitLogic() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "detailed-debug-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 详细调试分割逻辑 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                
                // 获取所有段落
                List<Object> paragraphs = getAllElementsFromObject(documentPart);
                
                // 转换为段落列表
                List<P> allParagraphs = new ArrayList<>();
                for (Object obj : paragraphs) {
                    if (obj instanceof P) {
                        allParagraphs.add((P) obj);
                    }
                }
                
                // 计算总字数
                int totalWordCount = calculateWordCount(allParagraphs);
                result.append("文档总字数: ").append(totalWordCount).append("\n");
                result.append("段落总数: ").append(allParagraphs.size()).append("\n\n");
                
                // 详细分析每个段落
                result.append("=== 段落详细分析 ===\n");
                for (int i = 0; i < allParagraphs.size(); i++) {
                    P para = allParagraphs.get(i);
                    String styleId = getStyleId(para);
                    String text = getParagraphText(para);
                    int wordCount = text.replaceAll("\\s+", "").length();
                    boolean isH1 = isHeadingLevel(para, 1);
                    
                    result.append(String.format("段落 %d: 样式=%s, 字数=%d, H1=%s, 内容=%s%n",
                        i + 1, styleId, wordCount, isH1 ? "是" : "否", 
                        text.length() > 20 ? text.substring(0, 20) + "..." : text));
                }
                
                // 模拟分割逻辑（详细版）
                result.append("\n=== 模拟分割逻辑（字数限制: 50）===\n");
                int maxWordCount = 50;
                
                if (totalWordCount <= maxWordCount) {
                    result.append("✅ 总字数 <= 限制，不分割\n");
                } else {
                    result.append("❌ 总字数 > 限制，需要分割\n");
                    
                    // 按Heading1分割（详细版）
                    Map<Integer, List<P>> chapterMap = new LinkedHashMap<>();
                    int chapterIndex = 0;
                    
                    result.append("\n按Heading1分割过程:\n");
                    for (int i = 0; i < allParagraphs.size(); i++) {
                        P para = allParagraphs.get(i);
                        boolean isH1 = isHeadingLevel(para, 1);
                        
                        if (isH1) {
                            chapterIndex++;
                            result.append(String.format("  段落 %d: 发现Heading1，章节索引 = %d\n", i + 1, chapterIndex));
                        }
                        
                        if (chapterIndex > 0) {
                            chapterMap.computeIfAbsent(chapterIndex, k -> new ArrayList<>()).add(para);
                            result.append(String.format("  段落 %d: 添加到章节 %d\n", i + 1, chapterIndex));
                        } else {
                            result.append(String.format("  段落 %d: 跳过（chapterIndex = 0）\n", i + 1));
                        }
                    }
                    
                    result.append(String.format("\n章节分割结果: %d个章节\n", chapterMap.size()));
                    
                    // 如果没有找到Heading1的处理
                    if (chapterMap.isEmpty() && !allParagraphs.isEmpty()) {
                        result.append("没有找到Heading1，将所有段落归入章节1\n");
                        chapterMap.put(1, allParagraphs);
                    }
                    
                    // 分析每个章节
                    result.append("\n=== 章节分析 ===\n");
                    for (Map.Entry<Integer, List<P>> entry : chapterMap.entrySet()) {
                        List<P> chapterParagraphs = entry.getValue();
                        int chapterWordCount = calculateWordCount(chapterParagraphs);
                        
                        result.append(String.format("章节 %d:\n", entry.getKey()));
                        result.append(String.format("  段落数: %d\n", chapterParagraphs.size()));
                        result.append(String.format("  字数: %d\n", chapterWordCount));
                        result.append(String.format("  是否超过限制: %s\n", chapterWordCount > maxWordCount ? "是" : "否"));
                        
                        // 显示章节内容概要
                        result.append("  内容概要:\n");
                        for (int i = 0; i < Math.min(3, chapterParagraphs.size()); i++) {
                            P para = chapterParagraphs.get(i);
                            String text = getParagraphText(para);
                            String styleId = getStyleId(para);
                            result.append(String.format("    - [%s] %s\n", styleId, 
                                text.length() > 30 ? text.substring(0, 30) + "..." : text));
                        }
                        if (chapterParagraphs.size() > 3) {
                            result.append(String.format("    ... 还有 %d 个段落\n", chapterParagraphs.size() - 3));
                        }
                        result.append("\n");
                    }
                }
                
                // 实际测试分割
                result.append("=== 实际分割测试 ===\n");
                String outputDir = "detailed-debug-output";
                List<File> splitFiles = docx4jSplitterService.splitByHeading(testFilePath, outputDir, maxWordCount);
                
                result.append("实际生成文件数: ").append(splitFiles.size()).append("\n");
                for (File file : splitFiles) {
                    result.append("  - ").append(file.getName()).append(" (").append(file.length()).append(" 字节)\n");
                }
                
                // 问题诊断
                result.append("\n=== 问题诊断 ===\n");
                if (totalWordCount > maxWordCount) {
                    if (splitFiles.size() == 1 && splitFiles.get(0).getName().contains("_complete.docx")) {
                        result.append("❌ 问题确认: 应该分割但输出了完整文档\n");
                        result.append("可能原因:\n");
                        result.append("1. 段落获取有问题\n");
                        result.append("2. 章节分割逻辑有bug\n");
                        result.append("3. 字数计算不一致\n");
                    } else {
                        result.append("✅ 正常: 正确进行了分割\n");
                    }
                } else {
                    result.append("✅ 正常: 字数未超过限制，正确输出完整文档\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("详细调试结果已保存到: " + resultFilePath);
    }
    
    private List<Object> getAllElementsFromObject(MainDocumentPart documentPart) {
        List<Object> result = new ArrayList<>();
        
        try {
            // 使用docx4j的TraversalUtil来安全地遍历所有元素
            org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                @Override
                public List<Object> apply(Object o) {
                    if (o instanceof P) {
                        result.add(o);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // 如果TraversalUtil失败，使用简单方法
            List<Object> content = documentPart.getContent();
            for (Object item : content) {
                if (item instanceof P) {
                    result.add(item);
                }
            }
        }
        
        return result;
    }
    
    private int calculateWordCount(List<P> paragraphs) {
        int totalWords = 0;
        for (P para : paragraphs) {
            String text = getParagraphText(para);
            totalWords += text.replaceAll("\\s+", "").length();
        }
        return totalWords;
    }
    
    private boolean isHeadingLevel(P para, int level) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            String styleId = para.getPPr().getPStyle().getVal();
            if (styleId == null) return false;
            
            // 检查数字格式 "1", "2", "3", "4", "5", "6"
            if (String.valueOf(level).equals(styleId)) return true;
            
            // 检查标准格式 "Heading1", "Heading2", etc.
            String standardStyle = "Heading" + level;
            if (standardStyle.equals(styleId)) return true;
        }
        return false;
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
