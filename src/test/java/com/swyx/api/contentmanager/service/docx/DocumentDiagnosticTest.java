package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 文档诊断测试工具
 * 用于检查Word文档是否有效以及格式问题
 */
@SpringBootTest
public class DocumentDiagnosticTest {

    @Test
    public void diagnosisDocument() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        
        System.out.println("=== Word文档诊断工具 ===");
        System.out.println("检查文档: " + testFilePath);
        
        // 1. 检查文件是否存在
        checkFileExists(testFilePath);
        
        // 2. 检查文件基本信息
        checkFileInfo(testFilePath);
        
        // 3. 检查文件是否为有效的ZIP格式
        checkZipFormat(testFilePath);
        
        // 4. 检查docx内部结构
        checkDocxStructure(testFilePath);
        
        // 5. 尝试用docx4j加载
        tryLoadWithDocx4j(testFilePath);
        
        System.out.println("\n=== 建议 ===");
        System.out.println("1. 确保文档是用Microsoft Word或兼容软件创建的.docx文件");
        System.out.println("2. 不要将.doc文件重命名为.docx");
        System.out.println("3. 如果文档损坏，请重新创建");
        System.out.println("4. 可以尝试用Word打开并另存为新的.docx文件");
    }
    
    private void checkFileExists(String filePath) {
        System.out.println("\n--- 1. 文件存在性检查 ---");
        File file = new File(filePath);
        
        if (file.exists()) {
            System.out.println("✅ 文件存在");
        } else {
            System.out.println("❌ 文件不存在");
            
            // 检查目录是否存在
            File parentDir = file.getParentFile();
            if (parentDir.exists()) {
                System.out.println("📁 目录存在，但文件缺失");
                System.out.println("目录内容:");
                String[] files = parentDir.list();
                if (files != null) {
                    for (String f : files) {
                        System.out.println("  - " + f);
                    }
                }
            } else {
                System.out.println("📁 目录不存在: " + parentDir.getAbsolutePath());
            }
        }
    }
    
    private void checkFileInfo(String filePath) {
        System.out.println("\n--- 2. 文件基本信息 ---");
        File file = new File(filePath);
        
        if (!file.exists()) {
            System.out.println("❌ 文件不存在，跳过检查");
            return;
        }
        
        System.out.println("📄 文件名: " + file.getName());
        System.out.println("📏 文件大小: " + file.length() + " 字节 (" + (file.length() / 1024.0) + " KB)");
        System.out.println("📅 最后修改: " + new java.util.Date(file.lastModified()));
        System.out.println("🔒 可读: " + file.canRead());
        System.out.println("✏️ 可写: " + file.canWrite());
        
        // 检查文件扩展名
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".docx")) {
            System.out.println("✅ 文件扩展名正确 (.docx)");
        } else if (fileName.endsWith(".doc")) {
            System.out.println("⚠️ 文件是.doc格式，不是.docx格式");
        } else {
            System.out.println("❌ 文件扩展名不正确");
        }
        
        // 检查文件大小
        if (file.length() == 0) {
            System.out.println("❌ 文件为空");
        } else if (file.length() < 1000) {
            System.out.println("⚠️ 文件太小，可能不是有效的Word文档");
        } else {
            System.out.println("✅ 文件大小正常");
        }
    }
    
    private void checkZipFormat(String filePath) {
        System.out.println("\n--- 3. ZIP格式检查 ---");
        File file = new File(filePath);
        
        if (!file.exists()) {
            System.out.println("❌ 文件不存在，跳过检查");
            return;
        }
        
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(file))) {
            ZipEntry entry;
            int entryCount = 0;
            boolean hasContentTypes = false;
            boolean hasDocumentXml = false;
            
            System.out.println("ZIP文件内容:");
            while ((entry = zis.getNextEntry()) != null) {
                entryCount++;
                String entryName = entry.getName();
                System.out.println("  - " + entryName);
                
                if ("[Content_Types].xml".equals(entryName)) {
                    hasContentTypes = true;
                }
                if ("word/document.xml".equals(entryName)) {
                    hasDocumentXml = true;
                }
                
                if (entryCount > 20) {
                    System.out.println("  ... (还有更多文件)");
                    break;
                }
            }
            
            System.out.println("📊 ZIP条目总数: " + entryCount + "+");
            System.out.println("✅ 文件是有效的ZIP格式");
            
            if (hasContentTypes) {
                System.out.println("✅ 包含 [Content_Types].xml");
            } else {
                System.out.println("❌ 缺少 [Content_Types].xml");
            }
            
            if (hasDocumentXml) {
                System.out.println("✅ 包含 word/document.xml");
            } else {
                System.out.println("❌ 缺少 word/document.xml");
            }
            
        } catch (IOException e) {
            System.out.println("❌ 不是有效的ZIP文件: " + e.getMessage());
        }
    }
    
    private void checkDocxStructure(String filePath) {
        System.out.println("\n--- 4. DOCX结构检查 ---");
        File file = new File(filePath);
        
        if (!file.exists()) {
            System.out.println("❌ 文件不存在，跳过检查");
            return;
        }
        
        // 检查关键文件是否存在
        String[] requiredFiles = {
            "[Content_Types].xml",
            "_rels/.rels",
            "word/document.xml",
            "word/_rels/document.xml.rels"
        };
        
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(file))) {
            ZipEntry entry;
            java.util.Set<String> foundFiles = new java.util.HashSet<>();
            
            while ((entry = zis.getNextEntry()) != null) {
                foundFiles.add(entry.getName());
            }
            
            for (String requiredFile : requiredFiles) {
                if (foundFiles.contains(requiredFile)) {
                    System.out.println("✅ " + requiredFile);
                } else {
                    System.out.println("❌ 缺少: " + requiredFile);
                }
            }
            
        } catch (IOException e) {
            System.out.println("❌ 检查DOCX结构失败: " + e.getMessage());
        }
    }
    
    private void tryLoadWithDocx4j(String filePath) {
        System.out.println("\n--- 5. docx4j加载测试 ---");
        File file = new File(filePath);
        
        if (!file.exists()) {
            System.out.println("❌ 文件不存在，跳过检查");
            return;
        }
        
        try {
            WordprocessingMLPackage wordMLPackage = Docx4J.load(file);
            System.out.println("✅ docx4j成功加载文档");
            
            // 尝试获取文档内容
            if (wordMLPackage.getMainDocumentPart() != null) {
                System.out.println("✅ 成功获取主文档部分");
                
                java.util.List<Object> content = wordMLPackage.getMainDocumentPart().getContent();
                System.out.println("📄 文档内容元素数量: " + content.size());
            } else {
                System.out.println("❌ 无法获取主文档部分");
            }
            
        } catch (Exception e) {
            System.out.println("❌ docx4j加载失败: " + e.getMessage());
            System.out.println("详细错误:");
            e.printStackTrace();
        }
    }
}
