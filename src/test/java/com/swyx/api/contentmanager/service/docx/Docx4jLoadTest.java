package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * docx4j加载测试
 */
@SpringBootTest
public class Docx4jLoadTest {

    @Test
    public void testDocx4jLoad() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        File file = new File(testFilePath);
        
        System.out.println("=== docx4j加载测试 ===");
        System.out.println("文件路径: " + testFilePath);
        System.out.println("文件存在: " + file.exists());
        System.out.println("文件大小: " + file.length() + " 字节");
        
        if (!file.exists()) {
            System.err.println("❌ 文件不存在");
            return;
        }
        
        // 1. 先验证ZIP结构
        System.out.println("\n1. 验证ZIP结构...");
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(file))) {
            ZipEntry entry;
            boolean hasContentTypes = false;
            boolean hasDocument = false;
            int entryCount = 0;
            
            while ((entry = zis.getNextEntry()) != null) {
                entryCount++;
                String name = entry.getName();
                if ("[Content_Types].xml".equals(name)) {
                    hasContentTypes = true;
                }
                if ("word/document.xml".equals(name)) {
                    hasDocument = true;
                }
                if (entryCount <= 5) {
                    System.out.println("  - " + name);
                }
            }
            
            System.out.println("  ZIP条目总数: " + entryCount);
            System.out.println("  包含Content_Types.xml: " + hasContentTypes);
            System.out.println("  包含word/document.xml: " + hasDocument);
            
            if (!hasContentTypes || !hasDocument) {
                System.err.println("❌ 文档结构不完整");
                return;
            }
            
        } catch (Exception e) {
            System.err.println("❌ ZIP验证失败: " + e.getMessage());
            return;
        }
        
        // 2. 尝试用docx4j加载
        System.out.println("\n2. 使用docx4j加载文档...");
        try {
            WordprocessingMLPackage wordMLPackage = Docx4J.load(file);
            System.out.println("✅ 文档加载成功");
            
            // 3. 获取主文档部分
            System.out.println("\n3. 获取主文档部分...");
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            if (documentPart != null) {
                System.out.println("✅ 主文档部分获取成功");
                
                // 4. 获取内容
                System.out.println("\n4. 获取文档内容...");
                List<Object> content = documentPart.getContent();
                System.out.println("✅ 内容获取成功，共 " + content.size() + " 个元素");
                
                // 5. 分析段落
                System.out.println("\n5. 分析段落...");
                int paragraphCount = 0;
                int headingCount = 0;
                
                for (Object obj : content) {
                    if (obj instanceof P) {
                        paragraphCount++;
                        P para = (P) obj;
                        
                        String styleId = getStyleId(para);
                        String text = getParagraphText(para);
                        
                        if (isHeadingStyle(styleId)) {
                            headingCount++;
                            System.out.printf("  标题 [%s]: %s%n", styleId, 
                                text.length() > 40 ? text.substring(0, 40) + "..." : text);
                        }
                    }
                }
                
                System.out.println("✅ 段落分析完成");
                System.out.println("  - 段落总数: " + paragraphCount);
                System.out.println("  - 标题总数: " + headingCount);
                
                // 6. 测试标题级别检测
                System.out.println("\n6. 测试标题级别检测...");
                for (int level = 1; level <= 6; level++) {
                    int levelCount = 0;
                    for (Object obj : content) {
                        if (obj instanceof P) {
                            P para = (P) obj;
                            if (isHeadingLevel(para, level)) {
                                levelCount++;
                            }
                        }
                    }
                    if (levelCount > 0) {
                        System.out.printf("  ✅ Heading%d: %d个%n", level, levelCount);
                    }
                }
                
                System.out.println("\n🎉 docx4j测试完全成功！");
                
            } else {
                System.err.println("❌ 无法获取主文档部分");
            }
            
        } catch (Exception e) {
            System.err.println("❌ docx4j加载失败: " + e.getClass().getSimpleName() + ": " + e.getMessage());
            System.err.println("详细错误信息:");
            e.printStackTrace();
            
            // 检查是否是特定的错误类型
            Throwable cause = e.getCause();
            while (cause != null) {
                System.err.println("原因: " + cause.getClass().getSimpleName() + ": " + cause.getMessage());
                cause = cause.getCause();
            }
        }
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return null;
    }
    
    private boolean isHeadingStyle(String styleId) {
        return styleId != null && styleId.startsWith("Heading");
    }
    
    private boolean isHeadingLevel(P para, int level) {
        String styleId = getStyleId(para);
        return ("Heading" + level).equals(styleId);
    }
    
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
