package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.List;

@SpringBootTest
public class Docx4jSplitterServiceTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testSplitByHeadingWithWordLimit() throws Exception {
        // 测试文件路径（请根据实际情况修改）
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output";
        
        // 设置字数限制为1000字
        int maxWordCount = 1000;
        
        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
        
        // 输出结果
        System.out.println("分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByHeadingWithoutWordLimit() throws Exception {
        // 测试不限制字数的原有功能
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output-unlimited";
        
        // 执行分割（不限制字数）
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath);
        
        // 输出结果
        System.out.println("分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByHeadingWithSmallWordLimit() throws Exception {
        // 测试小字数限制，强制进行多级分割
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output-small";

        // 设置字数限制为500字，强制进行更细粒度的分割
        int maxWordCount = 500;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("小字数限制分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitDocumentWithoutHeadings() throws Exception {
        // 测试没有标题的文档分割
        String sourceFilePath = "src/test/resources/no-heading-document.docx";
        String outputDirPath = "src/test/resources/output-no-heading";

        // 设置字数限制为800字
        int maxWordCount = 800;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("无标题文档分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByParagraphsOnly() throws Exception {
        // 测试纯段落分割功能
        String sourceFilePath = "src/test/resources/plain-text-document.docx";
        String outputDirPath = "src/test/resources/output-paragraphs-only";

        // 设置字数限制为600字
        int maxWordCount = 600;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByParagraphsOnly(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("纯段落分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitEmptyDocument() throws Exception {
        // 测试空文档的处理
        String sourceFilePath = "src/test/resources/empty-document.docx";
        String outputDirPath = "src/test/resources/output-empty";

        int maxWordCount = 1000;

        try {
            List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
            System.out.println("空文档处理完成，共生成 " + resultFiles.size() + " 个文件");
        } catch (Exception e) {
            System.out.println("空文档处理异常：" + e.getMessage());
        }
    }
}
