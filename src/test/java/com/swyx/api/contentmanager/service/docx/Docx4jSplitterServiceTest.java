package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.List;

@SpringBootTest
public class Docx4jSplitterServiceTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testSplitByHeadingWithWordLimit() throws Exception {
        // 测试文件路径（请根据实际情况修改）
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output";
        
        // 设置字数限制为1000字
        int maxWordCount = 1000;
        
        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
        
        // 输出结果
        System.out.println("分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByHeadingWithoutWordLimit() throws Exception {
        // 测试不限制字数的原有功能
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output-unlimited";
        
        // 执行分割（不限制字数）
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath);
        
        // 输出结果
        System.out.println("分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByHeadingWithSmallWordLimit() throws Exception {
        // 测试小字数限制，强制进行多级分割
        String sourceFilePath = "src/test/resources/test-document.docx";
        String outputDirPath = "src/test/resources/output-small";

        // 设置字数限制为500字，强制进行更细粒度的分割
        int maxWordCount = 500;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("小字数限制分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitDocumentWithoutHeadings() throws Exception {
        // 测试没有标题的文档分割
        String sourceFilePath = "src/test/resources/no-heading-document.docx";
        String outputDirPath = "src/test/resources/output-no-heading";

        // 设置字数限制为800字
        int maxWordCount = 800;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("无标题文档分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitByParagraphsOnly() throws Exception {
        // 测试纯段落分割功能
        String sourceFilePath = "src/test/resources/plain-text-document.docx";
        String outputDirPath = "src/test/resources/output-paragraphs-only";

        // 设置字数限制为600字
        int maxWordCount = 600;

        // 执行分割
        List<File> resultFiles = docx4jSplitterService.splitByParagraphsOnly(sourceFilePath, outputDirPath, maxWordCount);

        // 输出结果
        System.out.println("纯段落分割完成，共生成 " + resultFiles.size() + " 个文件：");
        for (File file : resultFiles) {
            System.out.println("- " + file.getName());
        }
    }

    @Test
    public void testSplitEmptyDocument() throws Exception {
        // 测试空文档的处理
        String sourceFilePath = "src/test/resources/empty-document.docx";
        String outputDirPath = "src/test/resources/output-empty";

        int maxWordCount = 1000;

        try {
            List<File> resultFiles = docx4jSplitterService.splitByHeading(sourceFilePath, outputDirPath, maxWordCount);
            System.out.println("空文档处理完成，共生成 " + resultFiles.size() + " 个文件");
        } catch (Exception e) {
            System.out.println("空文档处理异常：" + e.getMessage());
        }
    }

    @Autowired
    private HeadingLevelAnalyzer headingLevelAnalyzer;

    @Test
    public void testAnalyzeHeadingLevels() throws Exception {
        // 测试分析文档中的标题级别
        String testFilePath = "src/test/resources/multi-level-document.docx";

        System.out.println("=== 测试docx4j读取标题级别能力 ===");
        headingLevelAnalyzer.printExpectedStructure();

        try {
            headingLevelAnalyzer.analyzeHeadingLevels(testFilePath);
        } catch (Exception e) {
            System.err.println("无法分析文档: " + e.getMessage());
            System.out.println("请创建一个包含多级标题的测试文档: " + testFilePath);
            System.out.println("文档应包含Heading1到Heading6的示例");
        }
    }

    @Test
    public void testHeadingLevelDetection() throws Exception {
        // 直接测试标题级别检测方法
        System.out.println("=== 测试标题级别检测方法 ===");

        // 这里可以添加更多的单元测试
        // 由于需要实际的P对象，这个测试主要用于验证方法存在性
        System.out.println("✅ isHeading1, isHeading2, ..., isHeading6 方法已实现");
        System.out.println("✅ isHeadingLevel(P para, int level) 通用方法已实现");
        System.out.println("✅ getHeadingLevel(P para) 获取级别方法已实现");
    }
}
