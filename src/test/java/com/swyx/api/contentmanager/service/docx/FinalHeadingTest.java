package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 最终的标题级别测试
 * 验证docx4j能否正确读取所有6个级别的标题
 */
@SpringBootTest
public class FinalHeadingTest {

    @Test
    public void testAllHeadingLevelsDetection() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        File docFile = new File(testFilePath);
        
        System.out.println("🎯 === 最终标题级别检测测试 ===");
        System.out.println("测试文档: " + testFilePath);
        
        if (!docFile.exists()) {
            System.err.println("❌ 测试文档不存在: " + testFilePath);
            System.out.println("请在此路径创建包含多级标题的Word文档");
            return;
        }
        
        try {
            // 加载文档
            WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            List<Object> content = documentPart.getContent();
            
            System.out.println("✅ 文档加载成功，共 " + content.size() + " 个元素");
            
            // 统计各级标题
            Map<Integer, Integer> headingCounts = new HashMap<>();
            Map<Integer, String> headingExamples = new HashMap<>();
            
            for (Object obj : content) {
                if (obj instanceof P) {
                    P para = (P) obj;
                    
                    // 检查每个标题级别
                    for (int level = 1; level <= 6; level++) {
                        if (isHeadingLevel(para, level)) {
                            headingCounts.put(level, headingCounts.getOrDefault(level, 0) + 1);
                            
                            // 保存第一个示例
                            if (!headingExamples.containsKey(level)) {
                                String text = getParagraphText(para);
                                headingExamples.put(level, text);
                            }
                            break; // 找到级别后跳出循环
                        }
                    }
                }
            }
            
            // 输出结果
            System.out.println("\n📊 标题级别检测结果:");
            boolean foundAnyHeading = false;
            
            for (int level = 1; level <= 6; level++) {
                int count = headingCounts.getOrDefault(level, 0);
                if (count > 0) {
                    foundAnyHeading = true;
                    String example = headingExamples.get(level);
                    String preview = example.length() > 40 ? example.substring(0, 40) + "..." : example;
                    System.out.printf("✅ Heading%d: %d个 (示例: %s)%n", level, count, preview);
                } else {
                    System.out.printf("❌ Heading%d: 未找到%n", level);
                }
            }
            
            if (foundAnyHeading) {
                System.out.println("\n🎉 成功！docx4j可以正确读取Word文档中的标题样式！");
                
                // 验证我们的分割服务是否能正常工作
                System.out.println("\n🔧 测试分割服务...");
                testSplitterService(testFilePath);
                
            } else {
                System.out.println("\n⚠️ 未找到任何标题，可能的原因：");
                System.out.println("1. 文档中没有使用标准的标题样式");
                System.out.println("2. 标题样式名称不是标准格式（Heading1, Heading2等）");
                System.out.println("3. 文档内容为空");
                
                // 显示所有样式
                showAllStyles(content);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    private void testSplitterService(String filePath) {
        try {
            // 这里可以测试我们的分割服务
            System.out.println("分割服务测试将在实际使用时进行");
            System.out.println("现在已确认docx4j可以正确读取标题样式");
        } catch (Exception e) {
            System.err.println("分割服务测试失败: " + e.getMessage());
        }
    }
    
    private void showAllStyles(List<Object> content) {
        System.out.println("\n📋 文档中发现的所有样式:");
        Map<String, Integer> allStyles = new HashMap<>();
        Map<String, String> styleExamples = new HashMap<>();

        for (Object obj : content) {
            if (obj instanceof P) {
                P para = (P) obj;
                String styleId = getStyleId(para);
                String text = getParagraphText(para);

                if (styleId != null && !text.trim().isEmpty()) {
                    allStyles.put(styleId, allStyles.getOrDefault(styleId, 0) + 1);

                    // 保存每种样式的第一个示例
                    if (!styleExamples.containsKey(styleId)) {
                        String example = text.length() > 30 ? text.substring(0, 30) + "..." : text;
                        styleExamples.put(styleId, example);
                    }
                }
            }
        }

        if (allStyles.isEmpty()) {
            System.out.println("❌ 未找到任何有内容的段落样式");
            return;
        }

        allStyles.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> {
                    String style = entry.getKey();
                    int count = entry.getValue();
                    String example = styleExamples.get(style);
                    String marker = style.toLowerCase().contains("heading") ||
                                   style.toLowerCase().contains("标题") ||
                                   style.toLowerCase().contains("title") ? "🏷️" : "📄";
                    System.out.printf("%s %s: %d个 (示例: %s)%n", marker, style, count, example);
                });

        // 特别检查可能的标题样式
        System.out.println("\n🔍 可能的标题样式分析:");
        for (String style : allStyles.keySet()) {
            if (isPossibleHeadingStyle(style)) {
                System.out.printf("🎯 发现可能的标题样式: %s (示例: %s)%n",
                    style, styleExamples.get(style));
            }
        }
    }

    private boolean isPossibleHeadingStyle(String styleId) {
        if (styleId == null) return false;
        String lower = styleId.toLowerCase();
        return lower.contains("heading") ||
               lower.contains("标题") ||
               lower.contains("title") ||
               lower.matches(".*h[1-6].*") ||  // h1, h2, etc.
               lower.matches(".*[1-6]级.*") || // 1级标题, 2级标题
               lower.contains("chapter") ||
               lower.contains("section");
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isHeadingLevel(P para, int level) {
        String styleId = getStyleId(para);
        if (styleId == null) return false;
        
        // 检查标准格式
        String standardStyle = "Heading" + level;
        if (standardStyle.equals(styleId)) return true;
        
        // 检查其他可能的格式
        String[] possibleFormats = {
            "heading " + level,        // "heading 1"
            "heading" + level,         // "heading1" 
            "Heading " + level,        // "Heading 1"
            "HEADING" + level,         // "HEADING1"
            "HEADING " + level         // "HEADING 1"
        };
        
        for (String format : possibleFormats) {
            if (format.equals(styleId)) return true;
        }
        
        return false;
    }
    
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
