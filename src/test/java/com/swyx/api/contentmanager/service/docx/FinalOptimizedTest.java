package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * 最终优化功能测试（使用正确的字数限制）
 */
@SpringBootTest
public class FinalOptimizedTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testFinalOptimizedLogic() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "final-optimized-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 最终优化功能测试（正确字数限制） ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n");
        result.append("文档总字数: 137字\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                // 测试1: 大字数限制（不分割）
                result.append("=== 测试1: 大字数限制（不分割）===\n");
                String outputDir1 = "test-output-no-split";
                int largeLimit = 200; // 大于137字
                
                List<File> files1 = docx4jSplitterService.splitByHeading(testFilePath, outputDir1, largeLimit);
                result.append("字数限制: ").append(largeLimit).append(" (大于总字数137)\n");
                result.append("生成文件数: ").append(files1.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files1) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                boolean test1Pass = files1.size() == 1 && files1.get(0).getName().contains("_complete.docx");
                result.append(test1Pass ? "✅ 测试1通过：正确输出完整文档\n" : "❌ 测试1失败\n");
                
                // 测试2: 小字数限制（需要分割）
                result.append("\n=== 测试2: 小字数限制（需要分割）===\n");
                String outputDir2 = "test-output-split";
                int smallLimit = 50; // 小于137字，强制分割

                List<File> files2 = docx4jSplitterService.splitByHeading(testFilePath, outputDir2, smallLimit);
                result.append("字数限制: ").append(smallLimit).append(" (小于总字数137)\n");
                result.append("生成文件数: ").append(files2.size()).append("\n");
                result.append("文件列表:\n");

                // 分析每个分割文件的字数
                boolean allFilesWithinLimit = true;
                int totalWordsInSplitFiles = 0;

                for (int i = 0; i < files2.size(); i++) {
                    File file = files2.get(i);
                    try {
                        // 计算分割文件的字数
                        int fileWordCount = calculateFileWordCount(file.getAbsolutePath());
                        totalWordsInSplitFiles += fileWordCount;

                        result.append(String.format("  %d. %s\n", i + 1, file.getName()));
                        result.append(String.format("     文件大小: %d 字节\n", file.length()));
                        result.append(String.format("     文件字数: %d 字\n", fileWordCount));
                        result.append(String.format("     是否超过限制: %s\n",
                            fileWordCount > smallLimit ? "❌ 是 (超过" + (fileWordCount - smallLimit) + "字)" : "✅ 否"));

                        if (fileWordCount > smallLimit) {
                            allFilesWithinLimit = false;
                        }

                    } catch (Exception e) {
                        result.append(String.format("     ❌ 字数计算失败: %s\n", e.getMessage()));
                        allFilesWithinLimit = false;
                    }
                    result.append("\n");
                }

                result.append(String.format("分割文件总字数: %d (原文档137字)\n", totalWordsInSplitFiles));
                result.append(String.format("字数是否匹配: %s\n",
                    Math.abs(totalWordsInSplitFiles - 137) <= 5 ? "✅ 是" : "❌ 否 (差异:" + (totalWordsInSplitFiles - 137) + "字)"));
                result.append(String.format("所有文件都在限制内: %s\n", allFilesWithinLimit ? "✅ 是" : "❌ 否"));

                boolean test2Pass = files2.size() > 1 && allFilesWithinLimit;
                result.append(test2Pass ? "✅ 测试2通过：正确进行了分割且符合字数限制\n" : "❌ 测试2失败：分割不当或超过字数限制\n");
                
                // 测试3: 临界字数限制
                result.append("\n=== 测试3: 临界字数限制 ===\n");
                String outputDir3 = "test-output-critical";
                int criticalLimit = 137; // 等于总字数

                List<File> files3 = docx4jSplitterService.splitByHeading(testFilePath, outputDir3, criticalLimit);
                result.append("字数限制: ").append(criticalLimit).append(" (等于总字数137)\n");
                result.append("生成文件数: ").append(files3.size()).append("\n");
                result.append("文件列表:\n");

                for (File file : files3) {
                    try {
                        int fileWordCount = calculateFileWordCount(file.getAbsolutePath());
                        result.append(String.format("  - %s (大小: %d 字节, 字数: %d)\n",
                            file.getName(), file.length(), fileWordCount));
                    } catch (Exception e) {
                        result.append(String.format("  - %s (大小: %d 字节, 字数计算失败)\n",
                            file.getName(), file.length()));
                    }
                }

                boolean test3Pass = files3.size() == 1 && files3.get(0).getName().contains("_complete.docx");
                result.append(test3Pass ? "✅ 测试3通过：临界值正确处理\n" : "❌ 测试3失败\n");

                // 测试4: 更合理的字数限制（测试分割策略）
                result.append("\n=== 测试4: 合理字数限制（分割策略分析）===\n");
                String outputDir4 = "test-output-reasonable";
                int reasonableLimit = 30; // 更小的限制，强制分割

                List<File> files4 = docx4jSplitterService.splitByHeading(testFilePath, outputDir4, reasonableLimit);
                result.append("字数限制: ").append(reasonableLimit).append(" (更小的限制)\n");
                result.append("生成文件数: ").append(files4.size()).append("\n");
                result.append("分割策略分析:\n");

                boolean allFilesWithinReasonableLimit = true;
                int maxWordCountInFiles = 0;

                for (int i = 0; i < files4.size(); i++) {
                    File file = files4.get(i);
                    try {
                        int fileWordCount = calculateFileWordCount(file.getAbsolutePath());
                        maxWordCountInFiles = Math.max(maxWordCountInFiles, fileWordCount);

                        result.append(String.format("  %d. %s\n", i + 1, file.getName()));
                        result.append(String.format("     字数: %d 字 ", fileWordCount));

                        if (fileWordCount > reasonableLimit) {
                            result.append(String.format("❌ 超过限制 %d 字\n", fileWordCount - reasonableLimit));
                            allFilesWithinReasonableLimit = false;
                        } else {
                            result.append("✅ 符合限制\n");
                        }

                        // 分析文件类型
                        if (file.getName().contains("_complete.docx")) {
                            result.append("     类型: 完整文档\n");
                        } else if (file.getName().contains("_chapter_")) {
                            result.append("     类型: 章节文档\n");
                        }

                    } catch (Exception e) {
                        result.append(String.format("     ❌ 字数计算失败: %s\n", e.getMessage()));
                        allFilesWithinReasonableLimit = false;
                    }
                    result.append("\n");
                }

                result.append(String.format("最大文件字数: %d (限制: %d)\n", maxWordCountInFiles, reasonableLimit));
                result.append(String.format("分割策略评估: %s\n",
                    allFilesWithinReasonableLimit ? "✅ 所有文件都符合限制" : "❌ 存在超过限制的文件"));

                boolean test4Pass = files4.size() > 1 && allFilesWithinReasonableLimit;
                result.append(test4Pass ? "✅ 测试4通过：分割策略合理\n" : "❌ 测试4失败：分割策略需要优化\n");

                // 验证文件名前缀
                result.append("\n=== 文件名前缀验证 ===\n");
                String expectedPrefix = "multi-level-headings";
                boolean allHavePrefix = true;

                // 检查所有测试的文件
                List<List<File>> allTestFiles = List.of(files1, files2, files3, files4);
                for (List<File> testFiles : allTestFiles) {
                    for (File file : testFiles) {
                        if (!file.getName().startsWith(expectedPrefix)) {
                            allHavePrefix = false;
                            result.append("❌ 文件名缺少前缀: ").append(file.getName()).append("\n");
                        }
                    }
                }

                if (allHavePrefix) {
                    result.append("✅ 所有文件都包含正确的原文件名前缀\n");
                } else {
                    result.append("❌ 部分文件缺少原文件名前缀\n");
                }
                
                // 总结
                result.append("\n=== 优化功能测试总结 ===\n");
                result.append("1. 大字数限制不分割: ").append(test1Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("2. 小字数限制分割: ").append(test2Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("3. 临界值处理: ").append(test3Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("4. 合理分割策略: ").append(test4Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("5. 文件名前缀: ").append(allHavePrefix ? "✅ 通过" : "❌ 失败").append("\n");

                int passedTests = 0;
                if (test1Pass) passedTests++;
                if (test2Pass) passedTests++;
                if (test3Pass) passedTests++;
                if (test4Pass) passedTests++;
                if (allHavePrefix) passedTests++;

                result.append("\n🎯 最终测试结果: ").append(passedTests).append("/5 通过\n");

                if (passedTests >= 4) {
                    result.append("🎉 优化功能基本正常工作！\n");
                    result.append("\n✅ 优化功能确认:\n");
                    result.append("  - 智能字数判断：文档总字数 ≤ maxWordCount 时不分割\n");
                    result.append("  - 原文件名前缀：所有输出文件都包含原文件名\n");
                    result.append("  - 完整文档保存：不分割时直接保存原文档（保持所有格式）\n");
                    result.append("  - 分割文件命名：格式为 '原文件名_chapter_xx_标题.docx'\n");

                    if (!test4Pass) {
                        result.append("\n⚠️ 分割策略建议优化:\n");
                        result.append("  - 当前可能存在分割后文件仍超过字数限制的情况\n");
                        result.append("  - 建议检查段落级别的分割逻辑\n");
                        result.append("  - 考虑对超长标题进行特殊处理\n");
                    }
                } else {
                    result.append("⚠️ 部分功能需要进一步检查和优化\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + resultFilePath);
    }

    /**
     * 计算文件的字数
     */
    private int calculateFileWordCount(String filePath) throws Exception {
        org.docx4j.openpackaging.packages.WordprocessingMLPackage wordMLPackage =
            org.docx4j.Docx4J.load(new File(filePath));
        org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart documentPart =
            wordMLPackage.getMainDocumentPart();

        // 获取所有段落
        List<Object> paragraphs = getAllElementsFromObject(documentPart);

        // 转换为段落列表
        List<org.docx4j.wml.P> allParagraphs = new ArrayList<>();
        for (Object obj : paragraphs) {
            if (obj instanceof org.docx4j.wml.P) {
                allParagraphs.add((org.docx4j.wml.P) obj);
            }
        }

        // 计算字数
        int totalWords = 0;
        for (org.docx4j.wml.P para : allParagraphs) {
            String text = getParagraphText(para);
            totalWords += text.replaceAll("\\s+", "").length();
        }

        return totalWords;
    }

    /**
     * 获取所有段落元素
     */
    private List<Object> getAllElementsFromObject(org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart documentPart) {
        List<Object> result = new ArrayList<>();

        try {
            // 使用docx4j的TraversalUtil来安全地遍历所有元素
            org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                @Override
                public List<Object> apply(Object o) {
                    if (o instanceof org.docx4j.wml.P) {
                        result.add(o);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // 如果TraversalUtil失败，使用简单方法
            List<Object> content = documentPart.getContent();
            for (Object item : content) {
                if (item instanceof org.docx4j.wml.P) {
                    result.add(item);
                }
            }
        }

        return result;
    }

    /**
     * 获取段落文本内容
     */
    private String getParagraphText(org.docx4j.wml.P para) {
        try {
            // 使用docx4j的TextUtils来提取文本
            java.io.StringWriter writer = new java.io.StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 如果TextUtils失败，使用手动方法
        }

        // 手动提取文本的备用方法
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            } else if (runContent instanceof jakarta.xml.bind.JAXBElement) {
                                jakarta.xml.bind.JAXBElement<?> jaxbElement = (jakarta.xml.bind.JAXBElement<?>) runContent;
                                if (jaxbElement.getValue() instanceof org.docx4j.wml.Text) {
                                    org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) jaxbElement.getValue();
                                    if (textElement.getValue() != null) {
                                        text.append(textElement.getValue());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
