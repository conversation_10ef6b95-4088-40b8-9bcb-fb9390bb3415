package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * 最终优化功能测试（使用正确的字数限制）
 */
@SpringBootTest
public class FinalOptimizedTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testFinalOptimizedLogic() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "final-optimized-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 最终优化功能测试（正确字数限制） ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n");
        result.append("文档总字数: 137字\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                // 测试1: 大字数限制（不分割）
                result.append("=== 测试1: 大字数限制（不分割）===\n");
                String outputDir1 = "test-output-no-split";
                int largeLimit = 200; // 大于137字
                
                List<File> files1 = docx4jSplitterService.splitByHeading(testFilePath, outputDir1, largeLimit);
                result.append("字数限制: ").append(largeLimit).append(" (大于总字数137)\n");
                result.append("生成文件数: ").append(files1.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files1) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                boolean test1Pass = files1.size() == 1 && files1.get(0).getName().contains("_complete.docx");
                result.append(test1Pass ? "✅ 测试1通过：正确输出完整文档\n" : "❌ 测试1失败\n");
                
                // 测试2: 小字数限制（需要分割）
                result.append("\n=== 测试2: 小字数限制（需要分割）===\n");
                String outputDir2 = "test-output-split";
                int smallLimit = 50; // 小于137字，强制分割
                
                List<File> files2 = docx4jSplitterService.splitByHeading(testFilePath, outputDir2, smallLimit);
                result.append("字数限制: ").append(smallLimit).append(" (小于总字数137)\n");
                result.append("生成文件数: ").append(files2.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files2) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                boolean test2Pass = files2.size() > 1;
                result.append(test2Pass ? "✅ 测试2通过：正确进行了分割\n" : "❌ 测试2失败\n");
                
                // 测试3: 临界字数限制
                result.append("\n=== 测试3: 临界字数限制 ===\n");
                String outputDir3 = "test-output-critical";
                int criticalLimit = 137; // 等于总字数
                
                List<File> files3 = docx4jSplitterService.splitByHeading(testFilePath, outputDir3, criticalLimit);
                result.append("字数限制: ").append(criticalLimit).append(" (等于总字数137)\n");
                result.append("生成文件数: ").append(files3.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files3) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                boolean test3Pass = files3.size() == 1 && files3.get(0).getName().contains("_complete.docx");
                result.append(test3Pass ? "✅ 测试3通过：临界值正确处理\n" : "❌ 测试3失败\n");
                
                // 验证文件名前缀
                result.append("\n=== 文件名前缀验证 ===\n");
                String expectedPrefix = "multi-level-headings";
                boolean allHavePrefix = true;
                
                // 检查所有测试的文件
                for (File file : files1) {
                    if (!file.getName().startsWith(expectedPrefix)) {
                        allHavePrefix = false;
                        result.append("❌ 文件名缺少前缀: ").append(file.getName()).append("\n");
                    }
                }
                for (File file : files2) {
                    if (!file.getName().startsWith(expectedPrefix)) {
                        allHavePrefix = false;
                        result.append("❌ 文件名缺少前缀: ").append(file.getName()).append("\n");
                    }
                }
                for (File file : files3) {
                    if (!file.getName().startsWith(expectedPrefix)) {
                        allHavePrefix = false;
                        result.append("❌ 文件名缺少前缀: ").append(file.getName()).append("\n");
                    }
                }
                
                if (allHavePrefix) {
                    result.append("✅ 所有文件都包含正确的原文件名前缀\n");
                } else {
                    result.append("❌ 部分文件缺少原文件名前缀\n");
                }
                
                // 测试4: 验证分割后的文件内容
                result.append("\n=== 测试4: 分割文件内容验证 ===\n");
                if (files2.size() > 1) {
                    result.append("分割后的文件详情:\n");
                    for (int i = 0; i < files2.size(); i++) {
                        File file = files2.get(i);
                        result.append(String.format("文件 %d: %s\n", i + 1, file.getName()));
                        
                        // 检查文件是否包含预期的章节
                        if (file.getName().contains("chapter_01")) {
                            result.append("  ✅ 包含第1章内容\n");
                        } else if (file.getName().contains("chapter_02")) {
                            result.append("  ✅ 包含第2章内容\n");
                        } else if (file.getName().contains("chapter_03")) {
                            result.append("  ✅ 包含第3章内容\n");
                        }
                    }
                    result.append("✅ 测试4通过：分割文件结构正确\n");
                } else {
                    result.append("❌ 测试4跳过：没有分割文件可验证\n");
                }
                
                // 总结
                result.append("\n=== 优化功能测试总结 ===\n");
                result.append("1. 大字数限制不分割: ").append(test1Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("2. 小字数限制分割: ").append(test2Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("3. 临界值处理: ").append(test3Pass ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("4. 文件名前缀: ").append(allHavePrefix ? "✅ 通过" : "❌ 失败").append("\n");
                
                int passedTests = 0;
                if (test1Pass) passedTests++;
                if (test2Pass) passedTests++;
                if (test3Pass) passedTests++;
                if (allHavePrefix) passedTests++;
                
                result.append("\n🎯 最终测试结果: ").append(passedTests).append("/4 通过\n");
                
                if (passedTests == 4) {
                    result.append("🎉 所有优化功能都完美工作！\n");
                    result.append("\n✅ 优化功能确认:\n");
                    result.append("  - 智能字数判断：文档总字数 ≤ maxWordCount 时不分割\n");
                    result.append("  - 原文件名前缀：所有输出文件都包含原文件名\n");
                    result.append("  - 完整文档保存：不分割时直接保存原文档（保持所有格式）\n");
                    result.append("  - 分割文件命名：格式为 '原文件名_chapter_xx_标题.docx'\n");
                } else {
                    result.append("⚠️ 部分功能需要进一步检查\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + resultFilePath);
    }
}
