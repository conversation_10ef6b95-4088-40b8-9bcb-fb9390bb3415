package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标题检测测试
 */
@SpringBootTest
public class HeadingDetectionTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testHeadingDetection() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String outputFilePath = "heading-detection-result.txt";
        
        File docFile = new File(testFilePath);
        
        StringBuilder result = new StringBuilder();
        result.append("=== 标题检测测试结果 ===\n");
        result.append("文档路径: ").append(testFilePath).append("\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        if (!docFile.exists()) {
            result.append("❌ 文档不存在\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                List<Object> content = documentPart.getContent();
                
                result.append("✅ 文档加载成功\n");
                result.append("📄 文档元素总数: ").append(content.size()).append("\n\n");
                
                Map<Integer, Integer> headingCounts = new HashMap<>();
                Map<Integer, String> headingExamples = new HashMap<>();
                
                int paragraphIndex = 0;
                
                // 分析每个段落
                for (Object obj : content) {
                    if (obj instanceof P) {
                        paragraphIndex++;
                        P para = (P) obj;
                        String styleId = getStyleId(para);
                        String text = getParagraphText(para);
                        
                        result.append("段落 ").append(paragraphIndex).append(": ");
                        result.append("样式=").append(styleId).append(", ");
                        result.append("文本=").append(text.isEmpty() ? "[空]" : text).append("\n");
                        
                        // 检查每个标题级别
                        for (int level = 1; level <= 6; level++) {
                            if (isHeadingLevel(para, level)) {
                                headingCounts.put(level, headingCounts.getOrDefault(level, 0) + 1);
                                if (!headingExamples.containsKey(level)) {
                                    headingExamples.put(level, text);
                                }
                                result.append("  ✅ 检测为 Heading").append(level).append("\n");
                                break;
                            }
                        }
                    }
                }
                
                result.append("\n📊 标题检测统计:\n");
                result.append("-".repeat(40)).append("\n");
                
                boolean foundAnyHeading = false;
                for (int level = 1; level <= 6; level++) {
                    int count = headingCounts.getOrDefault(level, 0);
                    if (count > 0) {
                        foundAnyHeading = true;
                        String example = headingExamples.get(level);
                        result.append(String.format("✅ Heading%d: %d个 (示例: %s)%n", level, count, example));
                    } else {
                        result.append(String.format("❌ Heading%d: 未找到%n", level));
                    }
                }
                
                if (foundAnyHeading) {
                    result.append("\n🎉 成功！现在可以正确识别你文档中的标题了！\n");
                    
                    // 测试分割功能
                    result.append("\n🔧 测试分割功能...\n");
                    try {
                        List<File> splitFiles = docx4jSplitterService.splitByHeading(testFilePath, "test-output", 500);
                        result.append("✅ 分割成功！生成了 ").append(splitFiles.size()).append(" 个文件:\n");
                        for (File file : splitFiles) {
                            result.append("  - ").append(file.getName()).append("\n");
                        }
                    } catch (Exception e) {
                        result.append("❌ 分割测试失败: ").append(e.getMessage()).append("\n");
                    }
                    
                } else {
                    result.append("\n⚠️ 仍然未找到任何标题\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入文件
        try (FileWriter writer = new FileWriter(outputFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + outputFilePath);
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isHeadingLevel(P para, int level) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            String styleId = para.getPPr().getPStyle().getVal();
            if (styleId == null) return false;
            
            // 检查标准格式 "Heading1", "Heading2", etc.
            String standardStyle = "Heading" + level;
            if (standardStyle.equals(styleId)) return true;
            
            // 检查数字格式 "1", "2", "3", "4", "5", "6" (你的文档使用的格式)
            if (String.valueOf(level).equals(styleId)) return true;
            
            // 检查中文标题格式
            String[] chineseFormats = {
                "标题" + level,           // "标题1"
                "标题 " + level,          // "标题 1"
                "标题" + level + "级",     // "标题1级"
            };
            
            for (String format : chineseFormats) {
                if (format.equals(styleId)) return true;
            }
            
            // 检查可能的其他格式变体
            String[] possibleFormats = {
                "heading " + level,        // "heading 1"
                "heading" + level,         // "heading1" 
                "Heading " + level,        // "Heading 1"
                "HEADING" + level,         // "HEADING1"
                "HEADING " + level,        // "HEADING 1"
                "h" + level,               // "h1", "h2"
                "H" + level,               // "H1", "H2"
                level + "级标题",           // "1级标题"
                level + "级",              // "1级"
            };
            
            for (String format : possibleFormats) {
                if (format.equals(styleId)) return true;
            }
        }
        return false;
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
