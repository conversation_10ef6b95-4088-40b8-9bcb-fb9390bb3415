package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/**
 * 分析Word文档中的标题级别
 * 验证docx4j是否能正确读取Heading1-Heading6
 */
@Component
public class HeadingLevelAnalyzer {

    /**
     * 分析文档中的所有标题级别
     */
    public void analyzeHeadingLevels(String filePath) {
        try {
            System.out.println("=== 分析文档标题级别: " + filePath + " ===");
            
            WordprocessingMLPackage wordMLPackage = Docx4J.load(new File(filePath));
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            
            // 获取所有段落
            List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);
            
            // 统计各级标题
            Map<Integer, List<String>> headingsByLevel = new HashMap<>();
            Map<String, Integer> allStyles = new HashMap<>();
            
            for (Object obj : paragraphs) {
                if (obj instanceof P) {
                    P para = (P) obj;
                    String styleId = getStyleId(para);
                    String text = getParagraphText(para);
                    
                    // 统计所有样式
                    if (styleId != null && !text.trim().isEmpty()) {
                        allStyles.put(styleId, allStyles.getOrDefault(styleId, 0) + 1);
                    }
                    
                    // 检查是否为标题
                    int headingLevel = getHeadingLevel(para);
                    if (headingLevel > 0 && !text.trim().isEmpty()) {
                        headingsByLevel.computeIfAbsent(headingLevel, k -> new ArrayList<>()).add(text.trim());
                    }
                }
            }
            
            // 输出标题分析结果
            System.out.println("\n📋 标题级别统计:");
            for (int level = 1; level <= 6; level++) {
                List<String> headings = headingsByLevel.get(level);
                if (headings != null && !headings.isEmpty()) {
                    System.out.printf("✅ Heading%d: %d个%n", level, headings.size());
                    // 显示前3个示例
                    for (int i = 0; i < Math.min(3, headings.size()); i++) {
                        String heading = headings.get(i);
                        String preview = heading.length() > 50 ? heading.substring(0, 50) + "..." : heading;
                        System.out.printf("   %d. %s%n", i + 1, preview);
                    }
                    if (headings.size() > 3) {
                        System.out.printf("   ... 还有%d个%n", headings.size() - 3);
                    }
                } else {
                    System.out.printf("❌ Heading%d: 未找到%n", level);
                }
            }
            
            // 输出所有样式统计
            System.out.println("\n📊 所有段落样式统计:");
            allStyles.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> {
                        String style = entry.getKey();
                        int count = entry.getValue();
                        String indicator = isHeadingStyle(style) ? "🏷️" : "📄";
                        System.out.printf("%s %s: %d个%n", indicator, style, count);
                    });
            
        } catch (Exception e) {
            System.err.println("❌ 分析文档失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查样式是否为标题样式
     */
    private boolean isHeadingStyle(String styleId) {
        if (styleId == null) return false;
        String lower = styleId.toLowerCase();
        return lower.contains("heading") || lower.contains("标题") || lower.contains("title");
    }
    
    /**
     * 获取段落的样式ID
     */
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal"; // 默认样式
    }
    
    /**
     * 获取段落的标题级别
     */
    private int getHeadingLevel(P para) {
        String styleId = getStyleId(para);
        if (styleId == null) return 0;
        
        // 检查标准的Heading1-Heading6格式
        for (int level = 1; level <= 6; level++) {
            if (("Heading" + level).equals(styleId)) {
                return level;
            }
        }
        
        // 检查其他可能的格式
        String lower = styleId.toLowerCase();
        if (lower.startsWith("heading")) {
            // 尝试提取数字
            String numberPart = lower.replace("heading", "").trim();
            try {
                int level = Integer.parseInt(numberPart);
                if (level >= 1 && level <= 6) {
                    return level;
                }
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }
        
        return 0; // 不是标题
    }
    
    /**
     * 获取段落文本内容
     */
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString();
    }
    
    /**
     * 递归获取所有指定类型的元素
     */
    private List<Object> getAllElementsFromObject(Object obj, Class<?> toSearch) {
        List<Object> result = new ArrayList<>();
        if (obj instanceof org.w3c.dom.Node) {
            org.w3c.dom.Node node = (org.w3c.dom.Node) obj;
            if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                if (toSearch.isInstance(node)) {
                    result.add(node);
                }
                org.w3c.dom.NodeList children = node.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    result.addAll(getAllElementsFromObject(children.item(i), toSearch));
                }
            }
        } else if (obj instanceof jakarta.xml.bind.JAXBElement) {
            result.addAll(getAllElementsFromObject(((jakarta.xml.bind.JAXBElement<?>) obj).getValue(), toSearch));
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object o : list) {
                result.addAll(getAllElementsFromObject(o, toSearch));
            }
        } else if (obj != null) {
            if (toSearch.isInstance(obj)) {
                result.add(obj);
            }
            // 递归处理对象的字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        result.addAll(getAllElementsFromObject(fieldValue, toSearch));
                    }
                } catch (IllegalAccessException e) {
                    // 忽略访问异常
                }
            }
        }
        return result;
    }
    
    /**
     * 创建一个测试用的Word文档结构示例
     */
    public void printExpectedStructure() {
        System.out.println("=== Word文档标题结构示例 ===");
        System.out.println("在Word中，标题样式通常命名为:");
        System.out.println("✅ Heading1 - 一级标题");
        System.out.println("✅ Heading2 - 二级标题");  
        System.out.println("✅ Heading3 - 三级标题");
        System.out.println("✅ Heading4 - 四级标题");
        System.out.println("✅ Heading5 - 五级标题");
        System.out.println("✅ Heading6 - 六级标题");
        System.out.println();
        System.out.println("docx4j可以读取这些样式信息，通过:");
        System.out.println("para.getPPr().getPStyle().getVal()");
        System.out.println();
        System.out.println("注意: 不同版本的Word或不同语言环境可能有细微差异");
    }
}
