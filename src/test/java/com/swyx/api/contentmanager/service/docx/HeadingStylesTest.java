package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class HeadingStylesTest {

    @Autowired
    private HeadingStylesTestService headingStylesTestService;

    @Test
    public void testAnalyzeDocumentStyles() throws Exception {
        // 测试文档路径（请根据实际情况修改）
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        
        try {
            // 分析文档中的所有样式
            headingStylesTestService.analyzeDocumentStyles(testFilePath);
        } catch (Exception e) {
            System.err.println("无法分析文档: " + e.getMessage());
            System.out.println("请确保测试文档存在: " + testFilePath);
        }
    }

    @Test
    public void testAllHeadingLevels() throws Exception {
        // 测试文档路径
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        
        try {
            // 测试所有标题级别的识别
            headingStylesTestService.testAllHeadingLevels(testFilePath);
        } catch (Exception e) {
            System.err.println("无法测试标题级别: " + e.getMessage());
            System.out.println("请确保测试文档存在: " + testFilePath);
        }
    }

    @Test
    public void testMultipleDocuments() throws Exception {
        // 测试多个文档
        String[] testFiles = {
            "src/test/resources/document1.docx",
            "src/test/resources/document2.docx",
            "src/test/resources/complex-structure.docx"
        };
        
        for (String filePath : testFiles) {
            System.out.println("\n" + "=".repeat(80));
            System.out.println("测试文档: " + filePath);
            System.out.println("=".repeat(80));
            
            try {
                headingStylesTestService.analyzeDocumentStyles(filePath);
                headingStylesTestService.testAllHeadingLevels(filePath);
            } catch (Exception e) {
                System.err.println("跳过文档 " + filePath + ": " + e.getMessage());
            }
        }
    }
}
