package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * 测试和验证Word文档中各级标题样式的识别
 */
@Service
public class HeadingStylesTestService {

    /**
     * 分析文档中的所有段落样式
     */
    public void analyzeDocumentStyles(String filePath) throws Exception {
        System.out.println("=== 分析文档样式: " + filePath + " ===");
        
        WordprocessingMLPackage wordMLPackage = Docx4J.load(new File(filePath));
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        
        // 获取所有段落
        List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);
        
        Map<String, Integer> styleCount = new HashMap<>();
        Map<String, List<String>> styleExamples = new HashMap<>();
        
        for (Object obj : paragraphs) {
            if (obj instanceof P) {
                P para = (P) obj;
                String styleId = getStyleId(para);
                String text = getParagraphText(para);
                
                if (styleId != null && !text.trim().isEmpty()) {
                    styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                    
                    // 保存样式示例（最多3个）
                    styleExamples.computeIfAbsent(styleId, k -> new ArrayList<>());
                    if (styleExamples.get(styleId).size() < 3) {
                        String example = text.length() > 50 ? text.substring(0, 50) + "..." : text;
                        styleExamples.get(styleId).add(example);
                    }
                }
            }
        }
        
        // 输出分析结果
        System.out.println("发现的段落样式:");
        styleCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> {
                    String style = entry.getKey();
                    int count = entry.getValue();
                    System.out.printf("  %s: %d个段落%n", style, count);
                    
                    // 显示示例
                    List<String> examples = styleExamples.get(style);
                    if (examples != null && !examples.isEmpty()) {
                        System.out.println("    示例:");
                        examples.forEach(example -> System.out.println("      - " + example));
                    }
                    System.out.println();
                });
        
        // 检查标题样式
        checkHeadingStyles(styleCount);
    }
    
    /**
     * 检查是否包含标准的标题样式
     */
    private void checkHeadingStyles(Map<String, Integer> styleCount) {
        System.out.println("=== 标题样式检查 ===");
        
        String[] headingStyles = {"Heading1", "Heading2", "Heading3", "Heading4", "Heading5", "Heading6"};
        String[] alternativeStyles = {"heading 1", "heading 2", "heading 3", "heading 4", "heading 5", "heading 6"};
        
        for (int i = 0; i < headingStyles.length; i++) {
            String standardStyle = headingStyles[i];
            String altStyle = alternativeStyles[i];
            
            boolean found = false;
            if (styleCount.containsKey(standardStyle)) {
                System.out.printf("✅ %s: %d个%n", standardStyle, styleCount.get(standardStyle));
                found = true;
            } else if (styleCount.containsKey(altStyle)) {
                System.out.printf("✅ %s (小写): %d个%n", altStyle, styleCount.get(altStyle));
                found = true;
            }
            
            if (!found) {
                System.out.printf("❌ %s: 未找到%n", standardStyle);
            }
        }
        
        // 检查其他可能的标题样式变体
        System.out.println("\n其他可能的标题样式:");
        styleCount.keySet().stream()
                .filter(style -> style.toLowerCase().contains("heading") || 
                               style.toLowerCase().contains("title") ||
                               style.toLowerCase().contains("标题"))
                .forEach(style -> System.out.println("  - " + style + ": " + styleCount.get(style) + "个"));
    }
    
    /**
     * 测试所有标题级别的识别
     */
    public void testAllHeadingLevels(String filePath) throws Exception {
        System.out.println("=== 测试所有标题级别识别 ===");
        
        WordprocessingMLPackage wordMLPackage = Docx4J.load(new File(filePath));
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        
        List<Object> paragraphs = getAllElementsFromObject(documentPart, P.class);
        
        for (int level = 1; level <= 6; level++) {
            System.out.printf("\n--- Heading%d 检测结果 ---%n", level);
            
            int count = 0;
            for (Object obj : paragraphs) {
                if (obj instanceof P) {
                    P para = (P) obj;
                    if (isHeadingLevel(para, level)) {
                        count++;
                        String text = getParagraphText(para);
                        String preview = text.length() > 60 ? text.substring(0, 60) + "..." : text;
                        System.out.printf("  %d. %s%n", count, preview);
                        
                        if (count >= 5) { // 最多显示5个示例
                            System.out.println("  ... (更多内容)");
                            break;
                        }
                    }
                }
            }
            
            if (count == 0) {
                System.out.printf("  未找到 Heading%d 样式的段落%n", level);
            } else {
                System.out.printf("  总计: %d个 Heading%d 段落%n", count, level);
            }
        }
    }
    
    /**
     * 获取段落的样式ID
     */
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return null;
    }
    
    /**
     * 检查段落是否为指定级别的标题
     */
    private boolean isHeadingLevel(P para, int level) {
        String styleId = getStyleId(para);
        if (styleId == null) return false;
        
        // 检查标准格式
        String standardStyle = "Heading" + level;
        if (standardStyle.equals(styleId)) return true;
        
        // 检查小写格式
        String lowerStyle = "heading " + level;
        if (lowerStyle.equals(styleId)) return true;
        
        // 检查其他可能的格式
        String[] possibleFormats = {
            "heading" + level,
            "Heading " + level,
            "HEADING" + level,
            "HEADING " + level,
            "标题" + level,
            "标题 " + level
        };
        
        for (String format : possibleFormats) {
            if (format.equals(styleId)) return true;
        }
        
        return false;
    }
    
    /**
     * 获取段落文本内容
     */
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
    
    /**
     * 获取文档中所有指定类型的元素
     */
    private List<Object> getAllElementsFromObject(Object obj, Class<?> toSearch) {
        List<Object> result = new ArrayList<>();
        if (obj instanceof org.w3c.dom.Node) {
            org.w3c.dom.Node node = (org.w3c.dom.Node) obj;
            if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                if (toSearch.isInstance(node)) {
                    result.add(node);
                }
                org.w3c.dom.NodeList children = node.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    result.addAll(getAllElementsFromObject(children.item(i), toSearch));
                }
            }
        } else if (obj instanceof jakarta.xml.bind.JAXBElement) {
            result.addAll(getAllElementsFromObject(((jakarta.xml.bind.JAXBElement<?>) obj).getValue(), toSearch));
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object o : list) {
                result.addAll(getAllElementsFromObject(o, toSearch));
            }
        } else if (obj != null) {
            if (toSearch.isInstance(obj)) {
                result.add(obj);
            }
            // 递归处理对象的字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        result.addAll(getAllElementsFromObject(fieldValue, toSearch));
                    }
                } catch (IllegalAccessException e) {
                    // 忽略
                }
            }
        }
        return result;
    }
}
