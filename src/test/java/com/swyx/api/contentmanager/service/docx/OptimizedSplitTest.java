package com.swyx.api.contentmanager.service.docx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * 优化后的分割功能测试
 */
@SpringBootTest
public class OptimizedSplitTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testOptimizedSplitLogic() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "optimized-split-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 优化后的分割功能测试 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                // 测试1: 大字数限制（不分割）
                result.append("=== 测试1: 大字数限制（应该不分割） ===\n");
                String outputDir1 = "test-output-large-limit";
                int largeLimit = 10000; // 设置很大的字数限制
                
                List<File> files1 = docx4jSplitterService.splitByHeading(testFilePath, outputDir1, largeLimit);
                result.append("字数限制: ").append(largeLimit).append("\n");
                result.append("生成文件数: ").append(files1.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files1) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                if (files1.size() == 1 && files1.get(0).getName().contains("_complete.docx")) {
                    result.append("✅ 测试1通过：正确识别为无需分割，输出完整文档\n");
                } else {
                    result.append("❌ 测试1失败：应该输出完整文档但进行了分割\n");
                }
                
                // 测试2: 小字数限制（需要分割）
                result.append("\n=== 测试2: 小字数限制（应该分割） ===\n");
                String outputDir2 = "test-output-small-limit";
                int smallLimit = 300; // 设置较小的字数限制
                
                List<File> files2 = docx4jSplitterService.splitByHeading(testFilePath, outputDir2, smallLimit);
                result.append("字数限制: ").append(smallLimit).append("\n");
                result.append("生成文件数: ").append(files2.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files2) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                if (files2.size() > 1) {
                    result.append("✅ 测试2通过：正确进行了分割\n");
                } else {
                    result.append("❌ 测试2失败：应该分割但没有分割\n");
                }
                
                // 测试3: 中等字数限制
                result.append("\n=== 测试3: 中等字数限制 ===\n");
                String outputDir3 = "test-output-medium-limit";
                int mediumLimit = 800;
                
                List<File> files3 = docx4jSplitterService.splitByHeading(testFilePath, outputDir3, mediumLimit);
                result.append("字数限制: ").append(mediumLimit).append("\n");
                result.append("生成文件数: ").append(files3.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files3) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                // 验证文件名前缀
                result.append("\n=== 文件名前缀验证 ===\n");
                boolean allHavePrefix = true;
                String expectedPrefix = "multi-level-headings";
                
                for (File file : files3) {
                    if (!file.getName().startsWith(expectedPrefix)) {
                        allHavePrefix = false;
                        result.append("❌ 文件名缺少前缀: ").append(file.getName()).append("\n");
                    }
                }
                
                if (allHavePrefix) {
                    result.append("✅ 所有文件都包含正确的原文件名前缀: ").append(expectedPrefix).append("\n");
                } else {
                    result.append("❌ 部分文件缺少原文件名前缀\n");
                }
                
                // 测试4: 纯段落分割
                result.append("\n=== 测试4: 纯段落分割功能 ===\n");
                String outputDir4 = "test-output-paragraphs";
                int paragraphLimit = 500;
                
                List<File> files4 = docx4jSplitterService.splitByParagraphsOnly(testFilePath, outputDir4, paragraphLimit);
                result.append("字数限制: ").append(paragraphLimit).append("\n");
                result.append("生成文件数: ").append(files4.size()).append("\n");
                result.append("文件列表:\n");
                for (File file : files4) {
                    result.append("  - ").append(file.getName()).append(" (大小: ").append(file.length()).append(" 字节)\n");
                }
                
                // 验证纯段落分割的文件名前缀
                boolean paragraphFilesHavePrefix = true;
                for (File file : files4) {
                    if (!file.getName().startsWith(expectedPrefix)) {
                        paragraphFilesHavePrefix = false;
                        break;
                    }
                }
                
                if (paragraphFilesHavePrefix) {
                    result.append("✅ 纯段落分割文件也包含正确的前缀\n");
                } else {
                    result.append("❌ 纯段落分割文件缺少前缀\n");
                }
                
                // 总结
                result.append("\n=== 测试总结 ===\n");
                result.append("1. 大字数限制不分割功能: ").append(files1.size() == 1 ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("2. 小字数限制分割功能: ").append(files2.size() > 1 ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("3. 文件名前缀功能: ").append(allHavePrefix ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("4. 纯段落分割前缀: ").append(paragraphFilesHavePrefix ? "✅ 通过" : "❌ 失败").append("\n");
                
                int passedTests = 0;
                if (files1.size() == 1) passedTests++;
                if (files2.size() > 1) passedTests++;
                if (allHavePrefix) passedTests++;
                if (paragraphFilesHavePrefix) passedTests++;
                
                result.append("\n🎯 测试结果: ").append(passedTests).append("/4 通过\n");
                
                if (passedTests == 4) {
                    result.append("🎉 所有优化功能都正常工作！\n");
                } else {
                    result.append("⚠️ 部分功能需要进一步检查\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + resultFilePath);
    }
}
