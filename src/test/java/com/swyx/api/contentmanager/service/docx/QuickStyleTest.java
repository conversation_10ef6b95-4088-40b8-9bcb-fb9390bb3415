package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 快速样式测试 - 将结果写入文件
 */
@SpringBootTest
public class QuickStyleTest {

    @Test
    public void quickAnalysis() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String outputFilePath = "style-analysis-result.txt";
        
        File docFile = new File(testFilePath);
        
        StringBuilder result = new StringBuilder();
        result.append("=== Word文档样式分析结果 ===\n");
        result.append("文档路径: ").append(testFilePath).append("\n");
        result.append("分析时间: ").append(new java.util.Date()).append("\n\n");
        
        if (!docFile.exists()) {
            result.append("❌ 文档不存在\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                List<Object> content = documentPart.getContent();
                
                result.append("✅ 文档加载成功\n");
                result.append("📄 文档元素总数: ").append(content.size()).append("\n");
                
                Map<String, Integer> styleCount = new HashMap<>();
                Map<String, String> styleExamples = new HashMap<>();
                int totalParagraphs = 0;
                int paragraphsWithContent = 0;
                
                // 分析所有段落
                for (Object obj : content) {
                    if (obj instanceof P) {
                        totalParagraphs++;
                        P para = (P) obj;
                        String styleId = getStyleId(para);
                        String text = getParagraphText(para);
                        
                        result.append("段落 ").append(totalParagraphs).append(": 样式=").append(styleId)
                              .append(", 文本长度=").append(text.length())
                              .append(", 内容=").append(text.length() > 30 ? text.substring(0, 30) + "..." : text)
                              .append("\n");
                        
                        if (!text.trim().isEmpty()) {
                            paragraphsWithContent++;
                            styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                            
                            if (!styleExamples.containsKey(styleId)) {
                                String example = text.length() > 50 ? text.substring(0, 50) + "..." : text;
                                styleExamples.put(styleId, example);
                            }
                        }
                    }
                }
                
                result.append("\n📊 段落统计:\n");
                result.append("  - 段落总数: ").append(totalParagraphs).append("\n");
                result.append("  - 有内容的段落: ").append(paragraphsWithContent).append("\n");
                result.append("  - 样式种类: ").append(styleCount.size()).append("\n\n");
                
                result.append("📋 所有样式详情:\n");
                result.append("-".repeat(80)).append("\n");
                
                styleCount.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .forEach(entry -> {
                            String style = entry.getKey();
                            int count = entry.getValue();
                            String example = styleExamples.get(style);
                            
                            String marker = isPossibleHeadingStyle(style) ? "🏷️" : "📄";
                            result.append(String.format("%s 样式: %-20s | 数量: %2d | 示例: %s%n", 
                                marker, style, count, example));
                        });
                
                // 特别分析可能的标题样式
                result.append("\n🔍 标题样式分析:\n");
                result.append("-".repeat(50)).append("\n");
                
                boolean foundPossibleHeadings = false;
                for (String style : styleCount.keySet()) {
                    if (isPossibleHeadingStyle(style)) {
                        foundPossibleHeadings = true;
                        result.append(String.format("🎯 可能的标题样式: %s (数量: %d, 示例: %s)%n", 
                            style, styleCount.get(style), styleExamples.get(style)));
                    }
                }
                
                if (!foundPossibleHeadings) {
                    result.append("❌ 未发现明显的标题样式\n");
                    result.append("\n💡 建议:\n");
                    result.append("1. 检查Word文档是否使用了标题样式\n");
                    result.append("2. 在Word中选择文本，查看样式面板中的样式名称\n");
                    result.append("3. 确保使用的是'标题1'、'标题2'或'Heading1'、'Heading2'等标准样式\n");
                    result.append("4. 如果使用的是中文版Word，样式可能是'标题 1'、'标题 2'等\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 分析失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入文件
        try (FileWriter writer = new FileWriter(outputFilePath)) {
            writer.write(result.toString());
        }
        
        // 也输出到控制台
        System.out.println(result.toString());
        System.out.println("分析结果已保存到: " + outputFilePath);
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isPossibleHeadingStyle(String styleId) {
        if (styleId == null) return false;
        String lower = styleId.toLowerCase();
        return lower.contains("heading") || 
               lower.contains("标题") || 
               lower.contains("title") ||
               lower.matches(".*h[1-6].*") ||
               lower.matches(".*[1-6]级.*") ||
               lower.contains("chapter") ||
               lower.contains("section") ||
               lower.startsWith("toc") ||
               lower.contains("outline");
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
