package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单的docx4j功能测试
 */
@SpringBootTest
public class SimpleDocxTest {

    @Test
    public void testDocx4jBasicFunctionality() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        File docFile = new File(testFilePath);
        
        System.out.println("=== 测试docx4j基本功能 ===");
        
        if (!docFile.exists()) {
            System.err.println("❌ 测试文档不存在: " + testFilePath);
            return;
        }
        
        try {
            // 测试加载文档
            System.out.println("1. 加载Word文档...");
            WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
            System.out.println("✅ 文档加载成功");
            
            // 测试获取主文档部分
            System.out.println("2. 获取主文档部分...");
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            System.out.println("✅ 主文档部分获取成功");
            
            // 测试获取内容
            System.out.println("3. 获取文档内容...");
            List<Object> content = documentPart.getContent();
            System.out.println("✅ 文档内容获取成功，共 " + content.size() + " 个元素");
            
            // 分析段落和样式
            System.out.println("4. 分析段落和样式...");
            Map<String, Integer> styleCount = new HashMap<>();
            int paragraphCount = 0;
            int headingCount = 0;
            
            for (Object obj : content) {
                if (obj instanceof P) {
                    paragraphCount++;
                    P para = (P) obj;
                    
                    String styleId = getStyleId(para);
                    String text = getParagraphText(para);
                    
                    if (styleId != null && !text.trim().isEmpty()) {
                        styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                        
                        if (isHeadingStyle(styleId)) {
                            headingCount++;
                            System.out.printf("   发现标题 [%s]: %s%n", styleId, 
                                text.length() > 50 ? text.substring(0, 50) + "..." : text);
                        }
                    }
                }
            }
            
            System.out.println("✅ 分析完成");
            System.out.println("   - 段落总数: " + paragraphCount);
            System.out.println("   - 标题总数: " + headingCount);
            System.out.println("   - 样式种类: " + styleCount.size());
            
            // 显示样式统计
            System.out.println("\n5. 样式统计:");
            styleCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> {
                        String style = entry.getKey();
                        int count = entry.getValue();
                        String marker = isHeadingStyle(style) ? "🏷️" : "📄";
                        System.out.printf("   %s %s: %d个%n", marker, style, count);
                    });
            
            // 测试标题级别检测
            System.out.println("\n6. 测试标题级别检测:");
            for (int level = 1; level <= 6; level++) {
                int levelCount = 0;
                for (Object obj : content) {
                    if (obj instanceof P) {
                        P para = (P) obj;
                        if (isHeadingLevel(para, level)) {
                            levelCount++;
                        }
                    }
                }
                if (levelCount > 0) {
                    System.out.printf("   ✅ Heading%d: %d个%n", level, levelCount);
                } else {
                    System.out.printf("   ❌ Heading%d: 未找到%n", level);
                }
            }
            
            System.out.println("\n🎉 docx4j功能测试完成！所有功能正常工作。");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isHeadingStyle(String styleId) {
        if (styleId == null) return false;
        return styleId.startsWith("Heading") || styleId.toLowerCase().contains("heading");
    }
    
    private boolean isHeadingLevel(P para, int level) {
        String styleId = getStyleId(para);
        return ("Heading" + level).equals(styleId);
    }
    
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
