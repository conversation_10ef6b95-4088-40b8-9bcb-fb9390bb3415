package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单的标题级别测试类
 * 直接使用docx4j API读取文档并分析标题
 */
@SpringBootTest
public class SimpleHeadingTest {

    @Test
    public void testDocumentHeadings() throws Exception {
        // 测试文档路径
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        File docFile = new File(testFilePath);
        
        if (!docFile.exists()) {
            System.err.println("❌ 测试文档不存在: " + testFilePath);
            System.out.println("请确保在此路径创建包含多级标题的Word文档");
            return;
        }
        
        System.out.println("=== 分析文档: " + testFilePath + " ===");
        System.out.println("文件大小: " + (docFile.length() / 1024) + " KB");
        
        try {
            // 加载文档
            WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            
            // 获取所有段落
            List<Object> paragraphs = documentPart.getContent();
            System.out.println("文档中的段落总数: " + paragraphs.size());
            
            // 统计标题
            Map<String, Integer> styleCount = new HashMap<>();
            int totalHeadings = 0;
            
            for (Object obj : paragraphs) {
                if (obj instanceof P) {
                    P para = (P) obj;
                    String styleId = getStyleId(para);
                    String text = getParagraphText(para);
                    
                    if (styleId != null && !text.trim().isEmpty()) {
                        styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                        
                        // 检查是否为标题
                        if (isHeadingStyle(styleId)) {
                            totalHeadings++;
                            System.out.printf("发现标题 [%s]: %s%n", styleId, text);
                        }
                    }
                }
            }
            
            // 输出统计结果
            System.out.println("\n=== 样式统计 ===");
            System.out.println("总标题数: " + totalHeadings);
            
            for (Map.Entry<String, Integer> entry : styleCount.entrySet()) {
                String style = entry.getKey();
                int count = entry.getValue();
                String marker = isHeadingStyle(style) ? "✅" : "📄";
                System.out.printf("%s %s: %d个%n", marker, style, count);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 分析文档时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取段落的样式ID
     */
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return null;
    }
    
    /**
     * 检查是否为标题样式
     */
    private boolean isHeadingStyle(String styleId) {
        if (styleId == null) return false;
        
        // 检查标准标题格式
        for (int i = 1; i <= 6; i++) {
            if (styleId.equals("Heading" + i)) {
                return true;
            }
        }
        
        // 检查其他可能的标题格式
        String lower = styleId.toLowerCase();
        return lower.contains("heading") || lower.contains("标题") || lower.contains("title");
    }
    
    /**
     * 获取段落文本内容
     */
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    for (Object runContent : run.getContent()) {
                        if (runContent instanceof org.docx4j.wml.Text) {
                            text.append(((org.docx4j.wml.Text) runContent).getValue());
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
