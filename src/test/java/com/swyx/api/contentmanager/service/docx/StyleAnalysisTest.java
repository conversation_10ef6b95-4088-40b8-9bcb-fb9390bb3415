package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样式分析测试 - 专门用于分析Word文档中的样式
 */
@SpringBootTest
public class StyleAnalysisTest {

    @Test
    public void analyzeDocumentStyles() throws Exception {
        // 强制输出到控制台
        System.out.flush();
        System.err.flush();
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        File docFile = new File(testFilePath);
        
        System.out.println("=".repeat(60));
        System.out.println("📋 Word文档样式分析");
        System.out.println("=".repeat(60));
        System.out.println("文档路径: " + testFilePath);
        
        if (!docFile.exists()) {
            System.err.println("❌ 文档不存在");
            return;
        }
        
        try {
            WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
            MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
            List<Object> content = documentPart.getContent();
            
            System.out.println("✅ 文档加载成功");
            System.out.println("📄 文档元素总数: " + content.size());
            
            Map<String, Integer> styleCount = new HashMap<>();
            Map<String, String> styleExamples = new HashMap<>();
            int totalParagraphs = 0;
            int paragraphsWithContent = 0;
            
            // 分析所有段落
            for (Object obj : content) {
                if (obj instanceof P) {
                    totalParagraphs++;
                    P para = (P) obj;
                    String styleId = getStyleId(para);
                    String text = getParagraphText(para);
                    
                    if (!text.trim().isEmpty()) {
                        paragraphsWithContent++;
                        styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                        
                        if (!styleExamples.containsKey(styleId)) {
                            String example = text.length() > 50 ? text.substring(0, 50) + "..." : text;
                            styleExamples.put(styleId, example);
                        }
                    }
                }
            }
            
            System.out.println("📊 段落统计:");
            System.out.println("  - 段落总数: " + totalParagraphs);
            System.out.println("  - 有内容的段落: " + paragraphsWithContent);
            System.out.println("  - 样式种类: " + styleCount.size());
            
            System.out.println("\n📋 所有样式详情:");
            System.out.println("-".repeat(80));
            
            styleCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> {
                        String style = entry.getKey();
                        int count = entry.getValue();
                        String example = styleExamples.get(style);
                        
                        String marker = "📄";
                        if (isPossibleHeadingStyle(style)) {
                            marker = "🏷️";
                        }
                        
                        System.out.printf("%s 样式: %-20s | 数量: %2d | 示例: %s%n", 
                            marker, style, count, example);
                    });
            
            // 特别分析可能的标题样式
            System.out.println("\n🔍 标题样式分析:");
            System.out.println("-".repeat(50));
            
            boolean foundPossibleHeadings = false;
            for (String style : styleCount.keySet()) {
                if (isPossibleHeadingStyle(style)) {
                    foundPossibleHeadings = true;
                    System.out.printf("🎯 可能的标题样式: %s (数量: %d, 示例: %s)%n", 
                        style, styleCount.get(style), styleExamples.get(style));
                }
            }
            
            if (!foundPossibleHeadings) {
                System.out.println("❌ 未发现明显的标题样式");
                System.out.println("\n💡 建议:");
                System.out.println("1. 检查Word文档是否使用了标题样式");
                System.out.println("2. 在Word中选择文本，查看样式面板中的样式名称");
                System.out.println("3. 确保使用的是'标题1'、'标题2'或'Heading1'、'Heading2'等标准样式");
            }
            
            System.out.println("\n" + "=".repeat(60));
            
        } catch (Exception e) {
            System.err.println("❌ 分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isPossibleHeadingStyle(String styleId) {
        if (styleId == null) return false;
        String lower = styleId.toLowerCase();
        return lower.contains("heading") || 
               lower.contains("标题") || 
               lower.contains("title") ||
               lower.matches(".*h[1-6].*") ||
               lower.matches(".*[1-6]级.*") ||
               lower.contains("chapter") ||
               lower.contains("section") ||
               lower.startsWith("toc") ||  // Table of Contents
               lower.contains("outline");
    }
    
    private String getParagraphText(P para) {
        try {
            // 使用docx4j的TextUtils来提取文本
            java.io.StringWriter writer = new java.io.StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 如果TextUtils失败，继续使用简单的方法
        }

        // 简单的文本提取方法
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            } else if (runContent instanceof jakarta.xml.bind.JAXBElement) {
                                jakarta.xml.bind.JAXBElement<?> jaxbElement = (jakarta.xml.bind.JAXBElement<?>) runContent;
                                if (jaxbElement.getValue() instanceof org.docx4j.wml.Text) {
                                    org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) jaxbElement.getValue();
                                    if (textElement.getValue() != null) {
                                        text.append(textElement.getValue());
                                    }
                                }
                            }
                        }
                    }
                } else if (content instanceof jakarta.xml.bind.JAXBElement) {
                    jakarta.xml.bind.JAXBElement<?> jaxbElement = (jakarta.xml.bind.JAXBElement<?>) content;
                    if (jaxbElement.getValue() instanceof org.docx4j.wml.R) {
                        org.docx4j.wml.R run = (org.docx4j.wml.R) jaxbElement.getValue();
                        if (run.getContent() != null) {
                            for (Object runContent : run.getContent()) {
                                if (runContent instanceof org.docx4j.wml.Text) {
                                    org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                    if (textElement.getValue() != null) {
                                        text.append(textElement.getValue());
                                    }
                                } else if (runContent instanceof jakarta.xml.bind.JAXBElement) {
                                    jakarta.xml.bind.JAXBElement<?> innerJaxb = (jakarta.xml.bind.JAXBElement<?>) runContent;
                                    if (innerJaxb.getValue() instanceof org.docx4j.wml.Text) {
                                        org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) innerJaxb.getValue();
                                        if (textElement.getValue() != null) {
                                            text.append(textElement.getValue());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }

}
