package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样式保持测试
 * 测试分割后的文档是否保持了原有的样式
 */
@SpringBootTest
public class StylePreservationTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testStylePreservation() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String outputDirPath = "test-output-with-styles";
        String resultFilePath = "style-preservation-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 样式保持测试结果 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                // 1. 分析原文档的样式
                result.append("1. 分析原文档样式...\n");
                Map<String, Integer> originalStyles = analyzeDocumentStyles(testFilePath);
                result.append("原文档样式统计:\n");
                originalStyles.forEach((style, count) -> 
                    result.append("  - ").append(style).append(": ").append(count).append("个\n"));
                
                // 2. 执行分割
                result.append("\n2. 执行文档分割...\n");
                List<File> splitFiles = docx4jSplitterService.splitByHeading(testFilePath, outputDirPath, 300);
                result.append("✅ 分割成功！生成了 ").append(splitFiles.size()).append(" 个文件:\n");
                
                // 3. 分析每个分割后文档的样式
                result.append("\n3. 分析分割后文档的样式...\n");
                for (int i = 0; i < splitFiles.size(); i++) {
                    File splitFile = splitFiles.get(i);
                    result.append("\n文件 ").append(i + 1).append(": ").append(splitFile.getName()).append("\n");
                    
                    try {
                        Map<String, Integer> splitStyles = analyzeDocumentStyles(splitFile.getAbsolutePath());
                        if (splitStyles.isEmpty()) {
                            result.append("  ❌ 未找到任何样式（可能样式丢失）\n");
                        } else {
                            result.append("  ✅ 样式保持情况:\n");
                            splitStyles.forEach((style, count) -> 
                                result.append("    - ").append(style).append(": ").append(count).append("个\n"));
                        }
                        
                        // 检查是否包含标题样式
                        boolean hasHeadingStyles = splitStyles.keySet().stream()
                                .anyMatch(style -> isHeadingStyle(style));
                        if (hasHeadingStyles) {
                            result.append("  ✅ 包含标题样式\n");
                        } else {
                            result.append("  ⚠️ 未包含标题样式\n");
                        }
                        
                    } catch (Exception e) {
                        result.append("  ❌ 分析失败: ").append(e.getMessage()).append("\n");
                    }
                }
                
                // 4. 总结
                result.append("\n4. 测试总结:\n");
                boolean allFilesHaveStyles = true;
                for (File splitFile : splitFiles) {
                    try {
                        Map<String, Integer> styles = analyzeDocumentStyles(splitFile.getAbsolutePath());
                        if (styles.isEmpty() || styles.size() == 1 && styles.containsKey("Normal")) {
                            allFilesHaveStyles = false;
                            break;
                        }
                    } catch (Exception e) {
                        allFilesHaveStyles = false;
                        break;
                    }
                }
                
                if (allFilesHaveStyles) {
                    result.append("✅ 所有分割文档都保持了样式信息\n");
                    result.append("🎉 样式保持功能正常工作！\n");
                } else {
                    result.append("❌ 部分分割文档丢失了样式信息\n");
                    result.append("⚠️ 需要进一步检查样式复制逻辑\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + resultFilePath);
    }
    
    private Map<String, Integer> analyzeDocumentStyles(String filePath) throws Exception {
        Map<String, Integer> styleCount = new HashMap<>();
        
        WordprocessingMLPackage wordMLPackage = Docx4J.load(new File(filePath));
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        List<Object> content = documentPart.getContent();
        
        for (Object obj : content) {
            if (obj instanceof P) {
                P para = (P) obj;
                String styleId = getStyleId(para);
                String text = getParagraphText(para);
                
                if (!text.trim().isEmpty()) {
                    styleCount.put(styleId, styleCount.getOrDefault(styleId, 0) + 1);
                }
            }
        }
        
        return styleCount;
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private boolean isHeadingStyle(String styleId) {
        if (styleId == null) return false;
        return styleId.matches("[1-6]") || // 数字样式
               styleId.toLowerCase().contains("heading") ||
               styleId.toLowerCase().contains("标题");
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
