package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * 字数计算调试测试
 */
@SpringBootTest
public class WordCountDebugTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void debugWordCountCalculation() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "word-count-debug-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 字数计算调试 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                
                // 方法1：使用getAllElementsFromObject
                result.append("=== 方法1：getAllElementsFromObject ===\n");
                List<Object> paragraphs1 = getAllElementsFromObject(documentPart);
                result.append("获取到的Object数量: ").append(paragraphs1.size()).append("\n");
                
                List<P> allParagraphs1 = new ArrayList<>();
                for (Object obj : paragraphs1) {
                    if (obj instanceof P) {
                        allParagraphs1.add((P) obj);
                    }
                }
                result.append("转换后的段落数量: ").append(allParagraphs1.size()).append("\n");
                
                int totalWordCount1 = calculateWordCount(allParagraphs1);
                result.append("计算的总字数: ").append(totalWordCount1).append("\n\n");
                
                // 方法2：直接从documentPart.getContent()获取
                result.append("=== 方法2：直接从documentPart.getContent() ===\n");
                List<Object> content = documentPart.getContent();
                result.append("documentPart.getContent()数量: ").append(content.size()).append("\n");
                
                List<P> allParagraphs2 = new ArrayList<>();
                for (Object obj : content) {
                    if (obj instanceof P) {
                        allParagraphs2.add((P) obj);
                    }
                }
                result.append("直接获取的段落数量: ").append(allParagraphs2.size()).append("\n");
                
                int totalWordCount2 = calculateWordCount(allParagraphs2);
                result.append("计算的总字数: ").append(totalWordCount2).append("\n\n");
                
                // 方法3：使用TextUtils
                result.append("=== 方法3：使用TextUtils ===\n");
                try {
                    StringWriter writer = new StringWriter();
                    org.docx4j.TextUtils.extractText(wordMLPackage, writer);
                    String allText = writer.toString();
                    int textUtilsWordCount = allText.replaceAll("\\s+", "").length();
                    result.append("TextUtils提取的文本长度: ").append(textUtilsWordCount).append("\n");
                    result.append("TextUtils提取的文本内容: ").append(allText.length() > 200 ? allText.substring(0, 200) + "..." : allText).append("\n\n");
                } catch (Exception e) {
                    result.append("TextUtils提取失败: ").append(e.getMessage()).append("\n\n");
                }
                
                // 详细分析段落内容
                result.append("=== 段落内容详细分析 ===\n");
                List<P> paragraphsToAnalyze = allParagraphs1.size() > 0 ? allParagraphs1 : allParagraphs2;
                
                for (int i = 0; i < Math.min(10, paragraphsToAnalyze.size()); i++) {
                    P para = paragraphsToAnalyze.get(i);
                    String text = getParagraphText(para);
                    String styleId = getStyleId(para);
                    int wordCount = text.replaceAll("\\s+", "").length();
                    
                    result.append(String.format("段落 %d:\n", i + 1));
                    result.append(String.format("  样式: %s\n", styleId));
                    result.append(String.format("  原始文本: '%s'\n", text));
                    result.append(String.format("  去空格后: '%s'\n", text.replaceAll("\\s+", "")));
                    result.append(String.format("  字数: %d\n", wordCount));
                    result.append("\n");
                }
                
                if (paragraphsToAnalyze.size() > 10) {
                    result.append(String.format("... 还有 %d 个段落\n\n", paragraphsToAnalyze.size() - 10));
                }
                
                // 测试实际的分割方法
                result.append("=== 测试实际分割方法 ===\n");
                List<File> splitFiles = docx4jSplitterService.splitByHeading(testFilePath, "word-count-debug-output", 50);
                result.append("分割结果文件数: ").append(splitFiles.size()).append("\n");
                for (File file : splitFiles) {
                    result.append("  - ").append(file.getName()).append("\n");
                }
                
                // 问题诊断
                result.append("\n=== 问题诊断 ===\n");
                if (totalWordCount1 == 0 && totalWordCount2 == 0) {
                    result.append("❌ 两种方法都计算出0字数，问题在于文本提取\n");
                } else if (totalWordCount1 == 0) {
                    result.append("❌ getAllElementsFromObject方法有问题\n");
                } else if (totalWordCount2 == 0) {
                    result.append("❌ 直接获取方法有问题\n");
                } else {
                    result.append("✅ 字数计算正常\n");
                }
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("调试结果已保存到: " + resultFilePath);
    }
    
    private List<Object> getAllElementsFromObject(MainDocumentPart documentPart) {
        List<Object> result = new ArrayList<>();
        
        try {
            // 使用docx4j的TraversalUtil来安全地遍历所有元素
            org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                @Override
                public List<Object> apply(Object o) {
                    if (o instanceof P) {
                        result.add(o);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // 如果TraversalUtil失败，使用简单方法
            List<Object> content = documentPart.getContent();
            for (Object item : content) {
                if (item instanceof P) {
                    result.add(item);
                }
            }
        }
        
        return result;
    }
    
    private int calculateWordCount(List<P> paragraphs) {
        int totalWords = 0;
        for (P para : paragraphs) {
            String text = getParagraphText(para);
            totalWords += text.replaceAll("\\s+", "").length();
        }
        return totalWords;
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private String getParagraphText(P para) {
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString();
    }
}
