package com.swyx.api.contentmanager.service.docx;

import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.P;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileWriter;
import java.io.StringWriter;
import java.util.List;

/**
 * 字数统计测试
 */
@SpringBootTest
public class WordCountTest {

    @Autowired
    private Docx4jSplitterService docx4jSplitterService;

    @Test
    public void testWordCount() throws Exception {
        String testFilePath = "src/test/resources/multi-level-headings.docx";
        String resultFilePath = "word-count-result.txt";
        
        StringBuilder result = new StringBuilder();
        result.append("=== 字数统计测试 ===\n");
        result.append("测试时间: ").append(new java.util.Date()).append("\n\n");
        
        File docFile = new File(testFilePath);
        if (!docFile.exists()) {
            result.append("❌ 测试文档不存在: ").append(testFilePath).append("\n");
        } else {
            try {
                WordprocessingMLPackage wordMLPackage = Docx4J.load(docFile);
                MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
                
                // 获取所有段落
                List<Object> paragraphs = getAllElementsFromObject(documentPart);
                
                result.append("文档分析:\n");
                result.append("段落总数: ").append(paragraphs.size()).append("\n\n");
                
                int totalWordCount = 0;
                int paragraphIndex = 0;
                
                for (Object obj : paragraphs) {
                    if (obj instanceof P) {
                        paragraphIndex++;
                        P para = (P) obj;
                        String styleId = getStyleId(para);
                        String text = getParagraphText(para);
                        int wordCount = text.replaceAll("\\s+", "").length();
                        totalWordCount += wordCount;
                        
                        result.append(String.format("段落 %d: 样式=%s, 字数=%d, 内容=%s%n",
                            paragraphIndex, styleId, wordCount, 
                            text.length() > 30 ? text.substring(0, 30) + "..." : text));
                    }
                }
                
                result.append("\n字数统计总结:\n");
                result.append("总字数: ").append(totalWordCount).append("\n");
                result.append("平均每段字数: ").append(paragraphIndex > 0 ? totalWordCount / paragraphIndex : 0).append("\n");
                
                // 测试不同的字数限制
                result.append("\n分割测试:\n");
                int[] limits = {50, 100, 200, 300, 500, 1000};
                
                for (int limit : limits) {
                    result.append(String.format("字数限制 %d: ", limit));
                    if (totalWordCount <= limit) {
                        result.append("不需要分割\n");
                    } else {
                        result.append("需要分割\n");
                    }
                }
                
                result.append("\n建议的测试字数限制:\n");
                result.append("- 不分割测试: ").append(totalWordCount + 100).append(" (总字数+100)\n");
                result.append("- 分割测试: ").append(Math.max(50, totalWordCount / 4)).append(" (总字数/4)\n");
                
            } catch (Exception e) {
                result.append("❌ 测试失败: ").append(e.getMessage()).append("\n");
                e.printStackTrace();
            }
        }
        
        result.append("\n").append("=".repeat(60)).append("\n");
        
        // 写入结果文件
        try (FileWriter writer = new FileWriter(resultFilePath)) {
            writer.write(result.toString());
        }
        
        // 输出到控制台
        System.out.println(result.toString());
        System.out.println("测试结果已保存到: " + resultFilePath);
    }
    
    private List<Object> getAllElementsFromObject(MainDocumentPart documentPart) {
        java.util.List<Object> result = new java.util.ArrayList<>();
        
        try {
            // 使用docx4j的TraversalUtil来安全地遍历所有元素
            org.docx4j.TraversalUtil.visit(documentPart, new org.docx4j.TraversalUtil.CallbackImpl() {
                @Override
                public java.util.List<Object> apply(Object o) {
                    if (o instanceof P) {
                        result.add(o);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // 如果TraversalUtil失败，使用简单方法
            java.util.List<Object> content = documentPart.getContent();
            for (Object item : content) {
                if (item instanceof P) {
                    result.add(item);
                }
            }
        }
        
        return result;
    }
    
    private String getStyleId(P para) {
        if (para.getPPr() != null && para.getPPr().getPStyle() != null) {
            return para.getPPr().getPStyle().getVal();
        }
        return "Normal";
    }
    
    private String getParagraphText(P para) {
        try {
            StringWriter writer = new StringWriter();
            org.docx4j.TextUtils.extractText(para, writer);
            String result = writer.toString().trim();
            if (!result.isEmpty()) {
                return result;
            }
        } catch (Exception e) {
            // 继续使用简单方法
        }
        
        StringBuilder text = new StringBuilder();
        if (para.getContent() != null) {
            for (Object content : para.getContent()) {
                if (content instanceof org.docx4j.wml.R) {
                    org.docx4j.wml.R run = (org.docx4j.wml.R) content;
                    if (run.getContent() != null) {
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof org.docx4j.wml.Text) {
                                org.docx4j.wml.Text textElement = (org.docx4j.wml.Text) runContent;
                                if (textElement.getValue() != null) {
                                    text.append(textElement.getValue());
                                }
                            }
                        }
                    }
                }
            }
        }
        return text.toString().trim();
    }
}
