<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
<context id="Mysql" targetRuntime="MyBatis3">
    <!-- 数据库连接配置 -->
    <jdbcConnection
            driverClass="com.mysql.cj.jdbc.Driver"
            connectionURL="*******************************************************************************************"
            userId="ringlong"
            password="mH0LFdgoJpVoT2iu"/>

    <!-- Entity 生成位置 -->
    <javaModelGenerator
            targetPackage="com.swyx.api.contentmanager.dao"
            targetProject="src/main/java"/>

    <!-- Mapper XML 生成位置 -->
    <sqlMapGenerator
            targetPackage="mybatis"
            targetProject="src/main/resources"/>

    <!-- Mapper 接口生成位置 -->
    <javaClientGenerator
            targetPackage="com.swyx.api.contentmanager.dao.mapper"
            targetProject="src/main/java"
            type="XMLMAPPER"/>

    <!-- 指定需生成的表 -->
<!--    <table tableName="book" domainObjectName="BookEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

    <table tableName="task" domainObjectName="TaskEntity" enableSelectByExample="true"
           enableDeleteByExample="false"
           enableCountByExample="true"
           enableUpdateByExample="true">
        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
    </table>

<!--    <table tableName="page_content" domainObjectName="PageEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="project" domainObjectName="ProjectEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="user" domainObjectName="UserEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="ai_prompt" domainObjectName="AIPromptEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="ai_model" domainObjectName="AIModelEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="ai_issue" domainObjectName="AIIssueEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--        <table tableName="role" domainObjectName="RoleEntity" enableSelectByExample="true"-->
<!--               enableDeleteByExample="false"-->
<!--               enableCountByExample="true"-->
<!--               enableUpdateByExample="true">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--    <table tableName="permission" domainObjectName="PermissionEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--    </table>-->

<!--    <table tableName="role_permission" domainObjectName="RolePermissionEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--    </table>-->

<!--    <table tableName="user_role" domainObjectName="UserRoleEntity" enableSelectByExample="true"-->
<!--           enableDeleteByExample="false"-->
<!--           enableCountByExample="true"-->
<!--           enableUpdateByExample="true">-->
<!--    </table>-->
</context>
</generatorConfiguration>