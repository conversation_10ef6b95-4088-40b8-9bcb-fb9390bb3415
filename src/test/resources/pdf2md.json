{"image_process": [], "code": 200, "result": {"markdown": "<!-- **第五节** 函数的微分 -->\n\n对照，列表于下：\n\n\n| 导数公式 | 微分公式 |\n| --- | --- |\n| $(x^{\\mu })^{\\prime }=\\mu x^{\\mu -1}(\\mu$是任意常数） | $d(x^{\\mu })=\\mu x^{\\mu -1}dx(\\mu$是任意常数） |\n| $(\\sin x)^{\\prime }=\\cos x$ | $d(\\sin x)=\\cos xdx$ |\n| $(\\cos x)^{\\prime }=-\\sin x$ | $d(\\cos x)=-\\sin xdx$ |\n| $(\\tan x)^{\\prime }=\\sec ^{2}x$ | $d(\\tan x)=\\sec ^{2}xdx$ |\n| $(\\cot x)^{\\prime }=-\\csc ^{2}x$ | $d(\\cot x)=-\\csc ^{2}xdx$ |\n| $(\\sec x)^{\\prime }=\\sec x\\tan x$ | $d(\\sec x)=\\sec x\\tan xdx$ |\n| $(\\csc x)^{\\prime }=-\\csc x\\cot x$ | $d(\\csc x)=-\\csc x\\cot xdx$ |\n| $(a^{x})^{\\prime }=a^{x}\\ln a(a>0$且$a\\neq 1)$ | $d(a^{x})=a^{x}\\ln adx(a>0$且$a\\neq 1)$ |\n| $(e^{x})^{\\prime }=e^{x}$ | $d(e^{x})=e^{x}dx$ |\n| $(\\log _{a}x)^{\\prime }=\\frac {1}{x\\ln a}(a>0$且$a\\neq 1)$ | $d(\\log _{a}x)=\\frac {1}{x\\ln a}dx(a>0$且$a\\neq 1)$ |\n| $(\\ln x)^{\\prime }=\\frac {1}{x}$ | $d(\\ln x)=\\frac {1}{x}dx$ |\n| $(\\arcsin x)^{\\prime }=\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arcsin x)=\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arccos x)^{\\prime }=-\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arccos x)=-\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arctan x)^{\\prime }=\\frac {1}{1+x^{2}}$ | $d(\\arctan x)=\\frac {1}{1+x^{2}}dx$ |\n| $(\\operatorname{arccot}x)^{\\prime }=-\\frac {1}{1+x^{2}}$ | $d(\\operatorname{arccot}x)=-\\frac {1}{1+x^{2}}dx$ |\n\n\n# 2．函数和、差、积、商的微分法则\n\n由函数和、差、积、商的求导法则，可推得相应的微分法则.为了便于对照，列成下表（表中 $u=u(x),v=v(x)$ 都可导）.\n\n\n| 函数和、差、积、商的求导法则 | 函数和、差、积、商的微分法则 |\n| --- | --- |\n| $(u\\pm v)^{\\prime }=u^{\\prime }\\pm v^{\\prime }$ | $d(u\\pm v)=du\\pm dv$ |\n| $(Cu)^{\\prime }=Cu^{\\prime }$ $(C$是常数） | $d(Cu)=Cdu$（$C$是常数） |\n| $(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }$ | $d(uv)=vdu+udv$ |\n| $(\\frac {u}{v})^{\\prime }=\\frac {u^{\\prime }v-uv^{\\prime }}{v^{2}}$ $(v\\neq 0)$ | $d(\\frac {u}{v})=\\frac {vdu-udv}{v^{2}}$ $(v\\neq 0)$ |\n\n\n<!-- 111 -->\n\n<!-- 第二章 导数与微分 -->\n\n现在我们以乘积的微分法则为例加以证明.\n\n根据函数微分的表达式，有\n\n$$d(uv)=(uv)^{\\prime }dx.$$\n\n再根据乘积的求导法则，有\n\n$$(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }.$$\n\n于是\n\n$$d(uv)=(u^{\\prime }v+uv^{\\prime })dx=u^{\\prime }vdx+uv^{\\prime }dx.$$\n\n由于\n\n$$u^{\\prime }dx=du,v^{\\prime }dx=dv$$\n\n所以\n\n$$d(uv)=vdu+udv.$$\n\n其他法则都可以用类似方法证明.\n\n# 3．复合函数的微分法则\n\n与复合函数的求导法则相应的复合函数的微分法则可推导如下：\n\n设 $y=f(u)$ 及 $u=g(x)$ 都可导，则复合函数 $y=f[g(x)]$ 的微分为\n\n$$dy=y_{x}^{\\prime }dx\\textcircled {1}=f^{\\prime }(u)g^{\\prime }(x)dx.$$\n\n由于 $g^{\\prime }(x)dx=du$ ，所以，复合函数 $y=f[g(x)$ ］的微分公式也可以写成\n\n$dy=f^{\\prime }(u)du$  或 $dy=y_{u}^{\\prime }du.$ \n\n由此可见，无论 $u$ 是自变量还是中间变量，微分形式 $dy=f^{\\prime }(u)$ u保持不变.这一性质称为微分形式不变性.这性质表示，当变换自变量时，微分形式 $dy=f^{\\prime }(u)du$ 并不改变.\n\n**例3** 设 $y=\\sin (2x+1)$ ，求 $dy.$ \n\n解把 $2x+1$ 看成中间变量 $u$ ，则\n\n$$dy=d(\\sin u)=\\cos udu=\\cos (2x+1)d(2x+1)$$\n\n$$=\\cos (2x+1)\\cdot 2dx=2\\cos (2x+1)dx.$$\n\n在求复合函数的导数时，可以不写出中间变量.在求复合函数的微分时，类似地也可以不写出中间变量.下面我们用这种方法来求函数的微分.\n\n例4 设 $y=\\ln (1+e^{x^{2}})$ ，求dy.\n\n解 $dy=d(\\ln (1+e^{x2}))=\\frac {1}{1+e^{z^{2}}}d(1+e^{z^{2}})=\\frac {1}{1+e^{z^{2}}}·e^{z^{2}}d(x^{2})=\\frac {e^{z^{2}}}{1+e^{z^{2}}}·2xdx=\\frac {2xe^{z^{2}}}{1+e^{z^{2}}}dx.$ \n\n例5 设 $y=e^{1-3x}\\cos x$ ，求dy.\n\n$\\textcircled {1}$  其中 $y_{x}^{\\prime }$ 2表示 $y$ 对 $x$ 的导数，它也是导数的一种表示方法．\n\n<!-- 112 -->\n\n<!-- **第五节 函数的微分** -->\n\n**解** 应用积的微分法则，得\n\n$$dy=d(e^{1-3x}\\cos x)=\\cos xd(e^{1-3x})+e^{1-3x}d(\\cos x)\\\\ =(\\cos x)e^{1-3x}(-3dx)+e^{1-3x}(-\\sin xdx)=-e^{1-3x}(3\\cos x+\\sin x)dx.$$\n\n**例6** 在下列等式左端的括号中填入适当的函数，使等式成立．\n\n$($ $)$ $($ $)$ (1) $d$  $=xdx;$ (2) $d$  $=\\cos\\omegatdt$  $(\\neq 0)$ \n\n解（1）我们知道，\n\n$$d(x^{2})=2xdx.$$\n\n可见\n\n$$xdx=\\frac {1}{2}d(x^{2})=d(\\frac {x^{2}}{2})$$\n\n即\n\n$$d(\\frac {x^{2}}{2})=xdx.$$\n\n一般地，有\n\n$d(\\frac {x^{2}}{2}+C)=xdx$  （ $C$ 为任意常数）.\n\n（2）因为\n\n$$d(\\sin \\omega t)=\\omega \\cos \\omega tdt,$$\n\n可见\n\n$$\\cos \\omega tdt=\\frac {1}{\\omega }d(\\sin \\omega t)=d(\\frac {1}{\\omega }\\sin \\omega t),$$\n\n即\n\n$$d(\\frac {1}{\\omega }\\sin \\omega t)=\\cos \\omega tdt.$$\n\n一般地，有\n\n$d(\\frac {1}{\\omega }\\sin \\omega t+C)=\\cos \\omega tdt$  （ $C$ 为任意常数， $\\neq 0).$ \n\n# 四、微分在近似计算中的应用\n\n## 1．函数的近似计算\n\n在工程问题中，经常会遇到一些复杂的计算公式.如果直接用这些公式进行计算，那是很费力的.利用微分往往可以把一些复杂的计算公式用简单的近似公式来代替.\n\n前面说过，如果 $y=f(x)$ 在点 $x_{0}$ 处的导数 $f^{\\prime }(x_{0})\\neq 0$ ，且1 $\\Delta x\\vert$ 很小时，我们有\n\n$$\\Delta y\\approx dy=f^{\\prime }(x_{0})\\Delta x.$$\n\n<!-- 113 -->\n\n<!-- 第二章 导数与微分 -->\n\n这个式子也可以写为\n\n$$\\Delta y=f(x_{0}+\\Delta x)-f(x_{0})\\approx f^{\\prime }(x_{0})\\Delta x\\tag{5-4}$$\n\n或\n\n$$f(x_{0}+\\Delta x)\\approx f(x_{0})+f^{\\prime }(x_{0})\\Delta x.\\tag{5-5}$$\n\n在（5-5）式中令 $x=x_{0}+\\Delta x$ ，即 $\\Delta x=x-x_{0}$ ，那么（5-5）式可改写为\n\n$$f(x)\\approx f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})\\tag{5-6}$$\n\n如果 $f(x_{0})$ 与 $f^{\\prime }(x_{0})$ 都容易计算，那么可利用（5-4）式来近似计算 $\\Delta y$ ，利用（5-5）式来近似计算 $f(x_{0}+\\Delta x)$ ，或利用（5-6）式来近似计算 $f(x)$ .这种近似计算的实质就是用 $x$ 的线性函数 $f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})$ 来近似表达函数 $f(x)$ .从导数的几何意义可知，这也就是用曲线 $y=f(x)$ 在点 $(x_{0},f(x_{0}))$ 处的切线来近似代替该曲线（就切点邻近部分来说）.\n\n**例7** 有一批半径为 $1cm$ 的球，为了提高球面的光洁度，要镀上一层铜，厚度定为 $0.01cm$  估计一下镀每只球需用多少克铜（铜的密度是 $8.9g/cm^{3}$ )？\n\n**解** 先求出镀层的体积，再乘密度就可得到镀每只球需用铜的质量.\n\n因为镀层的体积等于镀铜前、后两个球体体积之差，所以它就是球体体积 $V=$ $\\frac {4}{3}\\pi R^{3}$ 当 $R$ 自 $R_{0}$ 取得增量 $\\Delta R$ 时的增量 $\\Delta V.$ .我们求 $A$ 对 $R$ 的导数\n\n$$V^{\\prime }\\vert _{R=R_{0}}=$$\n\n由（5-4）式得\n\n$$\\Delta V\\approx 4\\pi R_{0}^{2}\\Delta R.$$\n\n将 $R_{0}=1,\\Delta R=0.01$ 代入上式，得\n\n$$\\Delta V\\approx 4\\times 3.14\\times 1^{2}\\times 0.01\\approx 0.13(cm^{3})$$\n\n于是镀每只球需用的铜约为\n\n$$0.13\\times 8.9\\approx 1.16(g)$$\n\n例8 利用微分计算sin $30^{\\circ }30^{\\prime }$ 的近似值．\n\n解把 $30^{\\circ }30^{\\prime }$ 化为弧度，得\n\n$$30^{\\circ }30^{\\prime }=\\frac {\\pi }{6}+\\frac {\\pi }{360}.$$\n\n由于所求的是正弦函数的值，故设 $f(x)=\\sin x.$ 此时f＇ $(x)=\\cos$ x.如果取 $x_{0}=\\frac {\\pi }{6}$ ，那么 $f(\\frac {\\pi }{6})=\\sin \\frac {\\pi }{6}=\\frac {1}{2}$ 与 $f^{\\prime }(\\frac {\\pi }{6})=\\cos \\frac {\\pi }{6}=\\frac {\\sqrt {3}}{2}$ 都容易计算，并且 $\\Delta x=\\frac {\\pi }{360}$ 比较小.应用（5-5）式便得\n\n<!-- 114 -->\n\n<!-- 第五节 函数的微分 -->\n\n$$\\sin 30^{\\circ }30^{\\prime }=\\sin (\\frac {\\pi }{6}+\\frac {\\pi }{360})\\approx \\sin \\frac {\\pi }{6}+\\cos \\frac {\\pi }{6}\\times \\frac {\\pi }{360}\\\\ =\\frac {1}{2}+\\frac {\\sqrt {3}}{2}\\times \\frac {\\pi }{360}\\approx 0.5000+0.0076=0.5076.$$\n\n下面我们来推导一些常用的近似公式．为此，在（5-6）式中取 $x_{0}=0$ ，于是得\n\n$$f(x)\\approx f(0)+f^{\\prime }(0)x.\\tag{5-7}$$\n\n应用（5-7）式可以推得以下几个在工程上常用的近似公式（下面都假定 $\\vert x\\vert$ 是较小的数值）：\n\n(i) $(1+x)^{\\alpha }\\approx 1+\\alpha x$ $(\\alpha \\in R),$ \n\n(ii)sin $x\\approx x$  （ $x$ 以弧度为单位），\n\n(iii)tan $x\\approx x$  （ $x$ 以弧度为单位），\n\n(iv) $e^{x}\\approx 1+x,$ \n\n(v) $\\ln (1+x)\\approx x.$ \n\n证（i）在第一章第九节例7中我们已经知道 $(1+x)^{\\alpha }-1\\sim \\alpha x(x\\rightarrow 0)$ ，从而得出这个近似公式.在这里，我们利用微分证明.取 $f(x)=(1+x)^{\\alpha }$ ，那么 $f(0)=1,f^{\\prime }(0)=$ $\\alpha (1+x)^{\\alpha -1}\\vert _{x=0}=\\alpha ,代入(5-7)$ 式便得\n\n$$(1+x)^{\\alpha }\\approx 1+\\alpha x.$$\n\n（ii）取 $f(x)=\\sin x$ ，那么 $f(0)=0,f^{\\prime }(0)=\\cos x\\vert _{x=0}=1,代入(5-7):$ 式便得\n\n$$\\sin x\\approx x.$$\n\n其他几个近似公式可用类似方法证明，这里从略了.\n\n例9 计算 $\\sqrt {1.05}$ 的近似值．\n\n解 $\\sqrt {1.05}=\\sqrt {1+0.05}$ \n\n这里 $x=0.05$ ，其值较小，利用近似公式(i) $(\\alpha =\\frac {1}{2}$ 的情形），便得\n\n$$\\sqrt {1.05}\\approx 1+\\frac {1}{2}\\times 0.05=1.025.$$\n\n如果直接开方，可得\n\n$$\\sqrt {1.05}=1.02470.$$\n\n将两个结果比较一下，可以看出，用1.025作为 $\\sqrt {1.05}$ 的近似值，其误差不超过0.001，这样的近似值在一般应用上已够精确了.如果开方次数较高，就更能体现出用微分进行近似计算的优越性.\n\n<!-- 115 -->\n\n<!-- 第二章 导数与微分 -->\n\n## ＂2．误差估计\n\n在生产实践中，经常要测量各种数据.但是有的数据不易直接测量，这时我们就通过测量其他有关数据后，根据某种公式算出所要的数据.例如，要计算圆钢的截面积A，可先用卡尺测量圆钢截面的直径 $D$ ，然后根据公式 $A=\\frac {\\pi }{4}D^{2}$ 算出A.\n\n由于测量仪器的精度、测量的条件和测量的方法等各种因素的影响，测得的数据往往带有误差，而根据带有误差的数据计算所得的结果也会有误差，我们把它叫做间接测量误差\n\n下面就讨论怎样利用微分来估计间接测量误差.\n\n先说明绝对误差、相对误差的概念.\n\n如果某个量的精确值为A，它的近似值为 $a$ ，那么 $\\vert A-a\\vert$ 叫做 $a$ 的绝对误差，而绝对误差与 $\\vert a\\vert$ 的比值 $\\frac {\\vert A-a\\vert }{\\vert a\\vert }$ 叫做 $a$ 的相对误差.\n\n在实际工作中，某个量的精确值往往是无法知道的，于是绝对误差和相对误差也就无法求得.但是根据测量仪器的精度等因素，有时能够确定误差在某一个范围内.如果某个量的精确值是A，测得它的近似值是 $a$ ，又知道它的误差不超过 $δ_{A}$ ，即\n\n$$\\vert A-a\\vert \\leq \\delta _{A},$$\n\n那么 $δ_{A}$ 叫做测量 $A$ 的绝对误差限，而 $\\frac {\\delta _{A}}{\\vert a\\vert }$ 叫做测量 $A$ 的相对误差限.\n\n**例10** 设测得圆钢截面的直径 $D=60.03$ mm，测量 $D$  的绝对误差限 $δ_{D}=$ $0.05mm$ ．利用公式\n\n$$A=\\frac {\\pi }{4}D^{2}$$\n\n计算圆钢的截面积时，试估计面积的误差.\n\n解 如果我们把测量 $D$ 时所产生的误差当作自变量 $D$ 的增量 $\\Delta D$ ，那么，利用公式 $A=\\frac {\\pi }{4}D^{2}$ 来计算 $A$ 时所产生的误差就是函数A的对应增量 $\\Delta A.$ .当 $\\vert \\Delta D\\vert$ 很小时，可以利用微分dA近似地代替增量 $\\Delta A$ ，即\n\n$$\\Delta A\\approx dA=A^{\\prime }\\cdot \\Delta D=\\frac {\\pi }{2}D\\cdot \\Delta D.$$\n\n由于 $D$ 的绝对误差限为 $δ_{D}=0.05\\mathrm {\\sim mm}$ ，所以\n\n$\\vert \\Delta D\\vert \\leq \\delta _{D}=0.05$ ,\n\n而\n\n<!-- 116 -->\n\n", "success_count": 6, "catalog": {"toc": [{"pos": [194, 1090, 530, 1090, 530, 1113, 194, 1113], "paragraph_id": 4, "page_id": 1, "hierarchy": 1, "pos_list": [[194, 1090, 530, 1090, 530, 1113, 194, 1113]], "title": "2．函数和、差、积、商的微分法则", "sub_type": "text_title"}, {"pos": [261, 680, 487, 680, 487, 703, 261, 703], "paragraph_id": 14, "page_id": 2, "hierarchy": 1, "pos_list": [[261, 680, 487, 680, 487, 703, 261, 703]], "title": "3．复合函数的微分法则", "sub_type": "text_title"}, {"pos": [176, 1200, 500, 1200, 500, 1224, 176, 1224], "paragraph_id": 24, "page_id": 3, "hierarchy": 1, "pos_list": [[176, 1200, 500, 1200, 500, 1224, 176, 1224]], "title": "四、微分在近似计算中的应用", "sub_type": "text_title"}, {"pos": [174, 1278, 357, 1278, 357, 1297, 174, 1297], "paragraph_id": 25, "page_id": 3, "hierarchy": 2, "pos_list": [[174, 1278, 357, 1278, 357, 1297, 174, 1297]], "title": "1．函数的近似计算", "sub_type": "text_title"}, {"pos": [261, 239, 391, 239, 391, 257, 261, 257], "paragraph_id": 2, "page_id": 6, "hierarchy": 2, "pos_list": [[261, 239, 391, 239, 391, 257, 261, 257]], "title": "＂2．误差估计", "sub_type": "text_title"}]}, "pages": [{"angle": 0, "page_id": 1, "content": [{"pos": [786, 110, 942, 110, 942, 135, 786, 135], "id": 0, "score": 0.98699998855591, "type": "line", "text": "第五节 函数的微分"}, {"pos": [143, 208, 299, 205, 300, 235, 143, 237], "id": 1, "score": 0.9990000128746, "type": "line", "text": "对照，列表于下："}, {"pos": [298, 250, 408, 250, 408, 276, 298, 276], "id": 2, "score": 0.9990000128746, "type": "line", "text": "导数公式"}, {"pos": [694, 247, 804, 247, 804, 273, 694, 273], "id": 3, "score": 0.9990000128746, "type": "line", "text": "微分公式"}, {"pos": [225, 291, 362, 291, 362, 319, 225, 319], "id": 4, "type": "line", "score": 1, "text": "$( x ^ { \\mu } ) ^ { \\prime } = \\mu x ^ { \\mu - 1 } ( \\mu$", "sub_type": "formula"}, {"pos": [360, 291, 469, 291, 469, 317, 360, 317], "id": 5, "score": 0.9990000128746, "type": "line", "text": "是任意常数）"}, {"pos": [612, 288, 778, 288, 778, 316, 612, 316], "id": 6, "type": "line", "score": 1, "text": "$d ( x ^ { \\mu } ) = \\mu x ^ { \\mu - 1 } d x ( \\mu$", "sub_type": "formula"}, {"pos": [775, 288, 883, 288, 883, 314, 775, 314], "id": 7, "score": 0.9990000128746, "type": "line", "text": "是任意常数）"}, {"pos": [225, 337, 349, 337, 349, 362, 225, 362], "id": 8, "type": "line", "score": 1, "text": "$( \\sin x ) ^ { \\prime } = \\cos x$", "sub_type": "formula"}, {"pos": [613, 332, 758, 332, 758, 359, 613, 359], "id": 9, "type": "line", "score": 1, "text": "$d ( \\sin x ) = \\cos x d x$", "sub_type": "formula"}, {"pos": [227, 378, 360, 376, 360, 402, 227, 404], "id": 10, "type": "line", "score": 1, "text": "$( \\cos x ) ^ { \\prime } = - \\sin x$", "sub_type": "formula"}, {"pos": [613, 375, 770, 373, 770, 398, 614, 400], "id": 11, "type": "line", "score": 1, "text": "$d ( \\cos x ) = - \\sin x d x$", "sub_type": "formula"}, {"pos": [227, 418, 352, 418, 352, 446, 227, 446], "id": 12, "type": "line", "score": 1, "text": "$( \\tan x ) ^ { \\prime } = \\sec ^ { 2 } x$", "sub_type": "formula"}, {"pos": [615, 417, 761, 413, 762, 440, 615, 443], "id": 13, "type": "line", "score": 1, "text": "$d ( \\tan x ) = \\sec ^ { 2 } x d x$", "sub_type": "formula"}, {"pos": [227, 464, 362, 462, 362, 489, 227, 491], "id": 14, "type": "line", "score": 1, "text": "$( \\cot x ) ^ { \\prime } = - \\csc ^ { 2 } x$", "sub_type": "formula"}, {"pos": [615, 461, 771, 458, 772, 485, 615, 489], "id": 15, "type": "line", "score": 1, "text": "$d ( \\cot x ) = - \\csc ^ { 2 } x d x$", "sub_type": "formula"}, {"pos": [229, 508, 390, 508, 390, 534, 229, 534], "id": 16, "type": "line", "score": 1, "text": "$( \\sec x ) ^ { \\prime } = \\sec x \\tan x$", "sub_type": "formula"}, {"pos": [615, 505, 799, 502, 799, 529, 615, 532], "id": 17, "type": "line", "score": 1, "text": "$d ( \\sec x ) = \\sec x \\tan x d x$", "sub_type": "formula"}, {"pos": [229, 549, 400, 549, 400, 579, 229, 579], "id": 18, "type": "line", "score": 1, "text": "$( \\csc x ) ^ { \\prime } = - \\csc x \\cot x$", "sub_type": "formula"}, {"pos": [616, 551, 809, 548, 809, 574, 617, 577], "id": 19, "type": "line", "score": 1, "text": "$d ( \\csc x ) = - \\csc x \\cot x d x$", "sub_type": "formula"}, {"pos": [229, 595, 395, 595, 395, 622, 229, 622], "id": 20, "type": "line", "score": 1, "text": "$( a ^ { x } ) ^ { \\prime } = a ^ { x } \\ln a ( a > 0$", "sub_type": "formula"}, {"pos": [391, 595, 416, 595, 416, 620, 391, 620], "id": 21, "score": 0.99800002574921, "type": "line", "text": "且"}, {"pos": [414, 595, 467, 595, 467, 620, 414, 620], "id": 22, "type": "line", "score": 1, "text": "$a \\neq 1 )$", "sub_type": "formula"}, {"pos": [617, 592, 804, 592, 804, 618, 617, 618], "id": 23, "type": "line", "score": 1, "text": "$d ( a ^ { x } ) = a ^ { x } \\ln a d x ( a > 0$", "sub_type": "formula"}, {"pos": [803, 594, 824, 594, 824, 613, 803, 613], "id": 24, "score": 0.9990000128746, "type": "line", "text": "且"}, {"pos": [826, 590, 877, 590, 877, 615, 826, 615], "id": 25, "type": "line", "score": 1, "text": "$a \\neq 1 )$", "sub_type": "formula"}, {"pos": [230, 646, 308, 646, 308, 671, 230, 671], "id": 26, "type": "line", "score": 1, "text": "$( e ^ { x } ) ^ { \\prime } = e ^ { x }$", "sub_type": "formula"}, {"pos": [617, 641, 719, 641, 719, 668, 617, 668], "id": 27, "type": "line", "score": 1, "text": "$d ( e ^ { x } ) = e ^ { x } d x$", "sub_type": "formula"}, {"pos": [230, 694, 408, 692, 408, 739, 231, 741], "id": 28, "type": "line", "score": 1, "text": "$( \\log _ { a } x ) ^ { \\prime } = \\frac { 1 } { x \\ln a } ( a > 0$", "sub_type": "formula"}, {"pos": [423, 697, 477, 697, 477, 732, 423, 732], "id": 29, "type": "line", "score": 1, "text": "$a \\neq 1 )$", "sub_type": "formula"}, {"pos": [403, 702, 428, 702, 428, 729, 403, 729], "id": 30, "score": 0.9990000128746, "type": "line", "text": "且"}, {"pos": [618, 691, 819, 691, 819, 737, 618, 737], "id": 31, "type": "line", "score": 1, "text": "$d ( \\log _ { a } x ) = \\frac { 1 } { x \\ln a } d x ( a > 0$", "sub_type": "formula"}, {"pos": [816, 699, 840, 699, 840, 724, 816, 724], "id": 32, "score": 0.9990000128746, "type": "line", "text": "且"}, {"pos": [840, 696, 891, 696, 891, 725, 840, 725], "id": 33, "type": "line", "score": 1, "text": "$a \\neq 1 )$", "sub_type": "formula"}, {"pos": [232, 755, 331, 755, 331, 801, 232, 801], "id": 34, "type": "line", "score": 1, "text": "$( \\ln x ) ^ { \\prime } = \\frac { 1 } { x }$", "sub_type": "formula"}, {"pos": [620, 750, 740, 750, 740, 796, 620, 796], "id": 35, "type": "line", "score": 1, "text": "$d ( \\ln x ) = \\frac { 1 } { x } d x$", "sub_type": "formula"}, {"pos": [233, 817, 403, 814, 404, 868, 234, 871], "id": 36, "type": "line", "score": 1, "text": "$( \\arcsin x ) ^ { \\prime } = \\frac { 1 } { \\sqrt { 1 - x ^ { 2 } } }$", "sub_type": "formula"}, {"pos": [620, 814, 812, 810, 813, 863, 621, 868], "id": 37, "type": "line", "score": 1, "text": "$d ( \\arcsin x ) = \\frac { 1 } { \\sqrt { 1 - x ^ { 2 } } } d x$", "sub_type": "formula"}, {"pos": [233, 879, 419, 877, 420, 931, 234, 933], "id": 38, "type": "line", "score": 1, "text": "$( \\arccos x ) ^ { \\prime } = - \\frac { 1 } { \\sqrt { 1 - x ^ { 2 } } }$", "sub_type": "formula"}, {"pos": [621, 878, 829, 873, 830, 925, 622, 930], "id": 39, "type": "line", "score": 1, "text": "$d ( \\arccos x ) = - \\frac { 1 } { \\sqrt { 1 - x ^ { 2 } } } d x$", "sub_type": "formula"}, {"pos": [233, 946, 377, 942, 379, 991, 234, 996], "id": 40, "type": "line", "score": 1, "text": "$( \\arctan x ) ^ { \\prime } = \\frac { 1 } { 1 + x ^ { 2 } }$", "sub_type": "formula"}, {"pos": [621, 942, 787, 938, 789, 986, 622, 991], "id": 41, "type": "line", "score": 1, "text": "$d ( \\arctan x ) = \\frac { 1 } { 1 + x ^ { 2 } } d x$", "sub_type": "formula"}, {"pos": [233, 1010, 391, 1006, 392, 1054, 234, 1058], "id": 42, "type": "line", "score": 1, "text": "$( \\operatorname{arccot} x ) ^ { \\prime } = - \\frac { 1 } { 1 + x ^ { 2 } }$", "sub_type": "formula"}, {"pos": [623, 1007, 802, 1004, 803, 1052, 624, 1055], "id": 43, "type": "line", "score": 1, "text": "$d ( \\operatorname{arccot} x ) = - \\frac { 1 } { 1 + x ^ { 2 } } d x$", "sub_type": "formula"}, {"pos": [197, 1091, 533, 1087, 533, 1117, 197, 1121], "id": 44, "score": 0.9990000128746, "type": "line", "text": "2．函数和、差、积、商的微分法则"}, {"pos": [200, 1145, 955, 1137, 956, 1167, 201, 1176], "id": 45, "score": 0.9990000128746, "type": "line", "text": "由函数和、差、积、商的求导法则，可推得相应的微分法则.为了便于对照，列成下"}, {"pos": [158, 1182, 240, 1182, 240, 1212, 158, 1212], "id": 46, "score": 0.9990000128746, "type": "line", "text": "表（表中"}, {"pos": [240, 1181, 400, 1181, 400, 1210, 240, 1210], "id": 47, "type": "line", "score": 1, "text": "$u = u ( x ) , v = v ( x )$", "sub_type": "formula"}, {"pos": [398, 1181, 485, 1181, 485, 1209, 398, 1209], "id": 48, "score": 0.99599999189377, "type": "line", "text": "都可导）."}, {"pos": [246, 1231, 488, 1228, 489, 1255, 247, 1258], "id": 49, "score": 0.99800002574921, "type": "line", "text": "函数和、差、积、商的求导法则"}, {"pos": [644, 1227, 883, 1223, 883, 1251, 645, 1254], "id": 50, "score": 0.9990000128746, "type": "line", "text": "函数和、差、积、商的微分法则"}, {"pos": [256, 1269, 373, 1267, 374, 1294, 257, 1296], "id": 51, "type": "line", "score": 1, "text": "$( u \\pm v ) ^ { \\prime } = u ^ { \\prime } \\pm v ^ { \\prime }$", "sub_type": "formula"}, {"pos": [653, 1265, 783, 1265, 783, 1291, 653, 1291], "id": 52, "type": "line", "score": 1, "text": "$d ( u \\pm v ) = d u \\pm d v$", "sub_type": "formula"}, {"pos": [261, 1308, 359, 1308, 359, 1331, 261, 1331], "id": 53, "type": "line", "score": 1, "text": "$( C u ) ^ { \\prime } = C u ^ { \\prime }$", "sub_type": "formula"}, {"pos": [353, 1308, 386, 1308, 386, 1331, 353, 1331], "id": 54, "type": "line", "score": 1, "text": "$( C$", "sub_type": "formula"}, {"pos": [382, 1306, 452, 1306, 452, 1332, 382, 1332], "id": 55, "score": 0.99800002574921, "type": "line", "text": "是常数）"}, {"pos": [655, 1302, 761, 1302, 761, 1329, 655, 1329], "id": 56, "type": "line", "score": 1, "text": "$d ( C u ) = C d u$", "sub_type": "formula"}, {"pos": [766, 1305, 775, 1305, 775, 1324, 766, 1324], "id": 57, "score": 0.9990000128746, "type": "line", "text": "（"}, {"pos": [776, 1304, 791, 1304, 791, 1324, 776, 1324], "id": 58, "type": "line", "score": 1, "text": "$C$", "sub_type": "formula"}, {"pos": [793, 1305, 857, 1305, 857, 1323, 793, 1323], "id": 59, "score": 0.9990000128746, "type": "line", "text": "是常数）"}, {"pos": [258, 1345, 380, 1343, 380, 1370, 259, 1372], "id": 60, "type": "line", "score": 1, "text": "$( u v ) ^ { \\prime } = u ^ { \\prime } v + u v ^ { \\prime }$", "sub_type": "formula"}, {"pos": [655, 1340, 789, 1340, 789, 1367, 655, 1367], "id": 61, "type": "line", "score": 1, "text": "$d ( u v ) = v d u + u d v$", "sub_type": "formula"}, {"pos": [261, 1385, 401, 1385, 401, 1434, 261, 1434], "id": 62, "type": "line", "score": 1, "text": "$( \\frac { u } { v } ) ^ { \\prime } = \\frac { u ^ { \\prime } v - u v ^ { \\prime } } { v ^ { 2 } }$", "sub_type": "formula"}, {"pos": [396, 1385, 472, 1385, 472, 1429, 396, 1429], "id": 63, "type": "line", "score": 1, "text": "$( v \\neq 0 )$", "sub_type": "formula"}, {"pos": [655, 1380, 803, 1380, 803, 1427, 655, 1427], "id": 64, "type": "line", "score": 1, "text": "$d ( \\frac { u } { v } ) = \\frac { v d u - u d v } { v ^ { 2 } }$", "sub_type": "formula"}, {"pos": [798, 1380, 870, 1380, 870, 1424, 798, 1424], "id": 65, "type": "line", "score": 0.99800002574921, "text": "$( v \\neq 0 )$", "sub_type": "formula"}, {"pos": [977, 1518, 1013, 1518, 1013, 1543, 977, 1543], "id": 66, "score": 0.9990000128746, "type": "line", "text": "111"}], "status": "Success", "height": 1684, "structured": [{"pos": [786, 114, 942, 114, 942, 131, 786, 131], "blocks": [{"pos": [786, 114, 942, 114, 942, 131, 786, 131], "type": "textblock", "id": 1, "content": [0], "text": "第五节 函数的微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [143, 210, 299, 207, 299, 230, 142, 232], "type": "textblock", "id": 2, "content": [1], "text": "对照，列表于下：", "outline_level": -1, "sub_type": "text"}, {"cols": 2, "id": 3, "rows": 16, "outline_level": -1, "columns_width": [404, 380], "parse_type": "Ocr", "pos": [146, 240, 949, 236, 954, 1065, 150, 1070], "type": "table", "text": "\n| 导数公式 | 微分公式 |\n| --- | --- |\n| $(x^{\\mu })^{\\prime }=\\mu x^{\\mu -1}(\\mu$是任意常数） | $d(x^{\\mu })=\\mu x^{\\mu -1}dx(\\mu$是任意常数） |\n| $(\\sin x)^{\\prime }=\\cos x$ | $d(\\sin x)=\\cos xdx$ |\n| $(\\cos x)^{\\prime }=-\\sin x$ | $d(\\cos x)=-\\sin xdx$ |\n| $(\\tan x)^{\\prime }=\\sec ^{2}x$ | $d(\\tan x)=\\sec ^{2}xdx$ |\n| $(\\cot x)^{\\prime }=-\\csc ^{2}x$ | $d(\\cot x)=-\\csc ^{2}xdx$ |\n| $(\\sec x)^{\\prime }=\\sec x\\tan x$ | $d(\\sec x)=\\sec x\\tan xdx$ |\n| $(\\csc x)^{\\prime }=-\\csc x\\cot x$ | $d(\\csc x)=-\\csc x\\cot xdx$ |\n| $(a^{x})^{\\prime }=a^{x}\\ln a(a>0$且$a\\neq 1)$ | $d(a^{x})=a^{x}\\ln adx(a>0$且$a\\neq 1)$ |\n| $(e^{x})^{\\prime }=e^{x}$ | $d(e^{x})=e^{x}dx$ |\n| $(\\log _{a}x)^{\\prime }=\\frac {1}{x\\ln a}(a>0$且$a\\neq 1)$ | $d(\\log _{a}x)=\\frac {1}{x\\ln a}dx(a>0$且$a\\neq 1)$ |\n| $(\\ln x)^{\\prime }=\\frac {1}{x}$ | $d(\\ln x)=\\frac {1}{x}dx$ |\n| $(\\arcsin x)^{\\prime }=\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arcsin x)=\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arccos x)^{\\prime }=-\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arccos x)=-\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arctan x)^{\\prime }=\\frac {1}{1+x^{2}}$ | $d(\\arctan x)=\\frac {1}{1+x^{2}}dx$ |\n| $(\\operatorname{arccot}x)^{\\prime }=-\\frac {1}{1+x^{2}}$ | $d(\\operatorname{arccot}x)=-\\frac {1}{1+x^{2}}dx$ |\n", "sub_type": "bordered", "cells": [{"row_span": 1, "pos": [151, 246, 556, 241, 557, 278, 152, 283], "col": 0, "col_span": 1, "content": [{"content": [2], "pos": [151, 246, 556, 241, 557, 278, 152, 283], "type": "textblock"}], "row": 0}, {"row_span": 1, "pos": [556, 241, 937, 237, 938, 274, 557, 278], "col": 1, "col_span": 1, "content": [{"content": [3], "pos": [556, 241, 937, 237, 938, 274, 557, 278], "type": "textblock"}], "row": 0}, {"row_span": 1, "pos": [152, 283, 557, 278, 557, 321, 152, 326], "col": 0, "col_span": 1, "content": [{"content": [4, 5], "pos": [152, 283, 557, 278, 557, 321, 152, 326], "type": "textblock"}], "row": 1}, {"row_span": 1, "pos": [557, 278, 938, 274, 938, 317, 557, 321], "col": 1, "col_span": 1, "content": [{"content": [6, 7], "pos": [557, 278, 938, 274, 938, 317, 557, 321], "type": "textblock"}], "row": 1}, {"row_span": 1, "pos": [152, 326, 557, 321, 558, 363, 153, 368], "col": 0, "col_span": 1, "content": [{"content": [8], "pos": [152, 326, 557, 321, 558, 363, 153, 368], "type": "textblock"}], "row": 2}, {"row_span": 1, "pos": [557, 321, 938, 317, 939, 359, 558, 363], "col": 1, "col_span": 1, "content": [{"content": [9], "pos": [557, 321, 938, 317, 939, 359, 558, 363], "type": "textblock"}], "row": 2}, {"row_span": 1, "pos": [153, 368, 558, 363, 558, 401, 153, 406], "col": 0, "col_span": 1, "content": [{"content": [10], "pos": [153, 368, 558, 363, 558, 401, 153, 406], "type": "textblock"}], "row": 3}, {"row_span": 1, "pos": [558, 363, 939, 359, 939, 397, 558, 401], "col": 1, "col_span": 1, "content": [{"content": [11], "pos": [558, 363, 939, 359, 939, 397, 558, 401], "type": "textblock"}], "row": 3}, {"row_span": 1, "pos": [153, 406, 558, 401, 559, 449, 154, 454], "col": 0, "col_span": 1, "content": [{"content": [12], "pos": [153, 406, 558, 401, 559, 449, 154, 454], "type": "textblock"}], "row": 4}, {"row_span": 1, "pos": [558, 401, 939, 397, 940, 445, 559, 449], "col": 1, "col_span": 1, "content": [{"content": [13], "pos": [558, 401, 939, 397, 940, 445, 559, 449], "type": "textblock"}], "row": 4}, {"row_span": 1, "pos": [154, 454, 559, 449, 559, 491, 154, 496], "col": 0, "col_span": 1, "content": [{"content": [14], "pos": [154, 454, 559, 449, 559, 491, 154, 496], "type": "textblock"}], "row": 5}, {"row_span": 1, "pos": [559, 449, 940, 445, 940, 487, 559, 491], "col": 1, "col_span": 1, "content": [{"content": [15], "pos": [559, 449, 940, 445, 940, 487, 559, 491], "type": "textblock"}], "row": 5}, {"row_span": 1, "pos": [154, 496, 559, 491, 560, 539, 155, 544], "col": 0, "col_span": 1, "content": [{"content": [16], "pos": [154, 496, 559, 491, 560, 539, 155, 544], "type": "textblock"}], "row": 6}, {"row_span": 1, "pos": [559, 491, 940, 487, 941, 535, 560, 539], "col": 1, "col_span": 1, "content": [{"content": [17], "pos": [559, 491, 940, 487, 941, 535, 560, 539], "type": "textblock"}], "row": 6}, {"row_span": 1, "pos": [155, 544, 560, 539, 560, 582, 155, 587], "col": 0, "col_span": 1, "content": [{"content": [18], "pos": [155, 544, 560, 539, 560, 582, 155, 587], "type": "textblock"}], "row": 7}, {"row_span": 1, "pos": [560, 539, 941, 535, 941, 578, 560, 582], "col": 1, "col_span": 1, "content": [{"content": [19], "pos": [560, 539, 941, 535, 941, 578, 560, 582], "type": "textblock"}], "row": 7}, {"row_span": 1, "pos": [155, 587, 560, 582, 561, 625, 156, 630], "col": 0, "col_span": 1, "content": [{"content": [20, 21, 22], "pos": [155, 587, 560, 582, 561, 625, 156, 630], "type": "textblock"}], "row": 8}, {"row_span": 1, "pos": [560, 582, 941, 578, 942, 621, 561, 625], "col": 1, "col_span": 1, "content": [{"content": [23, 24, 25], "pos": [560, 582, 941, 578, 942, 621, 561, 625], "type": "textblock"}], "row": 8}, {"row_span": 1, "pos": [156, 630, 561, 625, 561, 678, 156, 683], "col": 0, "col_span": 1, "content": [{"content": [26], "pos": [156, 630, 561, 625, 561, 678, 156, 683], "type": "textblock"}], "row": 9}, {"row_span": 1, "pos": [561, 625, 942, 621, 942, 674, 561, 678], "col": 1, "col_span": 1, "content": [{"content": [27], "pos": [561, 625, 942, 621, 942, 674, 561, 678], "type": "textblock"}], "row": 9}, {"row_span": 1, "pos": [156, 683, 561, 678, 562, 742, 157, 747], "col": 0, "col_span": 1, "content": [{"content": [28, 29, 30], "pos": [156, 683, 561, 678, 562, 742, 157, 747], "type": "textblock"}], "row": 10}, {"row_span": 1, "pos": [561, 678, 942, 674, 943, 738, 562, 742], "col": 1, "col_span": 1, "content": [{"content": [31, 32, 33], "pos": [561, 678, 942, 674, 943, 738, 562, 742], "type": "textblock"}], "row": 10}, {"row_span": 1, "pos": [157, 747, 562, 742, 563, 801, 158, 806], "col": 0, "col_span": 1, "content": [{"content": [34], "pos": [157, 747, 562, 742, 563, 801, 158, 806], "type": "textblock"}], "row": 11}, {"row_span": 1, "pos": [562, 742, 943, 738, 944, 797, 563, 801], "col": 1, "col_span": 1, "content": [{"content": [35], "pos": [562, 742, 943, 738, 944, 797, 563, 801], "type": "textblock"}], "row": 11}, {"row_span": 1, "pos": [158, 806, 563, 801, 563, 870, 158, 875], "col": 0, "col_span": 1, "content": [{"content": [36], "pos": [158, 806, 563, 801, 563, 870, 158, 875], "type": "textblock"}], "row": 12}, {"row_span": 1, "pos": [563, 801, 944, 797, 944, 866, 563, 870], "col": 1, "col_span": 1, "content": [{"content": [37], "pos": [563, 801, 944, 797, 944, 866, 563, 870], "type": "textblock"}], "row": 12}, {"row_span": 1, "pos": [158, 875, 563, 870, 564, 934, 159, 939], "col": 0, "col_span": 1, "content": [{"content": [38], "pos": [158, 875, 563, 870, 564, 934, 159, 939], "type": "textblock"}], "row": 13}, {"row_span": 1, "pos": [563, 870, 944, 866, 945, 930, 564, 934], "col": 1, "col_span": 1, "content": [{"content": [39], "pos": [563, 870, 944, 866, 945, 930, 564, 934], "type": "textblock"}], "row": 13}, {"row_span": 1, "pos": [159, 939, 564, 934, 565, 998, 160, 1003], "col": 0, "col_span": 1, "content": [{"content": [40], "pos": [159, 939, 564, 934, 565, 998, 160, 1003], "type": "textblock"}], "row": 14}, {"row_span": 1, "pos": [564, 934, 945, 930, 946, 994, 565, 998], "col": 1, "col_span": 1, "content": [{"content": [41], "pos": [564, 934, 945, 930, 946, 994, 565, 998], "type": "textblock"}], "row": 14}, {"row_span": 1, "pos": [160, 1003, 565, 998, 565, 1057, 160, 1062], "col": 0, "col_span": 1, "content": [{"content": [42], "pos": [160, 1003, 565, 998, 565, 1057, 160, 1062], "type": "textblock"}], "row": 15}, {"row_span": 1, "pos": [565, 998, 946, 994, 946, 1053, 565, 1057], "col": 1, "col_span": 1, "content": [{"content": [43], "pos": [565, 998, 946, 994, 946, 1053, 565, 1057], "type": "textblock"}], "row": 15}], "rows_height": [36, 42, 42, 38, 48, 42, 48, 42, 42, 52, 64, 58, 69, 64, 64, 58]}, {"pos": [197, 1093, 533, 1089, 532, 1111, 196, 1115], "type": "textblock", "id": 4, "content": [44], "text": "2．函数和、差、积、商的微分法则", "outline_level": -1, "sub_type": "text_title"}, {"pos": [158, 1139, 955, 1139, 955, 1210, 158, 1210], "type": "textblock", "id": 5, "content": [45, 46, 47, 48], "text": "由函数和、差、积、商的求导法则，可推得相应的微分法则.为了便于对照，列成下表（表中$u=u(x),v=v(x)$都可导）.", "outline_level": -1, "sub_type": "text"}, {"cols": 2, "id": 6, "rows": 5, "outline_level": -1, "columns_width": [400, 367], "parse_type": "Ocr", "pos": [160, 1225, 955, 1216, 959, 1437, 162, 1446], "type": "table", "text": "\n| 函数和、差、积、商的求导法则 | 函数和、差、积、商的微分法则 |\n| --- | --- |\n| $(u\\pm v)^{\\prime }=u^{\\prime }\\pm v^{\\prime }$ | $d(u\\pm v)=du\\pm dv$ |\n| $(Cu)^{\\prime }=Cu^{\\prime }$ $(C$是常数） | $d(Cu)=Cdu$（$C$是常数） |\n| $(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }$ | $d(uv)=vdu+udv$ |\n| $(\\frac {u}{v})^{\\prime }=\\frac {u^{\\prime }v-uv^{\\prime }}{v^{2}}$ $(v\\neq 0)$ | $d(\\frac {u}{v})=\\frac {vdu-udv}{v^{2}}$ $(v\\neq 0)$ |\n", "sub_type": "bordered", "cells": [{"row_span": 1, "pos": [172, 1224, 573, 1219, 574, 1255, 173, 1260], "col": 0, "col_span": 1, "content": [{"content": [49], "pos": [172, 1224, 573, 1219, 574, 1255, 173, 1260], "type": "textblock"}], "row": 0}, {"row_span": 1, "pos": [573, 1219, 940, 1215, 940, 1251, 574, 1255], "col": 1, "col_span": 1, "content": [{"content": [50], "pos": [573, 1219, 940, 1215, 940, 1251, 574, 1255], "type": "textblock"}], "row": 0}, {"row_span": 1, "pos": [173, 1260, 574, 1255, 574, 1291, 173, 1296], "col": 0, "col_span": 1, "content": [{"content": [51], "pos": [173, 1260, 574, 1255, 574, 1291, 173, 1296], "type": "textblock"}], "row": 1}, {"row_span": 1, "pos": [574, 1255, 940, 1251, 941, 1287, 574, 1291], "col": 1, "col_span": 1, "content": [{"content": [52], "pos": [574, 1255, 940, 1251, 941, 1287, 574, 1291], "type": "textblock"}], "row": 1}, {"row_span": 1, "pos": [173, 1296, 574, 1291, 574, 1333, 173, 1338], "col": 0, "col_span": 1, "content": [{"content": [53, 54, 55], "pos": [173, 1296, 574, 1291, 574, 1333, 173, 1338], "type": "textblock"}], "row": 2}, {"row_span": 1, "pos": [574, 1291, 941, 1287, 941, 1329, 574, 1333], "col": 1, "col_span": 1, "content": [{"content": [56, 57, 58, 59], "pos": [574, 1291, 941, 1287, 941, 1329, 574, 1333], "type": "textblock"}], "row": 2}, {"row_span": 1, "pos": [173, 1338, 574, 1333, 575, 1369, 174, 1374], "col": 0, "col_span": 1, "content": [{"content": [60], "pos": [173, 1338, 574, 1333, 575, 1369, 174, 1374], "type": "textblock"}], "row": 3}, {"row_span": 1, "pos": [574, 1333, 941, 1329, 942, 1365, 575, 1369], "col": 1, "col_span": 1, "content": [{"content": [61], "pos": [574, 1333, 941, 1329, 942, 1365, 575, 1369], "type": "textblock"}], "row": 3}, {"row_span": 1, "pos": [174, 1374, 575, 1369, 575, 1431, 174, 1436], "col": 0, "col_span": 1, "content": [{"content": [62, 63], "pos": [174, 1374, 575, 1369, 575, 1431, 174, 1436], "type": "textblock"}], "row": 4}, {"row_span": 1, "pos": [575, 1369, 942, 1365, 942, 1427, 575, 1431], "col": 1, "col_span": 1, "content": [{"content": [64, 65], "pos": [575, 1369, 942, 1365, 942, 1427, 575, 1431], "type": "textblock"}], "row": 4}], "rows_height": [36, 36, 42, 36, 62]}, {"pos": [977, 1521, 1013, 1521, 1013, 1537, 977, 1537], "blocks": [{"pos": [977, 1521, 1013, 1521, 1013, 1537, 977, 1537], "type": "textblock", "id": 8, "content": [66], "text": "111", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1734.4929199219, "image_id": "", "width": 1190}, {"angle": 0, "page_id": 2, "content": [{"pos": [222, 117, 380, 119, 380, 145, 222, 143], "id": 0, "score": 0.99800002574921, "type": "line", "text": "第二章 导数与微分"}, {"pos": [265, 205, 671, 211, 671, 240, 265, 235], "id": 1, "score": 0.99599999189377, "type": "line", "text": "现在我们以乘积的微分法则为例加以证明."}, {"pos": [266, 243, 522, 247, 521, 276, 266, 273], "id": 2, "score": 0.9990000128746, "type": "line", "text": "根据函数微分的表达式，有"}, {"pos": [543, 285, 701, 285, 701, 314, 543, 314], "id": 3, "type": "line", "score": 1, "text": "$$d ( u v ) = ( u v ) ^ { \\prime } d x .$$", "sub_type": "formula"}, {"pos": [222, 318, 479, 321, 478, 351, 222, 348], "id": 4, "score": 0.9990000128746, "type": "line", "text": "再根据乘积的求导法则，有"}, {"pos": [549, 362, 696, 362, 696, 390, 549, 390], "id": 5, "type": "line", "score": 1, "text": "$$( u v ) ^ { \\prime } = u ^ { \\prime } v + u v ^ { \\prime } .$$", "sub_type": "formula"}, {"pos": [222, 395, 271, 395, 271, 423, 222, 423], "id": 6, "score": 0.9990000128746, "type": "line", "text": "于是"}, {"pos": [457, 435, 785, 437, 784, 466, 457, 464], "id": 7, "type": "line", "score": 1, "text": "$$d ( u v ) = ( u ^ { \\prime } v + u v ^ { \\prime } ) d x = u ^ { \\prime } v d x + u v ^ { \\prime } d x .$$", "sub_type": "formula"}, {"pos": [222, 470, 271, 470, 271, 498, 222, 498], "id": 8, "score": 0.9990000128746, "type": "line", "text": "由于"}, {"pos": [526, 511, 709, 514, 708, 542, 526, 538], "id": 9, "type": "line", "score": 1, "text": "$$u ^ { \\prime } d x = d u , v ^ { \\prime } d x = d v$$", "sub_type": "formula"}, {"pos": [220, 544, 270, 544, 270, 572, 220, 572], "id": 10, "score": 0.9990000128746, "type": "line", "text": "所以"}, {"pos": [539, 587, 701, 587, 701, 615, 539, 615], "id": 11, "type": "line", "score": 1, "text": "$$d ( u v ) = v d u + u d v .$$", "sub_type": "formula"}, {"pos": [263, 620, 578, 624, 577, 654, 263, 650], "id": 12, "score": 0.99800002574921, "type": "line", "text": "其他法则都可以用类似方法证明."}, {"pos": [262, 677, 489, 679, 488, 709, 261, 707], "id": 13, "score": 0.9990000128746, "type": "line", "text": "3．复合函数的微分法则"}, {"pos": [265, 733, 869, 740, 868, 770, 264, 763], "id": 14, "score": 0.9990000128746, "type": "line", "text": "与复合函数的求导法则相应的复合函数的微分法则可推导如下："}, {"pos": [263, 771, 291, 771, 291, 799, 263, 799], "id": 15, "score": 0.9990000128746, "type": "line", "text": "设"}, {"pos": [289, 773, 363, 773, 363, 801, 289, 801], "id": 16, "type": "line", "score": 1, "text": "$y = f ( u )$", "sub_type": "formula"}, {"pos": [362, 773, 388, 773, 388, 801, 362, 801], "id": 17, "score": 0.9990000128746, "type": "line", "text": "及"}, {"pos": [388, 775, 465, 775, 465, 803, 388, 803], "id": 18, "type": "line", "score": 1, "text": "$u = g ( x )$", "sub_type": "formula"}, {"pos": [465, 775, 655, 775, 655, 804, 465, 804], "id": 19, "score": 0.9990000128746, "type": "line", "text": "都可导，则复合函数"}, {"pos": [655, 776, 770, 776, 770, 806, 655, 806], "id": 20, "type": "line", "score": 1, "text": "$y = f [ g ( x ) ]$", "sub_type": "formula"}, {"pos": [766, 778, 860, 778, 860, 807, 766, 807], "id": 21, "score": 0.9990000128746, "type": "line", "text": "的微分为"}, {"pos": [493, 810, 744, 814, 743, 845, 493, 841], "id": 22, "type": "line", "score": 1, "text": "$$d y = y _ { x } ^ { \\prime } d x \\textcircled { 1 } = f ^ { \\prime } ( u ) g ^ { \\prime } ( x ) d x .$$", "sub_type": "formula"}, {"pos": [263, 847, 311, 847, 311, 873, 263, 873], "id": 23, "score": 0.9990000128746, "type": "line", "text": "由于"}, {"pos": [309, 847, 433, 847, 433, 875, 309, 875], "id": 24, "type": "line", "score": 1, "text": "$g ^ { \\prime } ( x ) d x = d u$", "sub_type": "formula"}, {"pos": [429, 849, 585, 849, 585, 878, 429, 878], "id": 25, "score": 0.9990000128746, "type": "line", "text": "，所以，复合函数"}, {"pos": [585, 850, 687, 850, 687, 880, 585, 880], "id": 26, "type": "line", "score": 1, "text": "$y = f [ g ( x )$", "sub_type": "formula"}, {"pos": [686, 851, 923, 854, 923, 883, 686, 881], "id": 27, "score": 0.99800002574921, "type": "line", "text": "］的微分公式也可以写成"}, {"pos": [483, 886, 604, 886, 604, 916, 483, 916], "id": 28, "type": "line", "score": 1, "text": "$d y = f ^ { \\prime } ( u ) d u$", "sub_type": "formula"}, {"pos": [618, 890, 648, 890, 648, 916, 618, 916], "id": 29, "score": 0.9990000128746, "type": "line", "text": "或"}, {"pos": [663, 890, 753, 890, 753, 919, 663, 919], "id": 30, "type": "line", "score": 1, "text": "$d y = y _ { u } ^ { \\prime } d u .$", "sub_type": "formula"}, {"pos": [265, 924, 405, 924, 405, 946, 265, 946], "id": 31, "score": 0.9990000128746, "type": "line", "text": "由此可见，无论"}, {"pos": [408, 926, 426, 926, 426, 947, 408, 947], "id": 32, "type": "line", "score": 1, "text": "$u$", "sub_type": "formula"}, {"pos": [428, 926, 747, 926, 747, 952, 428, 952], "id": 33, "score": 0.9990000128746, "type": "line", "text": "是自变量还是中间变量，微分形式"}, {"pos": [751, 928, 853, 928, 853, 958, 751, 958], "id": 34, "type": "line", "score": 1, "text": "$d y = f ^ { \\prime } ( u )$", "sub_type": "formula"}, {"pos": [847, 931, 877, 931, 877, 957, 847, 957], "id": 35, "score": 0.99800002574921, "type": "line", "text": "u"}, {"pos": [874, 929, 1023, 931, 1022, 963, 873, 960], "id": 36, "score": 0.99800002574921, "type": "line", "text": "保持不变.这一"}, {"pos": [219, 957, 847, 967, 847, 998, 218, 988], "id": 37, "score": 0.99400001764297, "type": "line", "text": "性质称为微分形式不变性.这性质表示，当变换自变量时，微分形式"}, {"pos": [847, 969, 970, 969, 970, 998, 847, 998], "id": 38, "type": "line", "score": 1, "text": "$d y = f ^ { \\prime } ( u ) d u$", "sub_type": "formula"}, {"pos": [970, 970, 1021, 970, 1021, 998, 970, 998], "id": 39, "score": 0.9990000128746, "type": "line", "text": "并不"}, {"pos": [217, 995, 271, 995, 271, 1023, 217, 1023], "id": 40, "score": 0.99800002574921, "type": "line", "text": "改变."}, {"pos": [261, 1034, 349, 1034, 349, 1062, 261, 1062], "id": 41, "score": 0.94900000095367, "type": "line", "text": "例3 设"}, {"pos": [347, 1035, 474, 1037, 474, 1066, 347, 1064], "id": 42, "type": "line", "score": 1, "text": "$y = \\sin ( 2 x + 1 )$", "sub_type": "formula"}, {"pos": [475, 1038, 511, 1038, 511, 1066, 475, 1066], "id": 43, "score": 0.99800002574921, "type": "line", "text": "，求"}, {"pos": [507, 1039, 541, 1039, 541, 1066, 507, 1066], "id": 44, "type": "line", "score": 1, "text": "$d y .$", "sub_type": "formula"}, {"pos": [260, 1070, 333, 1073, 332, 1101, 259, 1098], "id": 45, "score": 0.9990000128746, "type": "line", "text": "解把"}, {"pos": [331, 1072, 380, 1072, 380, 1099, 331, 1099], "id": 46, "type": "line", "score": 1, "text": "$2 x + 1$", "sub_type": "formula"}, {"pos": [384, 1076, 515, 1076, 515, 1098, 384, 1098], "id": 47, "score": 0.9990000128746, "type": "line", "text": "看成中间变量"}, {"pos": [517, 1079, 534, 1079, 534, 1100, 517, 1100], "id": 48, "type": "line", "score": 1, "text": "$u$", "sub_type": "formula"}, {"pos": [533, 1079, 564, 1079, 564, 1100, 533, 1100], "id": 49, "score": 0.9990000128746, "type": "line", "text": "，则"}, {"pos": [410, 1109, 824, 1118, 824, 1149, 409, 1141], "id": 50, "type": "line", "score": 1, "text": "$$d y = d ( \\sin u ) = \\cos u d u = \\cos ( 2 x + 1 ) d ( 2 x + 1 )$$", "sub_type": "formula"}, {"pos": [434, 1148, 767, 1155, 766, 1184, 434, 1177], "id": 51, "type": "line", "score": 1, "text": "$$= \\cos ( 2 x + 1 ) \\cdot 2 d x = 2 \\cos ( 2 x + 1 ) d x .$$", "sub_type": "formula"}, {"pos": [260, 1181, 1020, 1197, 1019, 1228, 259, 1212], "id": 52, "score": 0.9990000128746, "type": "line", "text": "在求复合函数的导数时，可以不写出中间变量.在求复合函数的微分时，类似地也"}, {"pos": [217, 1218, 778, 1230, 778, 1260, 217, 1248], "id": 53, "score": 0.99800002574921, "type": "line", "text": "可以不写出中间变量.下面我们用这种方法来求函数的微分."}, {"pos": [258, 1258, 345, 1258, 345, 1286, 258, 1286], "id": 54, "score": 0.88400000333786, "type": "line", "text": "例4 设"}, {"pos": [344, 1258, 467, 1258, 467, 1289, 344, 1289], "id": 55, "type": "line", "score": 1, "text": "$y = \\ln ( 1 + e ^ { x ^ { 2 } } )$", "sub_type": "formula"}, {"pos": [465, 1263, 531, 1263, 531, 1291, 465, 1291], "id": 56, "score": 0.9990000128746, "type": "line", "text": "，求dy."}, {"pos": [257, 1309, 286, 1309, 286, 1337, 257, 1337], "id": 57, "score": 0.9990000128746, "type": "line", "text": "解"}, {"pos": [300, 1295, 1015, 1307, 1014, 1371, 299, 1359], "id": 58, "type": "line", "score": 1, "text": "$d y = d ( \\ln ( 1 + e ^ { x 2 } ) ) = \\frac { 1 } { 1 + e ^ { z ^ { 2 } } } d ( 1 + e ^ { z ^ { 2 } } ) = \\frac { 1 } { 1 + e ^ { z ^ { 2 } } } · e ^ { z ^ { 2 } } d ( x ^ { 2 } ) = \\frac { e ^ { z ^ { 2 } } } { 1 + e ^ { z ^ { 2 } } } · 2 x d x = \\frac { 2 x e ^ { z ^ { 2 } } } { 1 + e ^ { z ^ { 2 } } } d x .$", "sub_type": "formula"}, {"pos": [257, 1365, 344, 1368, 344, 1397, 256, 1394], "id": 59, "score": 0.91000002622604, "type": "line", "text": "例5 设"}, {"pos": [344, 1367, 464, 1367, 464, 1398, 344, 1398], "id": 60, "type": "line", "score": 1, "text": "$y = e ^ { 1 - 3 x } \\cos x$", "sub_type": "formula"}, {"pos": [458, 1370, 524, 1373, 523, 1402, 457, 1399], "id": 61, "score": 0.9990000128746, "type": "line", "text": "，求dy."}, {"pos": [246, 1445, 268, 1445, 268, 1467, 246, 1467], "id": 62, "type": "line", "score": 1, "text": "$\\textcircled { 1 }$", "sub_type": "formula"}, {"pos": [280, 1448, 312, 1448, 312, 1463, 280, 1463], "id": 63, "score": 0.98100000619888, "type": "line", "text": "其中"}, {"pos": [315, 1447, 332, 1447, 332, 1468, 315, 1468], "id": 64, "type": "line", "score": 1, "text": "$y _ { x } ^ { \\prime }$", "sub_type": "formula"}, {"pos": [331, 1449, 364, 1449, 364, 1466, 331, 1466], "id": 65, "score": 0.98100000619888, "type": "line", "text": "2表示"}, {"pos": [367, 1449, 380, 1449, 380, 1469, 367, 1469], "id": 66, "type": "line", "score": 1, "text": "$y$", "sub_type": "formula"}, {"pos": [382, 1450, 396, 1450, 396, 1465, 382, 1465], "id": 67, "score": 0.98100000619888, "type": "line", "text": "对"}, {"pos": [400, 1450, 412, 1450, 412, 1468, 400, 1468], "id": 68, "type": "line", "score": 1, "text": "$x$", "sub_type": "formula"}, {"pos": [415, 1451, 677, 1451, 677, 1472, 415, 1472], "id": 69, "score": 0.98100000619888, "type": "line", "text": "的导数，它也是导数的一种表示方法．"}, {"pos": [163, 1518, 197, 1518, 197, 1538, 163, 1538], "id": 70, "score": 0.9990000128746, "type": "line", "text": "112"}], "status": "Success", "height": 1684, "structured": [{"pos": [221, 121, 379, 123, 380, 139, 222, 137], "blocks": [{"pos": [221, 121, 379, 123, 380, 139, 222, 137], "type": "textblock", "id": 1, "content": [0], "text": "第二章 导数与微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [264, 207, 670, 213, 671, 235, 265, 230], "type": "textblock", "id": 2, "content": [1], "text": "现在我们以乘积的微分法则为例加以证明.", "outline_level": -1, "sub_type": "text"}, {"pos": [265, 245, 521, 249, 521, 271, 266, 268], "type": "textblock", "id": 3, "content": [2], "text": "根据函数微分的表达式，有", "outline_level": -1, "sub_type": "text"}, {"pos": [543, 285, 701, 285, 701, 314, 543, 314], "type": "textblock", "id": 4, "content": [3], "text": "$$d(uv)=(uv)^{\\prime }dx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [221, 321, 478, 324, 478, 347, 222, 344], "type": "textblock", "id": 5, "content": [4], "text": "再根据乘积的求导法则，有", "outline_level": -1, "sub_type": "text"}, {"pos": [549, 362, 696, 362, 696, 390, 549, 390], "type": "textblock", "id": 6, "content": [5], "text": "$$(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [222, 398, 271, 398, 271, 418, 222, 418], "type": "textblock", "id": 7, "content": [6], "text": "于是", "outline_level": -1, "sub_type": "text"}, {"pos": [457, 435, 785, 437, 784, 466, 457, 464], "type": "textblock", "id": 8, "content": [7], "text": "$$d(uv)=(u^{\\prime }v+uv^{\\prime })dx=u^{\\prime }vdx+uv^{\\prime }dx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [222, 473, 271, 473, 271, 494, 222, 494], "type": "textblock", "id": 9, "content": [8], "text": "由于", "outline_level": -1, "sub_type": "text"}, {"pos": [526, 511, 709, 514, 708, 542, 526, 538], "type": "textblock", "id": 10, "content": [9], "text": "$$u^{\\prime }dx=du,v^{\\prime }dx=dv$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [220, 548, 270, 548, 270, 569, 220, 569], "type": "textblock", "id": 11, "content": [10], "text": "所以", "outline_level": -1, "sub_type": "text"}, {"pos": [539, 587, 701, 587, 701, 615, 539, 615], "type": "textblock", "id": 12, "content": [11], "text": "$$d(uv)=vdu+udv.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [262, 623, 577, 627, 577, 649, 263, 645], "type": "textblock", "id": 13, "content": [12], "text": "其他法则都可以用类似方法证明.", "outline_level": -1, "sub_type": "text"}, {"pos": [261, 680, 488, 682, 488, 704, 261, 702], "type": "textblock", "id": 14, "content": [13], "text": "3．复合函数的微分法则", "outline_level": -1, "sub_type": "text_title"}, {"pos": [264, 735, 868, 742, 868, 765, 264, 758], "type": "textblock", "id": 15, "content": [14], "text": "与复合函数的求导法则相应的复合函数的微分法则可推导如下：", "outline_level": -1, "sub_type": "text"}, {"pos": [263, 771, 860, 771, 860, 806, 263, 806], "type": "textblock", "id": 16, "content": [15, 16, 17, 18, 19, 20, 21], "text": "设$y=f(u)$及$u=g(x)$都可导，则复合函数$y=f[g(x)]$的微分为", "outline_level": -1, "sub_type": "text"}, {"pos": [493, 810, 744, 814, 743, 845, 493, 841], "type": "textblock", "id": 17, "content": [22], "text": "$$dy=y_{x}^{\\prime }dx\\textcircled {1}=f^{\\prime }(u)g^{\\prime }(x)dx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [263, 847, 923, 847, 923, 880, 263, 880], "type": "textblock", "id": 18, "content": [23, 24, 25, 26, 27], "text": "由于$g^{\\prime }(x)dx=du$，所以，复合函数$y=f[g(x)$］的微分公式也可以写成", "outline_level": -1, "sub_type": "text"}, {"pos": [483, 886, 753, 886, 753, 919, 483, 919], "type": "textblock", "id": 19, "content": [28, 29, 30], "text": "$dy=f^{\\prime }(u)du$ 或 $dy=y_{u}^{\\prime }du.$", "outline_level": -1, "sub_type": "text"}, {"pos": [217, 922, 1022, 922, 1022, 1018, 217, 1018], "type": "textblock", "id": 20, "content": [31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "text": "由此可见，无论$u$是自变量还是中间变量，微分形式$dy=f^{\\prime }(u)$u保持不变.这一性质称为微分形式不变性.这性质表示，当变换自变量时，微分形式$dy=f^{\\prime }(u)du$并不改变.", "outline_level": -1, "sub_type": "text"}, {"pos": [261, 1035, 541, 1035, 541, 1066, 261, 1066], "type": "textblock", "id": 21, "content": [41, 42, 43, 44], "text": "例3 设$y=\\sin (2x+1)$，求$dy.$", "outline_level": -1, "sub_type": "text"}, {"pos": [259, 1071, 569, 1072, 568, 1100, 258, 1098], "type": "textblock", "id": 22, "content": [45, 46, 47, 48, 49], "text": "解把$2x+1$看成中间变量$u$，则", "outline_level": -1, "sub_type": "text"}, {"pos": [410, 1109, 824, 1118, 824, 1149, 409, 1141], "type": "textblock", "id": 23, "content": [50], "text": "$$dy=d(\\sin u)=\\cos udu=\\cos (2x+1)d(2x+1)$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [434, 1148, 767, 1155, 766, 1184, 434, 1177], "type": "textblock", "id": 24, "content": [51], "text": "$$=\\cos (2x+1)\\cdot 2dx=2\\cos (2x+1)dx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [216, 1182, 1019, 1199, 1018, 1260, 215, 1243], "type": "textblock", "id": 25, "content": [52, 53], "text": "在求复合函数的导数时，可以不写出中间变量.在求复合函数的微分时，类似地也可以不写出中间变量.下面我们用这种方法来求函数的微分.", "outline_level": -1, "sub_type": "text"}, {"pos": [258, 1258, 531, 1258, 531, 1289, 258, 1289], "type": "textblock", "id": 26, "content": [54, 55, 56], "text": "例4 设$y=\\ln (1+e^{x^{2}})$，求dy.", "outline_level": -1, "sub_type": "text"}, {"pos": [257, 1295, 1015, 1295, 1015, 1371, 257, 1371], "type": "textblock", "id": 27, "content": [57, 58], "text": "解 $dy=d(\\ln (1+e^{x2}))=\\frac {1}{1+e^{z^{2}}}d(1+e^{z^{2}})=\\frac {1}{1+e^{z^{2}}}·e^{z^{2}}d(x^{2})=\\frac {e^{z^{2}}}{1+e^{z^{2}}}·2xdx=\\frac {2xe^{z^{2}}}{1+e^{z^{2}}}dx.$", "outline_level": -1, "sub_type": "text"}, {"pos": [256, 1367, 523, 1367, 523, 1398, 256, 1398], "type": "textblock", "id": 28, "content": [59, 60, 61], "text": "例5 设$y=e^{1-3x}\\cos x$，求dy.", "outline_level": -1, "sub_type": "text"}, {"pos": [244, 1446, 679, 1455, 679, 1473, 245, 1464], "type": "textblock", "id": 29, "content": [62, 63, 64, 65, 66, 67, 68, 69], "text": "$\\textcircled {1}$ 其中$y_{x}^{\\prime }$2表示$y$对$x$的导数，它也是导数的一种表示方法．", "outline_level": -1, "sub_type": "text"}, {"pos": [163, 1520, 197, 1520, 197, 1536, 163, 1536], "blocks": [{"pos": [163, 1520, 197, 1520, 197, 1536, 163, 1536], "type": "textblock", "id": 30, "content": [70], "text": "112", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1399.7904052734, "image_id": "", "width": 1190}, {"angle": 0, "page_id": 3, "content": [{"pos": [780, 116, 934, 119, 934, 144, 779, 142], "id": 0, "score": 0.97299998998642, "type": "line", "text": "第五节 函数的微分"}, {"pos": [178, 199, 434, 202, 434, 232, 177, 229], "id": 1, "score": 0.99500000476837, "type": "line", "text": "解 应用积的微分法则，得"}, {"pos": [224, 236, 839, 236, 839, 315, 224, 315], "id": 2, "type": "line", "score": 1, "text": "$$d y = d ( e ^ { 1 - 3 x } \\cos x ) = \\cos x d ( e ^ { 1 - 3 x } ) + e ^ { 1 - 3 x } d ( \\cos x ) \\\\ = ( \\cos x ) e ^ { 1 - 3 x } ( - 3 d x ) + e ^ { 1 - 3 x } ( - \\sin x d x ) = - e ^ { 1 - 3 x } ( 3 \\cos x + \\sin x ) d x .$$", "sub_type": "formula"}, {"pos": [178, 316, 758, 322, 758, 352, 177, 346], "id": 3, "score": 0.99099999666214, "type": "line", "text": "例6 在下列等式左端的括号中填入适当的函数，使等式成立．"}, {"pos": [216, 354, 216, 354, 216, 354, 216, 354], "id": 4, "type": "line", "score": 1, "text": "$($", "sub_type": "formula"}, {"pos": [216, 354, 216, 354, 216, 354, 216, 354], "id": 5, "type": "line", "score": 1, "text": "$)$", "sub_type": "formula"}, {"pos": [510, 360, 510, 360, 510, 360, 510, 360], "id": 6, "type": "line", "score": 1, "text": "$($", "sub_type": "formula"}, {"pos": [510, 360, 510, 360, 510, 360, 510, 360], "id": 7, "type": "line", "score": 1, "text": "$)$", "sub_type": "formula"}, {"pos": [181, 357, 217, 357, 217, 383, 181, 383], "id": 8, "score": 0.9990000128746, "type": "line", "text": "(1)"}, {"pos": [216, 354, 216, 354, 216, 387, 216, 387], "id": 9, "type": "line", "score": 1, "text": "$d$", "sub_type": "formula"}, {"pos": [216, 354, 216, 354, 216, 387, 216, 387], "id": 10, "type": "line", "score": 1, "text": "$=xdx;$", "sub_type": "formula"}, {"pos": [472, 360, 510, 360, 510, 386, 472, 386], "id": 11, "score": 0.9990000128746, "type": "line", "text": "(2)"}, {"pos": [510, 360, 510, 360, 510, 390, 510, 390], "id": 12, "type": "line", "score": 1, "text": "$d$", "sub_type": "formula"}, {"pos": [510, 360, 510, 360, 510, 390, 510, 390], "id": 13, "type": "line", "score": 1, "text": "$=\\cos\\omegatdt$", "sub_type": "formula"}, {"pos": [674, 362, 752, 362, 752, 390, 674, 390], "id": 14, "type": "line", "score": 1, "text": "$( \\neq 0 )$", "sub_type": "formula"}, {"pos": [178, 393, 367, 393, 367, 423, 178, 423], "id": 15, "score": 0.97500002384186, "type": "line", "text": "解（1）我们知道，"}, {"pos": [470, 434, 595, 434, 595, 462, 470, 462], "id": 16, "type": "line", "score": 1, "text": "$$d ( x ^ { 2 } ) = 2 x d x .$$", "sub_type": "formula"}, {"pos": [133, 469, 184, 469, 184, 497, 133, 497], "id": 17, "score": 0.9990000128746, "type": "line", "text": "可见"}, {"pos": [421, 510, 638, 510, 638, 572, 421, 572], "id": 18, "type": "line", "score": 1, "text": "$$x d x = \\frac { 1 } { 2 } d ( x ^ { 2 } ) = d ( \\frac { x ^ { 2 } } { 2 } )$$", "sub_type": "formula"}, {"pos": [133, 576, 161, 576, 161, 605, 133, 605], "id": 19, "score": 0.9990000128746, "type": "line", "text": "即"}, {"pos": [472, 617, 592, 617, 592, 679, 472, 679], "id": 20, "type": "line", "score": 1, "text": "$$d ( \\frac { x ^ { 2 } } { 2 } ) = x d x .$$", "sub_type": "formula"}, {"pos": [176, 686, 280, 686, 280, 715, 176, 715], "id": 21, "score": 0.9990000128746, "type": "line", "text": "一般地，有"}, {"pos": [374, 723, 519, 725, 518, 788, 373, 786], "id": 22, "type": "line", "score": 1, "text": "$d ( \\frac { x ^ { 2 } } { 2 } + C ) = x d x$", "sub_type": "formula"}, {"pos": [528, 745, 539, 745, 539, 767, 528, 767], "id": 23, "score": 0.9990000128746, "type": "line", "text": "（"}, {"pos": [539, 744, 558, 744, 558, 767, 539, 767], "id": 24, "type": "line", "score": 1, "text": "$C$", "sub_type": "formula"}, {"pos": [560, 745, 688, 745, 688, 767, 560, 767], "id": 25, "score": 0.9990000128746, "type": "line", "text": "为任意常数）."}, {"pos": [179, 794, 270, 794, 270, 822, 179, 822], "id": 26, "score": 0.9990000128746, "type": "line", "text": "（2）因为"}, {"pos": [428, 834, 638, 836, 638, 865, 427, 863], "id": 27, "type": "line", "score": 1, "text": "$$d ( \\sin \\omega t ) = \\omega \\cos \\omega t d t ,$$", "sub_type": "formula"}, {"pos": [133, 870, 183, 870, 183, 898, 133, 898], "id": 28, "score": 0.9990000128746, "type": "line", "text": "可见"}, {"pos": [355, 903, 702, 903, 702, 965, 355, 965], "id": 29, "type": "line", "score": 1, "text": "$$\\cos \\omega t d t = \\frac { 1 } { \\omega } d ( \\sin \\omega t ) = d ( \\frac { 1 } { \\omega } \\sin \\omega t ) ,$$", "sub_type": "formula"}, {"pos": [133, 972, 161, 972, 161, 1000, 133, 1000], "id": 30, "score": 0.9990000128746, "type": "line", "text": "即"}, {"pos": [419, 1003, 645, 1003, 645, 1066, 419, 1066], "id": 31, "type": "line", "score": 1, "text": "$$d ( \\frac { 1 } { \\omega } \\sin \\omega t ) = \\cos \\omega t d t .$$", "sub_type": "formula"}, {"pos": [178, 1074, 280, 1074, 280, 1102, 178, 1102], "id": 32, "score": 0.9990000128746, "type": "line", "text": "一般地，有"}, {"pos": [298, 1110, 538, 1112, 538, 1175, 297, 1173], "id": 33, "type": "line", "score": 1, "text": "$d ( \\frac { 1 } { \\omega } \\sin \\omega t + C ) = \\cos \\omega t d t$", "sub_type": "formula"}, {"pos": [547, 1131, 558, 1131, 558, 1152, 547, 1152], "id": 34, "score": 0.9990000128746, "type": "line", "text": "（"}, {"pos": [558, 1130, 576, 1130, 576, 1153, 558, 1153], "id": 35, "type": "line", "score": 1, "text": "$C$", "sub_type": "formula"}, {"pos": [579, 1131, 697, 1131, 697, 1152, 579, 1152], "id": 36, "score": 0.9990000128746, "type": "line", "text": "为任意常数，"}, {"pos": [694, 1128, 765, 1128, 765, 1158, 694, 1158], "id": 37, "type": "line", "score": 1, "text": "$\\neq 0 ) .$", "sub_type": "formula"}, {"pos": [178, 1197, 503, 1197, 503, 1230, 178, 1230], "id": 38, "score": 0.9990000128746, "type": "line", "text": "四、微分在近似计算中的应用"}, {"pos": [176, 1275, 360, 1275, 360, 1304, 176, 1304], "id": 39, "score": 0.9990000128746, "type": "line", "text": "1．函数的近似计算"}, {"pos": [178, 1331, 931, 1334, 931, 1364, 178, 1361], "id": 40, "score": 0.99800002574921, "type": "line", "text": "在工程问题中，经常会遇到一些复杂的计算公式.如果直接用这些公式进行计算，"}, {"pos": [132, 1368, 911, 1372, 911, 1402, 131, 1398], "id": 41, "score": 0.99800002574921, "type": "line", "text": "那是很费力的.利用微分往往可以把一些复杂的计算公式用简单的近似公式来代替."}, {"pos": [176, 1406, 322, 1406, 322, 1434, 176, 1434], "id": 42, "score": 0.9990000128746, "type": "line", "text": "前面说过，如果"}, {"pos": [321, 1408, 396, 1408, 396, 1436, 321, 1436], "id": 43, "type": "line", "score": 1, "text": "$y = f ( x )$", "sub_type": "formula"}, {"pos": [395, 1408, 442, 1408, 442, 1436, 395, 1436], "id": 44, "score": 0.9990000128746, "type": "line", "text": "在点"}, {"pos": [441, 1409, 469, 1409, 469, 1436, 441, 1436], "id": 45, "type": "line", "score": 1, "text": "$x _ { 0 }$", "sub_type": "formula"}, {"pos": [463, 1407, 553, 1407, 553, 1436, 463, 1436], "id": 46, "score": 0.9990000128746, "type": "line", "text": "处的导数"}, {"pos": [551, 1408, 653, 1408, 653, 1436, 551, 1436], "id": 47, "type": "line", "score": 1, "text": "$f ^ { \\prime } ( x _ { 0 } ) \\neq 0$", "sub_type": "formula"}, {"pos": [651, 1409, 692, 1409, 692, 1436, 651, 1436], "id": 48, "score": 0.841000020504, "type": "line", "text": "，且1"}, {"pos": [686, 1409, 725, 1409, 725, 1436, 686, 1436], "id": 49, "type": "line", "score": 1, "text": "$\\Delta x \\vert$", "sub_type": "formula"}, {"pos": [724, 1409, 872, 1409, 872, 1437, 724, 1437], "id": 50, "score": 0.9990000128746, "type": "line", "text": "很小时，我们有"}, {"pos": [442, 1446, 622, 1446, 622, 1473, 442, 1473], "id": 51, "type": "line", "score": 1, "text": "$$\\Delta y \\approx d y = f ^ { \\prime } ( x _ { 0 } ) \\Delta x .$$", "sub_type": "formula"}, {"pos": [949, 1528, 982, 1528, 982, 1548, 949, 1548], "id": 52, "score": 0.9990000128746, "type": "line", "text": "113"}], "status": "Success", "height": 1684, "structured": [{"pos": [779, 120, 933, 123, 934, 138, 779, 136], "blocks": [{"pos": [779, 120, 933, 123, 934, 138, 779, 136], "type": "textblock", "id": 1, "content": [0], "text": "第五节 函数的微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [177, 201, 433, 204, 434, 227, 177, 224], "type": "textblock", "id": 2, "content": [1], "text": "解 应用积的微分法则，得", "outline_level": -1, "sub_type": "text"}, {"pos": [227, 236, 839, 241, 839, 312, 227, 312], "type": "textblock", "id": 3, "content": [2], "text": "$$dy=d(e^{1-3x}\\cos x)=\\cos xd(e^{1-3x})+e^{1-3x}d(\\cos x)\\\\ =(\\cos x)e^{1-3x}(-3dx)+e^{1-3x}(-\\sin xdx)=-e^{1-3x}(3\\cos x+\\sin x)dx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [177, 319, 757, 325, 758, 347, 177, 341], "type": "textblock", "id": 5, "content": [3], "text": "例6 在下列等式左端的括号中填入适当的函数，使等式成立．", "outline_level": -1, "sub_type": "text"}, {"pos": [181, 354, 752, 354, 752, 390, 181, 390], "type": "textblock", "id": 6, "content": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "text": "$($ $)$ $($ $)$(1)$d$ $=xdx;$(2)$d$ $=\\cos\\omegatdt$ $(\\neq 0)$", "outline_level": -1, "sub_type": "text"}, {"pos": [178, 396, 367, 396, 367, 420, 178, 420], "type": "textblock", "id": 8, "content": [15], "text": "解（1）我们知道，", "outline_level": -1, "sub_type": "text"}, {"pos": [470, 434, 595, 434, 595, 462, 470, 462], "type": "textblock", "id": 9, "content": [16], "text": "$$d(x^{2})=2xdx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [133, 471, 184, 471, 184, 491, 133, 491], "type": "textblock", "id": 10, "content": [17], "text": "可见", "outline_level": -1, "sub_type": "text"}, {"pos": [421, 510, 638, 510, 638, 572, 421, 572], "type": "textblock", "id": 11, "content": [18], "text": "$$xdx=\\frac {1}{2}d(x^{2})=d(\\frac {x^{2}}{2})$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [133, 576, 161, 576, 161, 605, 133, 605], "type": "textblock", "id": 12, "content": [19], "text": "即", "outline_level": -1, "sub_type": "text"}, {"pos": [472, 617, 592, 617, 592, 679, 472, 679], "type": "textblock", "id": 13, "content": [20], "text": "$$d(\\frac {x^{2}}{2})=xdx.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [176, 689, 280, 689, 280, 710, 176, 710], "type": "textblock", "id": 14, "content": [21], "text": "一般地，有", "outline_level": -1, "sub_type": "text"}, {"pos": [373, 723, 689, 723, 689, 788, 373, 788], "type": "textblock", "id": 15, "content": [22, 23, 24, 25], "text": "$d(\\frac {x^{2}}{2}+C)=xdx$ （$C$为任意常数）.", "outline_level": -1, "sub_type": "text"}, {"pos": [179, 798, 270, 798, 270, 818, 179, 818], "type": "textblock", "id": 16, "content": [26], "text": "（2）因为", "outline_level": -1, "sub_type": "text"}, {"pos": [428, 834, 638, 836, 638, 865, 427, 863], "type": "textblock", "id": 17, "content": [27], "text": "$$d(\\sin \\omega t)=\\omega \\cos \\omega tdt,$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [133, 873, 183, 873, 183, 892, 133, 892], "type": "textblock", "id": 18, "content": [28], "text": "可见", "outline_level": -1, "sub_type": "text"}, {"pos": [355, 903, 702, 903, 702, 965, 355, 965], "type": "textblock", "id": 19, "content": [29], "text": "$$\\cos \\omega tdt=\\frac {1}{\\omega }d(\\sin \\omega t)=d(\\frac {1}{\\omega }\\sin \\omega t),$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [133, 972, 161, 972, 161, 1000, 133, 1000], "type": "textblock", "id": 20, "content": [30], "text": "即", "outline_level": -1, "sub_type": "text"}, {"pos": [419, 1003, 645, 1003, 645, 1066, 419, 1066], "type": "textblock", "id": 21, "content": [31], "text": "$$d(\\frac {1}{\\omega }\\sin \\omega t)=\\cos \\omega tdt.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [178, 1076, 280, 1076, 280, 1098, 178, 1098], "type": "textblock", "id": 22, "content": [32], "text": "一般地，有", "outline_level": -1, "sub_type": "text"}, {"pos": [297, 1110, 765, 1110, 765, 1175, 297, 1175], "type": "textblock", "id": 23, "content": [33, 34, 35, 36, 37], "text": "$d(\\frac {1}{\\omega }\\sin \\omega t+C)=\\cos \\omega tdt$ （$C$为任意常数，$\\neq 0).$", "outline_level": -1, "sub_type": "text"}, {"pos": [178, 1200, 503, 1200, 503, 1226, 178, 1226], "type": "textblock", "id": 24, "content": [38], "text": "四、微分在近似计算中的应用", "outline_level": -1, "sub_type": "text_title"}, {"pos": [176, 1278, 360, 1278, 360, 1299, 176, 1299], "type": "textblock", "id": 25, "content": [39], "text": "1．函数的近似计算", "outline_level": -1, "sub_type": "text_title"}, {"pos": [131, 1333, 931, 1333, 931, 1396, 131, 1396], "type": "textblock", "id": 26, "content": [40, 41], "text": "在工程问题中，经常会遇到一些复杂的计算公式.如果直接用这些公式进行计算，那是很费力的.利用微分往往可以把一些复杂的计算公式用简单的近似公式来代替.", "outline_level": -1, "sub_type": "text"}, {"pos": [176, 1408, 872, 1408, 872, 1436, 176, 1436], "type": "textblock", "id": 27, "content": [42, 43, 44, 45, 46, 47, 48, 49, 50], "text": "前面说过，如果$y=f(x)$在点$x_{0}$处的导数$f^{\\prime }(x_{0})\\neq 0$，且1$\\Delta x\\vert$很小时，我们有", "outline_level": -1, "sub_type": "text"}, {"pos": [442, 1446, 622, 1446, 622, 1473, 442, 1473], "type": "textblock", "id": 28, "content": [51], "text": "$$\\Delta y\\approx dy=f^{\\prime }(x_{0})\\Delta x.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [949, 1528, 982, 1528, 982, 1545, 949, 1545], "blocks": [{"pos": [949, 1528, 982, 1528, 982, 1545, 949, 1545], "type": "textblock", "id": 29, "content": [52], "text": "113", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1275.9375, "image_id": "", "width": 1190}, {"angle": 0, "page_id": 4, "content": [{"pos": [234, 125, 388, 125, 388, 150, 234, 150], "id": 0, "score": 0.98699998855591, "type": "line", "text": "第二章 导数与微分"}, {"pos": [233, 215, 436, 213, 436, 242, 234, 244], "id": 1, "score": 0.9990000128746, "type": "line", "text": "这个式子也可以写为"}, {"pos": [477, 252, 786, 252, 786, 281, 477, 281], "id": 2, "type": "line", "score": 1, "text": "$$\\Delta y = f ( x _ { 0 } + \\Delta x ) - f ( x _ { 0 } ) \\approx f ^ { \\prime } ( x _ { 0 } ) \\Delta x$$", "sub_type": "formula"}, {"pos": [974, 250, 1036, 250, 1036, 280, 974, 280], "id": 3, "score": 0.9990000128746, "type": "line", "text": "(5-4)"}, {"pos": [234, 291, 263, 291, 263, 319, 234, 319], "id": 4, "score": 0.9990000128746, "type": "line", "text": "或"}, {"pos": [496, 329, 768, 327, 768, 357, 497, 359], "id": 5, "type": "line", "score": 1, "text": "$$f ( x _ { 0 } + \\Delta x ) \\approx f ( x _ { 0 } ) + f ^ { \\prime } ( x _ { 0 } ) \\Delta x .$$", "sub_type": "formula"}, {"pos": [974, 327, 1036, 327, 1036, 355, 974, 355], "id": 6, "score": 0.9990000128746, "type": "line", "text": "(5-5)"}, {"pos": [280, 367, 431, 367, 431, 395, 280, 395], "id": 7, "score": 0.9990000128746, "type": "line", "text": "在（5-5）式中令"}, {"pos": [431, 370, 521, 370, 521, 395, 431, 395], "id": 8, "type": "line", "score": 1, "text": "$x = x _ { 0 } + \\Delta x$", "sub_type": "formula"}, {"pos": [518, 368, 554, 368, 554, 395, 518, 395], "id": 9, "score": 0.9990000128746, "type": "line", "text": "，即"}, {"pos": [554, 368, 645, 370, 644, 396, 554, 393], "id": 10, "type": "line", "score": 1, "text": "$\\Delta x = x - x _ { 0 }$", "sub_type": "formula"}, {"pos": [641, 367, 868, 365, 869, 394, 641, 396], "id": 11, "score": 0.9990000128746, "type": "line", "text": "，那么（5-5）式可改写为"}, {"pos": [498, 405, 761, 405, 761, 436, 498, 436], "id": 12, "type": "line", "score": 1, "text": "$$f ( x ) \\approx f ( x _ { 0 } ) + f ^ { \\prime } ( x _ { 0 } ) ( x - x _ { 0 } )$$", "sub_type": "formula"}, {"pos": [974, 405, 1036, 405, 1036, 433, 974, 433], "id": 13, "score": 0.9990000128746, "type": "line", "text": "(5-6)"}, {"pos": [280, 446, 327, 446, 327, 474, 280, 474], "id": 14, "score": 0.9990000128746, "type": "line", "text": "如果"}, {"pos": [324, 446, 380, 446, 380, 475, 324, 475], "id": 15, "type": "line", "score": 1, "text": "$f ( x _ { 0 } )$", "sub_type": "formula"}, {"pos": [378, 444, 405, 444, 405, 472, 378, 472], "id": 16, "score": 0.9990000128746, "type": "line", "text": "与"}, {"pos": [400, 446, 465, 446, 465, 474, 400, 474], "id": 17, "type": "line", "score": 1, "text": "$f ^ { \\prime } ( x _ { 0 } )$", "sub_type": "formula"}, {"pos": [467, 445, 889, 445, 889, 468, 467, 468], "id": 18, "score": 0.99299997091293, "type": "line", "text": "都容易计算，那么可利用（5-4）式来近似计算"}, {"pos": [892, 444, 922, 444, 922, 471, 892, 471], "id": 19, "type": "line", "score": 1, "text": "$\\Delta y$", "sub_type": "formula"}, {"pos": [921, 445, 1032, 445, 1032, 467, 921, 467], "id": 20, "score": 0.99299997091293, "type": "line", "text": "，利用（5-5）"}, {"pos": [237, 483, 372, 483, 372, 513, 237, 513], "id": 21, "score": 0.9990000128746, "type": "line", "text": "式来近似计算"}, {"pos": [368, 485, 462, 485, 462, 513, 368, 513], "id": 22, "type": "line", "score": 1, "text": "$f ( x _ { 0 } + \\Delta x )$", "sub_type": "formula"}, {"pos": [462, 482, 737, 482, 737, 511, 462, 511], "id": 23, "score": 0.9990000128746, "type": "line", "text": "，或利用（5-6）式来近似计算"}, {"pos": [735, 483, 783, 483, 783, 511, 735, 511], "id": 24, "type": "line", "score": 1, "text": "$f ( x )$", "sub_type": "formula"}, {"pos": [781, 482, 1041, 482, 1041, 511, 781, 511], "id": 25, "score": 0.99299997091293, "type": "line", "text": ".这种近似计算的实质就是"}, {"pos": [238, 526, 260, 526, 260, 547, 238, 547], "id": 26, "score": 0.9990000128746, "type": "line", "text": "用"}, {"pos": [262, 526, 279, 526, 279, 547, 262, 547], "id": 27, "type": "line", "score": 1, "text": "$x$", "sub_type": "formula"}, {"pos": [281, 525, 390, 525, 390, 547, 281, 547], "id": 28, "score": 0.9990000128746, "type": "line", "text": "的线性函数"}, {"pos": [390, 521, 585, 521, 585, 553, 390, 553], "id": 29, "type": "line", "score": 1, "text": "$f ( x _ { 0 } ) + f ^ { \\prime } ( x _ { 0 } ) ( x - x _ { 0 } )$", "sub_type": "formula"}, {"pos": [585, 521, 747, 521, 747, 551, 585, 551], "id": 30, "score": 0.9990000128746, "type": "line", "text": "来近似表达函数"}, {"pos": [743, 521, 793, 521, 793, 551, 743, 551], "id": 31, "type": "line", "score": 1, "text": "$f ( x )$", "sub_type": "formula"}, {"pos": [789, 521, 1039, 521, 1039, 549, 789, 549], "id": 32, "score": 0.99500000476837, "type": "line", "text": ".从导数的几何意义可知，"}, {"pos": [235, 563, 393, 561, 393, 590, 235, 592], "id": 33, "score": 0.9990000128746, "type": "line", "text": "这也就是用曲线"}, {"pos": [393, 562, 465, 562, 465, 590, 393, 590], "id": 34, "type": "line", "score": 1, "text": "$y = f ( x )$", "sub_type": "formula"}, {"pos": [464, 562, 513, 562, 513, 589, 464, 589], "id": 35, "score": 0.9990000128746, "type": "line", "text": "在点"}, {"pos": [510, 561, 618, 561, 618, 590, 510, 590], "id": 36, "type": "line", "score": 1, "text": "$( x _ { 0 } , f ( x _ { 0 } ) )$", "sub_type": "formula"}, {"pos": [618, 559, 1041, 559, 1041, 589, 618, 589], "id": 37, "score": 0.9990000128746, "type": "line", "text": "处的切线来近似代替该曲线（就切点邻近部"}, {"pos": [237, 600, 324, 600, 324, 628, 237, 628], "id": 38, "score": 0.99699997901917, "type": "line", "text": "分来说）."}, {"pos": [283, 640, 472, 640, 472, 662, 283, 662], "id": 39, "score": 0.99599999189377, "type": "line", "text": "例7 有一批半径为"}, {"pos": [475, 638, 525, 638, 525, 663, 475, 663], "id": 40, "type": "line", "score": 1, "text": "$1 c m$", "sub_type": "formula"}, {"pos": [529, 639, 1037, 639, 1037, 662, 529, 662], "id": 41, "score": 0.99599999189377, "type": "line", "text": "的球，为了提高球面的光洁度，要镀上一层铜，厚度定"}, {"pos": [240, 679, 260, 679, 260, 700, 240, 700], "id": 42, "score": 0.98699998855591, "type": "line", "text": "为"}, {"pos": [264, 676, 339, 676, 339, 703, 264, 703], "id": 43, "type": "line", "score": 1, "text": "$0 . 0 1 c m$", "sub_type": "formula"}, {"pos": [346, 677, 773, 677, 773, 699, 346, 699], "id": 44, "score": 0.98699998855591, "type": "line", "text": "估计一下镀每只球需用多少克铜（铜的密度是"}, {"pos": [776, 673, 870, 673, 870, 703, 776, 703], "id": 45, "type": "line", "score": 1, "text": "$8 . 9 g / c m ^ { 3 }$", "sub_type": "formula"}, {"pos": [867, 677, 891, 677, 891, 699, 867, 699], "id": 46, "score": 0.98699998855591, "type": "line", "text": ")？"}, {"pos": [280, 713, 914, 711, 914, 742, 280, 744], "id": 47, "score": 0.99599999189377, "type": "line", "text": "解 先求出镀层的体积，再乘密度就可得到镀每只球需用铜的质量."}, {"pos": [283, 752, 1005, 752, 1005, 781, 283, 781], "id": 48, "score": 0.9990000128746, "type": "line", "text": "因为镀层的体积等于镀铜前、后两个球体体积之差，所以它就是球体体积"}, {"pos": [1005, 752, 1039, 752, 1039, 776, 1005, 776], "id": 49, "type": "line", "score": 1, "text": "$V =$", "sub_type": "formula"}, {"pos": [240, 792, 300, 795, 298, 848, 238, 845], "id": 50, "type": "line", "score": 1, "text": "$\\frac { 4 } { 3 } \\pi R ^ { 3 }$", "sub_type": "formula"}, {"pos": [301, 808, 320, 808, 320, 828, 301, 828], "id": 51, "score": 0.9990000128746, "type": "line", "text": "当"}, {"pos": [323, 806, 343, 806, 343, 830, 323, 830], "id": 52, "type": "line", "score": 1, "text": "$R$", "sub_type": "formula"}, {"pos": [348, 808, 364, 808, 364, 829, 348, 829], "id": 53, "score": 0.9990000128746, "type": "line", "text": "自"}, {"pos": [370, 806, 398, 806, 398, 834, 370, 834], "id": 54, "type": "line", "score": 1, "text": "$R _ { 0 }$", "sub_type": "formula"}, {"pos": [395, 808, 482, 808, 482, 829, 395, 829], "id": 55, "score": 0.98600000143051, "type": "line", "text": "取得增量"}, {"pos": [485, 807, 519, 807, 519, 831, 485, 831], "id": 56, "type": "line", "score": 1, "text": "$\\Delta R$", "sub_type": "formula"}, {"pos": [522, 808, 608, 808, 608, 829, 522, 829], "id": 57, "score": 0.98600000143051, "type": "line", "text": "时的增量"}, {"pos": [611, 806, 646, 806, 646, 831, 611, 831], "id": 58, "type": "line", "score": 1, "text": "$\\Delta V .$", "sub_type": "formula"}, {"pos": [648, 807, 714, 807, 714, 829, 648, 829], "id": 59, "score": 0.98600000143051, "type": "line", "text": ".我们求"}, {"pos": [717, 806, 736, 806, 736, 830, 717, 830], "id": 60, "type": "line", "score": 1, "text": "$A$", "sub_type": "formula"}, {"pos": [737, 808, 759, 808, 759, 829, 737, 829], "id": 61, "score": 0.98600000143051, "type": "line", "text": "对"}, {"pos": [761, 806, 781, 806, 781, 830, 761, 830], "id": 62, "type": "line", "score": 1, "text": "$R$", "sub_type": "formula"}, {"pos": [784, 807, 848, 807, 848, 829, 784, 829], "id": 63, "score": 0.98600000143051, "type": "line", "text": "的导数"}, {"pos": [482, 857, 791, 859, 791, 922, 482, 920], "id": 64, "type": "line", "score": 1, "text": "$$V ^ { \\prime } \\vert _ { R = R _ { 0 } } =$$", "sub_type": "formula"}, {"pos": [242, 929, 372, 929, 372, 957, 242, 957], "id": 65, "score": 0.9990000128746, "type": "line", "text": "由（5-4）式得"}, {"pos": [572, 966, 708, 969, 707, 998, 572, 995], "id": 66, "type": "line", "score": 1, "text": "$$\\Delta V \\approx 4 \\pi R _ { 0 } ^ { 2 } \\Delta R .$$", "sub_type": "formula"}, {"pos": [283, 1006, 311, 1006, 311, 1034, 283, 1034], "id": 67, "score": 0.9990000128746, "type": "line", "text": "将"}, {"pos": [311, 1008, 462, 1008, 462, 1036, 311, 1036], "id": 68, "type": "line", "score": 1, "text": "$R _ { 0 } = 1 , \\Delta R = 0 . 0 1$", "sub_type": "formula"}, {"pos": [460, 1008, 590, 1008, 590, 1036, 460, 1036], "id": 69, "score": 0.99699997901917, "type": "line", "text": "代入上式，得"}, {"pos": [474, 1044, 801, 1047, 801, 1078, 473, 1075], "id": 70, "type": "line", "score": 1, "text": "$$\\Delta V \\approx 4 \\times 3 . 1 4 \\times 1 ^ { 2 } \\times 0 . 0 1 \\approx 0 . 1 3 ( c m ^ { 3 } )$$", "sub_type": "formula"}, {"pos": [242, 1083, 507, 1085, 506, 1115, 242, 1112], "id": 71, "score": 0.9990000128746, "type": "line", "text": "于是镀每只球需用的铜约为"}, {"pos": [546, 1125, 729, 1125, 729, 1153, 546, 1153], "id": 72, "type": "line", "score": 1, "text": "$$0 . 1 3 \\times 8 . 9 \\approx 1 . 1 6 ( g )$$", "sub_type": "formula"}, {"pos": [286, 1163, 513, 1163, 513, 1185, 286, 1185], "id": 73, "score": 0.97399997711182, "type": "line", "text": "例8 利用微分计算sin "}, {"pos": [511, 1163, 574, 1163, 574, 1188, 511, 1188], "id": 74, "type": "line", "score": 1, "text": "$3 0 ^ { \\circ } 3 0 ^ { \\prime }$", "sub_type": "formula"}, {"pos": [576, 1166, 669, 1166, 669, 1186, 576, 1186], "id": 75, "score": 0.97399997711182, "type": "line", "text": "的近似值．"}, {"pos": [283, 1199, 355, 1199, 355, 1227, 283, 1227], "id": 76, "score": 0.9990000128746, "type": "line", "text": "解把"}, {"pos": [354, 1199, 415, 1199, 415, 1225, 354, 1225], "id": 77, "type": "line", "score": 1, "text": "$3 0 ^ { \\circ } 3 0 ^ { \\prime }$", "sub_type": "formula"}, {"pos": [416, 1202, 535, 1202, 535, 1224, 416, 1224], "id": 78, "score": 0.98600000143051, "type": "line", "text": "化为弧度，得"}, {"pos": [560, 1243, 720, 1248, 718, 1297, 558, 1292], "id": 79, "type": "line", "score": 1, "text": "$$3 0 ^ { \\circ } 3 0 ^ { \\prime } = \\frac { \\pi } { 6 } + \\frac { \\pi } { 3 6 0 } .$$", "sub_type": "formula"}, {"pos": [285, 1320, 607, 1323, 607, 1353, 284, 1350], "id": 80, "score": 0.9990000128746, "type": "line", "text": "由于所求的是正弦函数的值，故设"}, {"pos": [604, 1324, 714, 1324, 714, 1353, 604, 1353], "id": 81, "type": "line", "score": 1, "text": "$f ( x ) = \\sin x .$", "sub_type": "formula"}, {"pos": [712, 1327, 776, 1327, 776, 1353, 712, 1353], "id": 82, "score": 0.98400002717972, "type": "line", "text": "此时f＇"}, {"pos": [775, 1327, 860, 1327, 860, 1353, 775, 1353], "id": 83, "type": "line", "score": 1, "text": "$( x ) = \\cos$", "sub_type": "formula"}, {"pos": [860, 1327, 949, 1327, 949, 1355, 860, 1355], "id": 84, "score": 0.99800002574921, "type": "line", "text": "x.如果取"}, {"pos": [949, 1321, 1011, 1321, 1011, 1367, 949, 1367], "id": 85, "type": "line", "score": 1, "text": "$x _ { 0 } = \\frac { \\pi } { 6 }$", "sub_type": "formula"}, {"pos": [1010, 1329, 1044, 1329, 1044, 1357, 1010, 1357], "id": 86, "score": 0.9990000128746, "type": "line", "text": "，那"}, {"pos": [243, 1395, 268, 1395, 268, 1421, 243, 1421], "id": 87, "score": 0.99000000953674, "type": "line", "text": "么"}, {"pos": [263, 1377, 451, 1380, 450, 1441, 263, 1439], "id": 88, "type": "line", "score": 1, "text": "$f ( \\frac { \\pi } { 6 } ) = \\sin \\frac { \\pi } { 6 } = \\frac { 1 } { 2 }$", "sub_type": "formula"}, {"pos": [444, 1396, 469, 1396, 469, 1424, 444, 1424], "id": 89, "score": 0.9879999756813, "type": "line", "text": "与"}, {"pos": [460, 1380, 669, 1380, 669, 1442, 460, 1442], "id": 90, "type": "line", "score": 1, "text": "$f ^ { \\prime } ( \\frac { \\pi } { 6 } ) = \\cos \\frac { \\pi } { 6 } = \\frac { \\sqrt { 3 } } { 2 }$", "sub_type": "formula"}, {"pos": [668, 1399, 844, 1399, 844, 1429, 668, 1429], "id": 91, "score": 0.9990000128746, "type": "line", "text": "都容易计算，并且"}, {"pos": [842, 1395, 921, 1395, 921, 1441, 842, 1441], "id": 92, "type": "line", "score": 1, "text": "$\\Delta x = \\frac { \\pi } { 3 6 0 }$", "sub_type": "formula"}, {"pos": [919, 1403, 1044, 1403, 1044, 1432, 919, 1432], "id": 93, "score": 0.9990000128746, "type": "line", "text": "比较小.应用"}, {"pos": [243, 1450, 373, 1450, 373, 1478, 243, 1478], "id": 94, "score": 0.9990000128746, "type": "line", "text": "（5-5）式便得"}, {"pos": [192, 1528, 225, 1528, 225, 1548, 192, 1548], "id": 95, "score": 0.9990000128746, "type": "line", "text": "114"}], "status": "Success", "height": 1684, "structured": [{"pos": [234, 129, 388, 129, 388, 145, 234, 145], "blocks": [{"pos": [234, 129, 388, 129, 388, 145, 234, 145], "type": "textblock", "id": 1, "content": [0], "text": "第二章 导数与微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [233, 218, 436, 216, 435, 237, 233, 239], "type": "textblock", "id": 2, "content": [1], "text": "这个式子也可以写为", "outline_level": -1, "sub_type": "text"}, {"pos": [477, 252, 1036, 252, 1036, 281, 477, 281], "type": "textblock", "id": 3, "content": [2, 3], "text": "$$\\Delta y=f(x_{0}+\\Delta x)-f(x_{0})\\approx f^{\\prime }(x_{0})\\Delta x\\tag{5-4}$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [234, 291, 263, 291, 263, 319, 234, 319], "type": "textblock", "id": 5, "content": [4], "text": "或", "outline_level": -1, "sub_type": "text"}, {"pos": [496, 329, 1036, 327, 1036, 357, 497, 359], "type": "textblock", "id": 6, "content": [5, 6], "text": "$$f(x_{0}+\\Delta x)\\approx f(x_{0})+f^{\\prime }(x_{0})\\Delta x.\\tag{5-5}$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [280, 367, 868, 367, 868, 396, 280, 396], "type": "textblock", "id": 8, "content": [7, 8, 9, 10, 11], "text": "在（5-5）式中令$x=x_{0}+\\Delta x$，即$\\Delta x=x-x_{0}$，那么（5-5）式可改写为", "outline_level": -1, "sub_type": "text"}, {"pos": [498, 405, 1036, 405, 1036, 436, 498, 436], "type": "textblock", "id": 9, "content": [12, 13], "text": "$$f(x)\\approx f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})\\tag{5-6}$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [234, 444, 1041, 444, 1041, 625, 234, 625], "type": "textblock", "id": 11, "content": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38], "text": "如果$f(x_{0})$与$f^{\\prime }(x_{0})$都容易计算，那么可利用（5-4）式来近似计算$\\Delta y$，利用（5-5）式来近似计算$f(x_{0}+\\Delta x)$，或利用（5-6）式来近似计算$f(x)$.这种近似计算的实质就是用$x$的线性函数$f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})$来近似表达函数$f(x)$.从导数的几何意义可知，这也就是用曲线$y=f(x)$在点$(x_{0},f(x_{0}))$处的切线来近似代替该曲线（就切点邻近部分来说）.", "outline_level": -1, "sub_type": "text"}, {"pos": [237, 639, 1041, 639, 1041, 700, 237, 700], "type": "textblock", "id": 12, "content": [39, 40, 41, 42, 43, 44, 45, 46], "text": "例7 有一批半径为$1cm$的球，为了提高球面的光洁度，要镀上一层铜，厚度定为$0.01cm$ 估计一下镀每只球需用多少克铜（铜的密度是$8.9g/cm^{3}$)？", "outline_level": -1, "sub_type": "text"}, {"pos": [280, 716, 914, 714, 913, 736, 279, 738], "type": "textblock", "id": 13, "content": [47], "text": "解 先求出镀层的体积，再乘密度就可得到镀每只球需用铜的质量.", "outline_level": -1, "sub_type": "text"}, {"pos": [238, 752, 1039, 752, 1039, 848, 238, 848], "type": "textblock", "id": 14, "content": [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63], "text": "因为镀层的体积等于镀铜前、后两个球体体积之差，所以它就是球体体积$V=$ $\\frac {4}{3}\\pi R^{3}$当$R$自 $R_{0}$取得增量$\\Delta R$时的增量$\\Delta V.$.我们求$A$对$R$的导数", "outline_level": -1, "sub_type": "text"}, {"pos": [482, 857, 791, 859, 791, 922, 482, 920], "type": "textblock", "id": 15, "content": [64], "text": "$$V^{\\prime }\\vert _{R=R_{0}}=$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [242, 932, 372, 932, 372, 954, 242, 954], "type": "textblock", "id": 16, "content": [65], "text": "由（5-4）式得", "outline_level": -1, "sub_type": "text"}, {"pos": [572, 966, 708, 969, 707, 998, 572, 995], "type": "textblock", "id": 17, "content": [66], "text": "$$\\Delta V\\approx 4\\pi R_{0}^{2}\\Delta R.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [283, 1006, 590, 1006, 590, 1036, 283, 1036], "type": "textblock", "id": 18, "content": [67, 68, 69], "text": "将$R_{0}=1,\\Delta R=0.01$代入上式，得", "outline_level": -1, "sub_type": "text"}, {"pos": [474, 1044, 801, 1047, 801, 1078, 473, 1075], "type": "textblock", "id": 19, "content": [70], "text": "$$\\Delta V\\approx 4\\times 3.14\\times 1^{2}\\times 0.01\\approx 0.13(cm^{3})$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [241, 1086, 506, 1088, 506, 1110, 242, 1107], "type": "textblock", "id": 20, "content": [71], "text": "于是镀每只球需用的铜约为", "outline_level": -1, "sub_type": "text"}, {"pos": [546, 1125, 729, 1125, 729, 1153, 546, 1153], "type": "textblock", "id": 21, "content": [72], "text": "$$0.13\\times 8.9\\approx 1.16(g)$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [282, 1162, 668, 1164, 669, 1187, 283, 1184], "type": "textblock", "id": 22, "content": [73, 74, 75], "text": "例8 利用微分计算sin $30^{\\circ }30^{\\prime }$的近似值．", "outline_level": -1, "sub_type": "text"}, {"pos": [283, 1198, 539, 1201, 539, 1225, 282, 1221], "type": "textblock", "id": 23, "content": [76, 77, 78], "text": "解把$30^{\\circ }30^{\\prime }$化为弧度，得", "outline_level": -1, "sub_type": "text"}, {"pos": [560, 1243, 720, 1248, 718, 1297, 558, 1292], "type": "textblock", "id": 24, "content": [79], "text": "$$30^{\\circ }30^{\\prime }=\\frac {\\pi }{6}+\\frac {\\pi }{360}.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [243, 1321, 1044, 1321, 1044, 1474, 243, 1474], "type": "textblock", "id": 25, "content": [80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94], "text": "由于所求的是正弦函数的值，故设$f(x)=\\sin x.$此时f＇$(x)=\\cos$x.如果取$x_{0}=\\frac {\\pi }{6}$，那么$f(\\frac {\\pi }{6})=\\sin \\frac {\\pi }{6}=\\frac {1}{2}$与$f^{\\prime }(\\frac {\\pi }{6})=\\cos \\frac {\\pi }{6}=\\frac {\\sqrt {3}}{2}$都容易计算，并且$\\Delta x=\\frac {\\pi }{360}$比较小.应用（5-5）式便得", "outline_level": -1, "sub_type": "text"}, {"pos": [192, 1528, 225, 1528, 225, 1544, 192, 1544], "blocks": [{"pos": [192, 1528, 225, 1528, 225, 1544, 192, 1544], "type": "textblock", "id": 27, "content": [95], "text": "114", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1588.7033691406, "image_id": "", "width": 1190}, {"angle": 0, "page_id": 5, "content": [{"pos": [785, 121, 941, 123, 941, 149, 784, 146], "id": 0, "score": 0.97899997234344, "type": "line", "text": "第五节 函数的微分"}, {"pos": [288, 207, 782, 207, 782, 349, 288, 349], "id": 1, "type": "line", "score": 1, "text": "$$\\sin 3 0 ^ { \\circ } 3 0 ^ { \\prime } = \\sin ( \\frac { \\pi } { 6 } + \\frac { \\pi } { 3 6 0 } ) \\approx \\sin \\frac { \\pi } { 6 } + \\cos \\frac { \\pi } { 6 } \\times \\frac { \\pi } { 3 6 0 } \\\\ = \\frac { 1 } { 2 } + \\frac { \\sqrt { 3 } } { 2 } \\times \\frac { \\pi } { 3 6 0 } \\approx 0 . 5 0 0 0 + 0 . 0 0 7 6 = 0 . 5 0 7 6 .$$", "sub_type": "formula"}, {"pos": [184, 358, 747, 361, 747, 391, 184, 388], "id": 2, "score": 0.99599999189377, "type": "line", "text": "下面我们来推导一些常用的近似公式．为此，在（5-6）式中取"}, {"pos": [742, 363, 794, 363, 794, 391, 742, 391], "id": 3, "type": "line", "score": 1, "text": "$x _ { 0 } = 0$", "sub_type": "formula"}, {"pos": [791, 362, 870, 362, 870, 390, 791, 390], "id": 4, "score": 0.9990000128746, "type": "line", "text": "，于是得"}, {"pos": [439, 398, 635, 398, 635, 429, 439, 429], "id": 5, "type": "line", "score": 1, "text": "$$f ( x ) \\approx f ( 0 ) + f ^ { \\prime } ( 0 ) x .$$", "sub_type": "formula"}, {"pos": [875, 401, 937, 401, 937, 429, 875, 429], "id": 6, "score": 0.9990000128746, "type": "line", "text": "(5-7)"}, {"pos": [183, 435, 862, 439, 862, 469, 182, 465], "id": 7, "score": 0.9990000128746, "type": "line", "text": "应用（5-7）式可以推得以下几个在工程上常用的近似公式（下面都假定"}, {"pos": [860, 441, 896, 441, 896, 467, 860, 467], "id": 8, "type": "line", "score": 1, "text": "$\\vert x \\vert$", "sub_type": "formula"}, {"pos": [891, 441, 941, 441, 941, 467, 891, 467], "id": 9, "score": 0.9990000128746, "type": "line", "text": "是较"}, {"pos": [140, 474, 257, 474, 257, 503, 140, 503], "id": 10, "score": 0.9990000128746, "type": "line", "text": "小的数值）："}, {"pos": [186, 515, 220, 515, 220, 543, 186, 543], "id": 11, "score": 0.99800002574921, "type": "line", "text": "(i)"}, {"pos": [225, 515, 375, 515, 375, 543, 225, 543], "id": 12, "type": "line", "score": 1, "text": "$( 1 + x ) ^ { \\alpha } \\approx 1 + \\alpha x$", "sub_type": "formula"}, {"pos": [370, 515, 464, 515, 464, 544, 370, 544], "id": 13, "type": "line", "score": 1, "text": "$( \\alpha \\in R ) ,$", "sub_type": "formula"}, {"pos": [186, 554, 230, 554, 230, 581, 186, 581], "id": 14, "score": 0.99800002574921, "type": "line", "text": "(ii)"}, {"pos": [229, 557, 260, 557, 260, 577, 229, 577], "id": 15, "score": 0.9990000128746, "type": "line", "text": "sin"}, {"pos": [260, 556, 311, 556, 311, 581, 260, 581], "id": 16, "type": "line", "score": 1, "text": "$x \\approx x$", "sub_type": "formula"}, {"pos": [319, 557, 329, 557, 329, 578, 319, 578], "id": 17, "score": 0.9990000128746, "type": "line", "text": "（"}, {"pos": [329, 558, 345, 558, 345, 578, 329, 578], "id": 18, "type": "line", "score": 1, "text": "$x$", "sub_type": "formula"}, {"pos": [348, 557, 502, 557, 502, 579, 348, 579], "id": 19, "score": 0.9990000128746, "type": "line", "text": "以弧度为单位），"}, {"pos": [186, 592, 270, 592, 270, 618, 186, 618], "id": 20, "score": 0.9990000128746, "type": "line", "text": "(iii)tan"}, {"pos": [268, 594, 317, 594, 317, 618, 268, 618], "id": 21, "type": "line", "score": 1, "text": "$x \\approx x$", "sub_type": "formula"}, {"pos": [326, 596, 336, 596, 336, 616, 326, 616], "id": 22, "score": 0.9990000128746, "type": "line", "text": "（"}, {"pos": [337, 596, 352, 596, 352, 616, 337, 616], "id": 23, "type": "line", "score": 1, "text": "$x$", "sub_type": "formula"}, {"pos": [356, 595, 506, 595, 506, 618, 356, 618], "id": 24, "score": 0.9990000128746, "type": "line", "text": "以弧度为单位），"}, {"pos": [186, 630, 229, 630, 229, 658, 186, 658], "id": 25, "score": 0.9990000128746, "type": "line", "text": "(iv)"}, {"pos": [234, 628, 323, 632, 322, 661, 233, 657], "id": 26, "type": "line", "score": 1, "text": "$e ^ { x } \\approx 1 + x ,$", "sub_type": "formula"}, {"pos": [186, 669, 225, 669, 225, 697, 186, 697], "id": 27, "score": 0.99699997901917, "type": "line", "text": "(v)"}, {"pos": [225, 669, 346, 671, 345, 700, 225, 697], "id": 28, "type": "line", "score": 1, "text": "$\\ln ( 1 + x ) \\approx x .$", "sub_type": "formula"}, {"pos": [184, 707, 622, 707, 622, 737, 184, 737], "id": 29, "score": 0.99400001764297, "type": "line", "text": "证（i）在第一章第九节例7中我们已经知道"}, {"pos": [617, 710, 839, 710, 839, 738, 617, 738], "id": 30, "type": "line", "score": 1, "text": "$( 1 + x ) ^ { \\alpha } - 1 \\sim \\alpha x ( x \\rightarrow 0 )$", "sub_type": "formula"}, {"pos": [837, 710, 939, 710, 939, 738, 837, 738], "id": 31, "score": 0.9990000128746, "type": "line", "text": "，从而得出"}, {"pos": [140, 747, 569, 747, 569, 776, 140, 776], "id": 32, "score": 0.9990000128746, "type": "line", "text": "这个近似公式.在这里，我们利用微分证明.取"}, {"pos": [566, 748, 710, 748, 710, 778, 566, 778], "id": 33, "type": "line", "score": 1, "text": "$f ( x ) = ( 1 + x ) ^ { \\alpha }$", "sub_type": "formula"}, {"pos": [709, 750, 768, 750, 768, 778, 709, 778], "id": 34, "score": 0.9990000128746, "type": "line", "text": "，那么"}, {"pos": [770, 750, 936, 750, 936, 780, 770, 780], "id": 35, "type": "line", "score": 1, "text": "$f ( 0 ) = 1 , f ^ { \\prime } ( 0 ) =$", "sub_type": "formula"}, {"pos": [141, 783, 435, 789, 434, 845, 140, 839], "id": 36, "type": "line", "score": 1, "text": "$\\alpha ( 1 + x ) ^ { \\alpha - 1 } \\vert _ { x = 0 } = \\alpha , 代 入 ( 5 - 7 )$", "sub_type": "formula"}, {"pos": [420, 800, 493, 800, 493, 830, 420, 830], "id": 37, "score": 0.9990000128746, "type": "line", "text": "式便得"}, {"pos": [467, 852, 612, 852, 612, 881, 467, 881], "id": 38, "type": "line", "score": 1, "text": "$$( 1 + x ) ^ { \\alpha } \\approx 1 + \\alpha x .$$", "sub_type": "formula"}, {"pos": [187, 903, 258, 903, 258, 931, 187, 931], "id": 39, "score": 0.9990000128746, "type": "line", "text": "（ii）取"}, {"pos": [255, 903, 365, 903, 365, 932, 255, 932], "id": 40, "type": "line", "score": 1, "text": "$f ( x ) = \\sin x$", "sub_type": "formula"}, {"pos": [362, 904, 418, 904, 418, 931, 362, 931], "id": 41, "score": 0.9990000128746, "type": "line", "text": "，那么"}, {"pos": [414, 895, 814, 895, 814, 944, 414, 944], "id": 42, "type": "line", "score": 1, "text": "$f ( 0 ) = 0 , f ^ { \\prime } ( 0 ) = \\cos x \\vert _ { x = 0 } = 1 , 代 入 ( 5 - 7 ) :$", "sub_type": "formula"}, {"pos": [806, 904, 877, 904, 877, 932, 806, 932], "id": 43, "score": 0.9990000128746, "type": "line", "text": "式便得"}, {"pos": [500, 957, 581, 959, 580, 980, 499, 978], "id": 44, "type": "line", "score": 1, "text": "$$\\sin x \\approx x .$$", "sub_type": "formula"}, {"pos": [184, 990, 663, 990, 663, 1020, 184, 1020], "id": 45, "score": 0.99699997901917, "type": "line", "text": "其他几个近似公式可用类似方法证明，这里从略了."}, {"pos": [188, 1037, 291, 1037, 291, 1058, 188, 1058], "id": 46, "score": 0.97100001573563, "type": "line", "text": "例9 计算"}, {"pos": [291, 1033, 349, 1033, 349, 1061, 291, 1061], "id": 47, "type": "line", "score": 1, "text": "$\\sqrt { 1 . 0 5 }$", "sub_type": "formula"}, {"pos": [350, 1038, 442, 1038, 442, 1058, 350, 1058], "id": 48, "score": 0.97100001573563, "type": "line", "text": "的近似值．"}, {"pos": [184, 1079, 214, 1079, 214, 1108, 184, 1108], "id": 49, "score": 0.9990000128746, "type": "line", "text": "解"}, {"pos": [459, 1077, 623, 1077, 623, 1108, 459, 1108], "id": 50, "type": "line", "score": 1, "text": "$\\sqrt { 1 . 0 5 } = \\sqrt { 1 + 0 . 0 5 }$", "sub_type": "formula"}, {"pos": [141, 1136, 191, 1136, 191, 1163, 141, 1163], "id": 51, "score": 0.9990000128746, "type": "line", "text": "这里"}, {"pos": [189, 1136, 263, 1136, 263, 1163, 189, 1163], "id": 52, "type": "line", "score": 1, "text": "$x = 0 . 0 5$", "sub_type": "formula"}, {"pos": [262, 1136, 504, 1136, 504, 1165, 262, 1165], "id": 53, "score": 0.9990000128746, "type": "line", "text": "，其值较小，利用近似公式"}, {"pos": [495, 1136, 533, 1136, 533, 1163, 495, 1163], "id": 54, "score": 0.99699997901917, "type": "line", "text": "(i)"}, {"pos": [525, 1123, 599, 1123, 599, 1176, 525, 1176], "id": 55, "type": "line", "score": 1, "text": "$( \\alpha = \\frac { 1 } { 2 }$", "sub_type": "formula"}, {"pos": [595, 1136, 732, 1136, 732, 1164, 595, 1164], "id": 56, "score": 0.9990000128746, "type": "line", "text": "的情形），便得"}, {"pos": [416, 1201, 671, 1201, 671, 1253, 416, 1253], "id": 57, "type": "line", "score": 1, "text": "$$\\sqrt { 1 . 0 5 } \\approx 1 + \\frac { 1 } { 2 } \\times 0 . 0 5 = 1 . 0 2 5 .$$", "sub_type": "formula"}, {"pos": [186, 1275, 377, 1275, 377, 1302, 186, 1302], "id": 58, "score": 0.9990000128746, "type": "line", "text": "如果直接开方，可得"}, {"pos": [464, 1322, 625, 1322, 625, 1353, 464, 1353], "id": 59, "type": "line", "score": 1, "text": "$$\\sqrt { 1 . 0 5 } = 1 . 0 2 4 7 0 .$$", "sub_type": "formula"}, {"pos": [142, 1377, 581, 1377, 581, 1407, 142, 1407], "id": 60, "score": 0.9990000128746, "type": "line", "text": "将两个结果比较一下，可以看出，用1.025作为"}, {"pos": [576, 1375, 640, 1375, 640, 1408, 576, 1408], "id": 61, "type": "line", "score": 1, "text": "$\\sqrt { 1 . 0 5 }$", "sub_type": "formula"}, {"pos": [635, 1378, 941, 1378, 941, 1408, 635, 1408], "id": 62, "score": 0.9990000128746, "type": "line", "text": "的近似值，其误差不超过0.001，"}, {"pos": [141, 1416, 944, 1416, 944, 1446, 141, 1446], "id": 63, "score": 0.99800002574921, "type": "line", "text": "这样的近似值在一般应用上已够精确了.如果开方次数较高，就更能体现出用微分进"}, {"pos": [141, 1454, 349, 1454, 349, 1482, 141, 1482], "id": 64, "score": 0.99699997901917, "type": "line", "text": "行近似计算的优越性."}, {"pos": [959, 1533, 992, 1533, 992, 1552, 959, 1552], "id": 65, "score": 0.9990000128746, "type": "line", "text": "115"}], "status": "Success", "height": 1684, "structured": [{"pos": [784, 125, 940, 127, 941, 144, 784, 141], "blocks": [{"pos": [784, 125, 940, 127, 941, 144, 784, 141], "type": "textblock", "id": 0, "content": [0], "text": "第五节 函数的微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [291, 209, 776, 212, 776, 347, 291, 347], "type": "textblock", "id": 2, "content": [1], "text": "$$\\sin 30^{\\circ }30^{\\prime }=\\sin (\\frac {\\pi }{6}+\\frac {\\pi }{360})\\approx \\sin \\frac {\\pi }{6}+\\cos \\frac {\\pi }{6}\\times \\frac {\\pi }{360}\\\\ =\\frac {1}{2}+\\frac {\\sqrt {3}}{2}\\times \\frac {\\pi }{360}\\approx 0.5000+0.0076=0.5076.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [183, 360, 870, 360, 870, 391, 183, 391], "type": "textblock", "id": 4, "content": [2, 3, 4], "text": "下面我们来推导一些常用的近似公式．为此，在（5-6）式中取$x_{0}=0$，于是得", "outline_level": -1, "sub_type": "text"}, {"pos": [439, 398, 937, 398, 937, 429, 439, 429], "type": "textblock", "id": 5, "content": [5, 6], "text": "$$f(x)\\approx f(0)+f^{\\prime }(0)x.\\tag{5-7}$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [140, 437, 941, 437, 941, 499, 140, 499], "type": "textblock", "id": 7, "content": [7, 8, 9, 10], "text": "应用（5-7）式可以推得以下几个在工程上常用的近似公式（下面都假定$\\vert x\\vert$是较小的数值）：", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 515, 464, 515, 464, 544, 186, 544], "type": "textblock", "id": 8, "content": [11, 12, 13], "text": "(i)$(1+x)^{\\alpha }\\approx 1+\\alpha x$ $(\\alpha \\in R),$", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 556, 502, 556, 502, 581, 186, 581], "type": "textblock", "id": 9, "content": [14, 15, 16, 17, 18, 19], "text": "(ii)sin$x\\approx x$ （$x$以弧度为单位），", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 593, 510, 593, 510, 618, 186, 618], "type": "textblock", "id": 10, "content": [20, 21, 22, 23, 24], "text": "(iii)tan$x\\approx x$ （$x$以弧度为单位），", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 625, 323, 632, 321, 661, 185, 654], "type": "textblock", "id": 11, "content": [25, 26], "text": "(iv)$e^{x}\\approx 1+x,$", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 669, 346, 669, 346, 700, 186, 700], "type": "textblock", "id": 12, "content": [27, 28], "text": "(v)$\\ln (1+x)\\approx x.$", "outline_level": -1, "sub_type": "text"}, {"pos": [140, 710, 939, 710, 939, 845, 140, 845], "type": "textblock", "id": 13, "content": [29, 30, 31, 32, 33, 34, 35, 36, 37], "text": "证（i）在第一章第九节例7中我们已经知道$(1+x)^{\\alpha }-1\\sim \\alpha x(x\\rightarrow 0)$，从而得出这个近似公式.在这里，我们利用微分证明.取$f(x)=(1+x)^{\\alpha }$，那么$f(0)=1,f^{\\prime }(0)=$ $\\alpha (1+x)^{\\alpha -1}\\vert _{x=0}=\\alpha ,代入(5-7)$式便得", "outline_level": -1, "sub_type": "text"}, {"pos": [467, 852, 612, 852, 612, 881, 467, 881], "type": "textblock", "id": 14, "content": [38], "text": "$$(1+x)^{\\alpha }\\approx 1+\\alpha x.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [187, 895, 877, 895, 877, 944, 187, 944], "type": "textblock", "id": 15, "content": [39, 40, 41, 42, 43], "text": "（ii）取$f(x)=\\sin x$，那么$f(0)=0,f^{\\prime }(0)=\\cos x\\vert _{x=0}=1,代入(5-7):$式便得", "outline_level": -1, "sub_type": "text"}, {"pos": [500, 957, 581, 959, 580, 980, 499, 978], "type": "textblock", "id": 16, "content": [44], "text": "$$\\sin x\\approx x.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [184, 992, 663, 992, 663, 1014, 184, 1014], "type": "textblock", "id": 17, "content": [45], "text": "其他几个近似公式可用类似方法证明，这里从略了.", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 1033, 442, 1033, 442, 1058, 186, 1058], "type": "textblock", "id": 18, "content": [46, 47, 48], "text": "例9 计算$\\sqrt {1.05}$的近似值．", "outline_level": -1, "sub_type": "text"}, {"pos": [184, 1077, 623, 1077, 623, 1108, 184, 1108], "type": "textblock", "id": 19, "content": [49, 50], "text": "解                       $\\sqrt {1.05}=\\sqrt {1+0.05}$", "outline_level": -1, "sub_type": "text"}, {"pos": [141, 1123, 732, 1123, 732, 1176, 141, 1176], "type": "textblock", "id": 21, "content": [51, 52, 53, 54, 55, 56], "text": "这里$x=0.05$，其值较小，利用近似公式(i)$(\\alpha =\\frac {1}{2}$的情形），便得", "outline_level": -1, "sub_type": "text"}, {"pos": [416, 1201, 671, 1201, 671, 1253, 416, 1253], "type": "textblock", "id": 22, "content": [57], "text": "$$\\sqrt {1.05}\\approx 1+\\frac {1}{2}\\times 0.05=1.025.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [186, 1277, 377, 1277, 377, 1298, 186, 1298], "type": "textblock", "id": 23, "content": [58], "text": "如果直接开方，可得", "outline_level": -1, "sub_type": "text"}, {"pos": [464, 1322, 625, 1322, 625, 1353, 464, 1353], "type": "textblock", "id": 24, "content": [59], "text": "$$\\sqrt {1.05}=1.02470.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [141, 1375, 944, 1375, 944, 1478, 141, 1478], "type": "textblock", "id": 25, "content": [60, 61, 62, 63, 64], "text": "将两个结果比较一下，可以看出，用1.025作为$\\sqrt {1.05}$的近似值，其误差不超过0.001，这样的近似值在一般应用上已够精确了.如果开方次数较高，就更能体现出用微分进行近似计算的优越性.", "outline_level": -1, "sub_type": "text"}, {"pos": [959, 1534, 992, 1534, 992, 1550, 959, 1550], "blocks": [{"pos": [959, 1534, 992, 1534, 992, 1550, 959, 1550], "type": "textblock", "id": 26, "content": [65], "text": "115", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1400.5153808594, "image_id": "", "width": 1190}, {"angle": 0, "page_id": 6, "content": [{"pos": [227, 130, 383, 130, 383, 155, 227, 155], "id": 0, "score": 0.97500002384186, "type": "line", "text": "第二章 导数与微分"}, {"pos": [258, 235, 390, 235, 390, 265, 258, 265], "id": 1, "score": 0.92699998617172, "type": "line", "text": "＂2．误差估计"}, {"pos": [270, 290, 1033, 288, 1033, 319, 270, 321], "id": 2, "score": 0.9990000128746, "type": "line", "text": "在生产实践中，经常要测量各种数据.但是有的数据不易直接测量，这时我们就通"}, {"pos": [227, 329, 1034, 327, 1034, 358, 227, 360], "id": 3, "score": 0.99800002574921, "type": "line", "text": "过测量其他有关数据后，根据某种公式算出所要的数据.例如，要计算圆钢的截面积"}, {"pos": [228, 387, 560, 387, 560, 408, 228, 408], "id": 4, "score": 0.9990000128746, "type": "line", "text": "A，可先用卡尺测量圆钢截面的直径"}, {"pos": [563, 386, 582, 386, 582, 409, 563, 409], "id": 5, "type": "line", "score": 1, "text": "$D$", "sub_type": "formula"}, {"pos": [583, 386, 722, 386, 722, 408, 583, 408], "id": 6, "score": 0.9990000128746, "type": "line", "text": "，然后根据公式"}, {"pos": [725, 377, 801, 377, 801, 423, 725, 423], "id": 7, "type": "line", "score": 1, "text": "$A = \\frac { \\pi } { 4 } D ^ { 2 }$", "sub_type": "formula"}, {"pos": [799, 383, 873, 383, 873, 411, 799, 411], "id": 8, "score": 0.9990000128746, "type": "line", "text": "算出A."}, {"pos": [271, 438, 1034, 436, 1034, 467, 271, 469], "id": 9, "score": 0.9990000128746, "type": "line", "text": "由于测量仪器的精度、测量的条件和测量的方法等各种因素的影响，测得的数据"}, {"pos": [229, 475, 1034, 475, 1034, 507, 229, 507], "id": 10, "score": 0.9990000128746, "type": "line", "text": "往往带有误差，而根据带有误差的数据计算所得的结果也会有误差，我们把它叫做间"}, {"pos": [229, 516, 349, 516, 349, 549, 229, 549], "id": 11, "score": 0.99800002574921, "type": "line", "text": "接测量误差"}, {"pos": [273, 554, 720, 554, 720, 584, 273, 584], "id": 12, "score": 0.99599999189377, "type": "line", "text": "下面就讨论怎样利用微分来估计间接测量误差."}, {"pos": [273, 592, 602, 592, 602, 623, 273, 623], "id": 13, "score": 0.99500000476837, "type": "line", "text": "先说明绝对误差、相对误差的概念."}, {"pos": [276, 633, 658, 633, 658, 656, 276, 656], "id": 14, "score": 0.9990000128746, "type": "line", "text": "如果某个量的精确值为A，它的近似值为"}, {"pos": [661, 634, 678, 634, 678, 656, 661, 656], "id": 15, "type": "line", "score": 1, "text": "$a$", "sub_type": "formula"}, {"pos": [678, 634, 730, 634, 730, 655, 678, 655], "id": 16, "score": 0.9990000128746, "type": "line", "text": "，那么"}, {"pos": [733, 628, 797, 631, 795, 662, 731, 659], "id": 17, "type": "line", "score": 1, "text": "$\\vert A - a \\vert$", "sub_type": "formula"}, {"pos": [794, 633, 838, 633, 838, 655, 794, 655], "id": 18, "score": 0.99699997901917, "type": "line", "text": "叫做"}, {"pos": [841, 634, 858, 634, 858, 655, 841, 655], "id": 19, "type": "line", "score": 1, "text": "$a$", "sub_type": "formula"}, {"pos": [863, 634, 1030, 634, 1030, 658, 863, 658], "id": 20, "score": 0.99699997901917, "type": "line", "text": "的绝对误差，而绝"}, {"pos": [232, 689, 318, 689, 318, 710, 232, 710], "id": 21, "score": 0.9879999756813, "type": "line", "text": "对误差与"}, {"pos": [321, 686, 355, 686, 355, 713, 321, 713], "id": 22, "type": "line", "score": 1, "text": "$\\vert a \\vert$", "sub_type": "formula"}, {"pos": [357, 689, 419, 689, 419, 709, 357, 709], "id": 23, "score": 0.9879999756813, "type": "line", "text": "的比值"}, {"pos": [423, 668, 483, 668, 483, 730, 423, 730], "id": 24, "type": "line", "score": 1, "text": "$\\frac { \\vert A - a \\vert } { \\vert a \\vert }$", "sub_type": "formula"}, {"pos": [484, 688, 526, 688, 526, 709, 484, 709], "id": 25, "score": 0.95800000429153, "type": "line", "text": "叫做"}, {"pos": [528, 690, 545, 690, 545, 710, 528, 710], "id": 26, "type": "line", "score": 1, "text": "$a$", "sub_type": "formula"}, {"pos": [549, 689, 664, 689, 664, 715, 549, 715], "id": 27, "score": 0.95800000429153, "type": "line", "text": "的相对误差."}, {"pos": [273, 740, 1036, 740, 1036, 770, 273, 770], "id": 28, "score": 0.9990000128746, "type": "line", "text": "在实际工作中，某个量的精确值往往是无法知道的，于是绝对误差和相对误差也"}, {"pos": [230, 778, 1036, 778, 1036, 807, 230, 807], "id": 29, "score": 0.99800002574921, "type": "line", "text": "就无法求得.但是根据测量仪器的精度等因素，有时能够确定误差在某一个范围内.如"}, {"pos": [233, 821, 634, 821, 634, 842, 233, 842], "id": 30, "score": 0.9990000128746, "type": "line", "text": "果某个量的精确值是A，测得它的近似值是"}, {"pos": [637, 822, 653, 822, 653, 843, 637, 843], "id": 31, "type": "line", "score": 1, "text": "$a$", "sub_type": "formula"}, {"pos": [654, 821, 881, 821, 881, 843, 654, 843], "id": 32, "score": 0.9990000128746, "type": "line", "text": "，又知道它的误差不超过"}, {"pos": [883, 821, 909, 821, 909, 849, 883, 849], "id": 33, "type": "line", "score": 1, "text": "$δ _ { A }$", "sub_type": "formula"}, {"pos": [906, 819, 941, 819, 941, 847, 906, 847], "id": 34, "score": 0.9990000128746, "type": "line", "text": "，即"}, {"pos": [579, 857, 686, 857, 686, 886, 579, 886], "id": 35, "type": "line", "score": 1, "text": "$$\\vert A - a \\vert \\leq \\delta _ { A } ,$$", "sub_type": "formula"}, {"pos": [232, 916, 281, 916, 281, 944, 232, 944], "id": 36, "score": 0.9990000128746, "type": "line", "text": "那么"}, {"pos": [279, 915, 308, 915, 308, 945, 279, 945], "id": 37, "type": "line", "score": 1, "text": "$δ _ { A }$", "sub_type": "formula"}, {"pos": [303, 918, 389, 918, 389, 939, 303, 939], "id": 38, "score": 0.9990000128746, "type": "line", "text": "叫做测量"}, {"pos": [392, 918, 410, 918, 410, 941, 392, 941], "id": 39, "type": "line", "score": 1, "text": "$A$", "sub_type": "formula"}, {"pos": [414, 919, 576, 919, 576, 943, 414, 943], "id": 40, "score": 0.9990000128746, "type": "line", "text": "的绝对误差限，而"}, {"pos": [577, 901, 618, 901, 618, 960, 577, 960], "id": 41, "type": "line", "score": 1, "text": "$\\frac { \\delta _ { A } } { \\vert a \\vert }$", "sub_type": "formula"}, {"pos": [614, 919, 700, 919, 700, 940, 614, 940], "id": 42, "score": 0.97500002384186, "type": "line", "text": "叫做测量"}, {"pos": [702, 919, 721, 919, 721, 942, 702, 942], "id": 43, "type": "line", "score": 1, "text": "$A$", "sub_type": "formula"}, {"pos": [724, 920, 862, 920, 862, 947, 724, 947], "id": 44, "score": 0.97500002384186, "type": "line", "text": "的相对误差限."}, {"pos": [275, 970, 602, 970, 602, 1000, 275, 1000], "id": 45, "score": 0.99500000476837, "type": "line", "text": "例10 设测得圆钢截面的直径"}, {"pos": [604, 974, 707, 974, 707, 1000, 604, 1000], "id": 46, "type": "line", "score": 1, "text": "$D = 6 0 . 0 3$", "sub_type": "formula"}, {"pos": [711, 976, 803, 976, 803, 998, 711, 998], "id": 47, "score": 0.9990000128746, "type": "line", "text": "mm，测量"}, {"pos": [810, 975, 830, 975, 830, 999, 810, 999], "id": 48, "type": "line", "score": 1, "text": "$D$", "sub_type": "formula"}, {"pos": [837, 976, 982, 976, 982, 999, 837, 999], "id": 49, "score": 0.9990000128746, "type": "line", "text": "的绝对误差限"}, {"pos": [988, 977, 1034, 977, 1034, 1003, 988, 1003], "id": 50, "type": "line", "score": 1, "text": "$δ _ { D } =$", "sub_type": "formula"}, {"pos": [234, 1010, 316, 1010, 316, 1036, 234, 1036], "id": 51, "type": "line", "score": 1, "text": "$0 . 0 5 m m$", "sub_type": "formula"}, {"pos": [313, 1012, 407, 1012, 407, 1034, 313, 1034], "id": 52, "score": 0.9990000128746, "type": "line", "text": "．利用公式"}, {"pos": [594, 1059, 673, 1059, 673, 1103, 594, 1103], "id": 53, "type": "line", "score": 1, "text": "$$A = \\frac { \\pi } { 4 } D ^ { 2 }$$", "sub_type": "formula"}, {"pos": [232, 1116, 625, 1119, 625, 1149, 232, 1146], "id": 54, "score": 0.9990000128746, "type": "line", "text": "计算圆钢的截面积时，试估计面积的误差."}, {"pos": [278, 1159, 475, 1159, 475, 1180, 278, 1180], "id": 55, "score": 0.99199998378754, "type": "line", "text": "解 如果我们把测量"}, {"pos": [478, 1159, 499, 1159, 499, 1183, 478, 1183], "id": 56, "type": "line", "score": 1, "text": "$D$", "sub_type": "formula"}, {"pos": [502, 1161, 768, 1161, 768, 1184, 502, 1184], "id": 57, "score": 0.99199998378754, "type": "line", "text": "时所产生的误差当作自变量"}, {"pos": [771, 1163, 793, 1163, 793, 1186, 771, 1186], "id": 58, "type": "line", "score": 1, "text": "$D$", "sub_type": "formula"}, {"pos": [797, 1163, 862, 1163, 862, 1185, 797, 1185], "id": 59, "score": 0.99199998378754, "type": "line", "text": "的增量"}, {"pos": [866, 1162, 900, 1162, 900, 1187, 866, 1187], "id": 60, "type": "line", "score": 1, "text": "$\\Delta D$", "sub_type": "formula"}, {"pos": [901, 1164, 1034, 1164, 1034, 1186, 901, 1186], "id": 61, "score": 0.99199998378754, "type": "line", "text": "，那么，利用公"}, {"pos": [232, 1209, 261, 1209, 261, 1237, 232, 1237], "id": 62, "score": 0.9990000128746, "type": "line", "text": "式"}, {"pos": [260, 1202, 337, 1202, 337, 1248, 260, 1248], "id": 63, "type": "line", "score": 1, "text": "$A = \\frac { \\pi } { 4 } D ^ { 2 }$", "sub_type": "formula"}, {"pos": [336, 1212, 402, 1212, 402, 1234, 336, 1234], "id": 64, "score": 0.9879999756813, "type": "line", "text": "来计算"}, {"pos": [404, 1212, 422, 1212, 422, 1236, 404, 1236], "id": 65, "type": "line", "score": 1, "text": "$A$", "sub_type": "formula"}, {"pos": [426, 1215, 798, 1215, 798, 1238, 426, 1238], "id": 66, "score": 0.9879999756813, "type": "line", "text": "时所产生的误差就是函数A的对应增量"}, {"pos": [801, 1217, 836, 1217, 836, 1240, 801, 1240], "id": 67, "type": "line", "score": 1, "text": "$\\Delta A .$", "sub_type": "formula"}, {"pos": [830, 1218, 858, 1218, 858, 1239, 830, 1239], "id": 68, "score": 0.9879999756813, "type": "line", "text": ".当"}, {"pos": [863, 1215, 911, 1215, 911, 1243, 863, 1243], "id": 69, "type": "line", "score": 1, "text": "$\\vert \\Delta D \\vert$", "sub_type": "formula"}, {"pos": [911, 1215, 1036, 1215, 1036, 1245, 911, 1245], "id": 70, "score": 0.9990000128746, "type": "line", "text": "很小时，可以"}, {"pos": [235, 1266, 510, 1266, 510, 1289, 235, 1289], "id": 71, "score": 0.98299998044968, "type": "line", "text": "利用微分dA近似地代替增量"}, {"pos": [513, 1267, 545, 1267, 545, 1291, 513, 1291], "id": 72, "type": "line", "score": 1, "text": "$\\Delta A$", "sub_type": "formula"}, {"pos": [545, 1270, 574, 1270, 574, 1291, 545, 1291], "id": 73, "score": 0.98299998044968, "type": "line", "text": "，即"}, {"pos": [495, 1313, 770, 1317, 769, 1362, 495, 1359], "id": 74, "type": "line", "score": 1, "text": "$$\\Delta A \\approx d A = A ^ { \\prime } \\cdot \\Delta D = \\frac { \\pi } { 2 } D \\cdot \\Delta D .$$", "sub_type": "formula"}, {"pos": [236, 1376, 278, 1376, 278, 1397, 236, 1397], "id": 75, "score": 0.9990000128746, "type": "line", "text": "由于"}, {"pos": [281, 1378, 301, 1378, 301, 1399, 281, 1399], "id": 76, "type": "line", "score": 1, "text": "$D$", "sub_type": "formula"}, {"pos": [305, 1377, 458, 1377, 458, 1400, 305, 1400], "id": 77, "score": 0.9990000128746, "type": "line", "text": "的绝对误差限为"}, {"pos": [460, 1378, 585, 1378, 585, 1406, 460, 1406], "id": 78, "type": "line", "score": 1, "text": "$δ _ { D } = 0 . 0 5 \\mathrm { \\sim m m }$", "sub_type": "formula"}, {"pos": [579, 1378, 636, 1378, 636, 1406, 579, 1406], "id": 79, "score": 0.9990000128746, "type": "line", "text": "，所以"}, {"pos": [554, 1415, 709, 1418, 709, 1449, 554, 1446], "id": 80, "type": "line", "score": 1, "text": "$\\vert \\Delta D \\vert \\leq \\delta _ { D } = 0 . 0 5$", "sub_type": "formula"}, {"pos": [706, 1432, 719, 1432, 719, 1449, 706, 1449], "id": 81, "score": 0.83200001716614, "type": "line", "text": ","}, {"pos": [234, 1452, 261, 1452, 261, 1480, 234, 1480], "id": 82, "score": 0.9990000128746, "type": "line", "text": "而"}, {"pos": [186, 1529, 219, 1529, 219, 1549, 186, 1549], "id": 83, "score": 0.9990000128746, "type": "line", "text": "116"}], "status": "Success", "height": 1684, "structured": [{"pos": [227, 133, 383, 133, 383, 148, 227, 148], "blocks": [{"pos": [227, 133, 383, 133, 383, 148, 227, 148], "type": "textblock", "id": 1, "content": [0], "text": "第二章 导数与微分", "outline_level": -1, "sub_type": "text_title"}], "type": "header"}, {"pos": [258, 238, 390, 238, 390, 259, 258, 259], "type": "textblock", "id": 2, "content": [1], "text": "＂2．误差估计", "outline_level": -1, "sub_type": "text_title"}, {"pos": [226, 291, 1034, 291, 1034, 423, 226, 423], "type": "textblock", "id": 3, "content": [2, 3, 4, 5, 6, 7, 8], "text": "在生产实践中，经常要测量各种数据.但是有的数据不易直接测量，这时我们就通过测量其他有关数据后，根据某种公式算出所要的数据.例如，要计算圆钢的截面积A，可先用卡尺测量圆钢截面的直径$D$，然后根据公式$A=\\frac {\\pi }{4}D^{2}$算出A.", "outline_level": -1, "sub_type": "text"}, {"pos": [229, 439, 1034, 439, 1034, 544, 229, 544], "type": "textblock", "id": 4, "content": [9, 10, 11], "text": "由于测量仪器的精度、测量的条件和测量的方法等各种因素的影响，测得的数据往往带有误差，而根据带有误差的数据计算所得的结果也会有误差，我们把它叫做间接测量误差", "outline_level": -1, "sub_type": "text"}, {"pos": [273, 557, 720, 557, 720, 578, 273, 578], "type": "textblock", "id": 5, "content": [12], "text": "下面就讨论怎样利用微分来估计间接测量误差.", "outline_level": -1, "sub_type": "text"}, {"pos": [273, 596, 602, 596, 602, 617, 273, 617], "type": "textblock", "id": 6, "content": [13], "text": "先说明绝对误差、相对误差的概念.", "outline_level": -1, "sub_type": "text"}, {"pos": [230, 628, 1035, 628, 1035, 730, 230, 730], "type": "textblock", "id": 7, "content": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "text": "如果某个量的精确值为A，它的近似值为$a$，那么$\\vert A-a\\vert$叫做$a$的绝对误差，而绝对误差与$\\vert a\\vert$的比值$\\frac {\\vert A-a\\vert }{\\vert a\\vert }$叫做$a$的相对误差.", "outline_level": -1, "sub_type": "text"}, {"pos": [230, 743, 1036, 743, 1036, 849, 230, 849], "type": "textblock", "id": 8, "content": [28, 29, 30, 31, 32, 33, 34], "text": "在实际工作中，某个量的精确值往往是无法知道的，于是绝对误差和相对误差也就无法求得.但是根据测量仪器的精度等因素，有时能够确定误差在某一个范围内.如果某个量的精确值是A，测得它的近似值是$a$，又知道它的误差不超过$δ_{A}$，即", "outline_level": -1, "sub_type": "text"}, {"pos": [579, 857, 686, 857, 686, 886, 579, 886], "type": "textblock", "id": 9, "content": [35], "text": "$$\\vert A-a\\vert \\leq \\delta _{A},$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [232, 901, 862, 901, 862, 960, 232, 960], "type": "textblock", "id": 10, "content": [36, 37, 38, 39, 40, 41, 42, 43, 44], "text": "那么$δ_{A}$叫做测量$A$的绝对误差限，而$\\frac {\\delta _{A}}{\\vert a\\vert }$叫做测量$A$的相对误差限.", "outline_level": -1, "sub_type": "text"}, {"pos": [234, 974, 1034, 974, 1034, 1034, 234, 1034], "type": "textblock", "id": 11, "content": [45, 46, 47, 48, 49, 50, 51, 52], "text": "例10 设测得圆钢截面的直径$D=60.03$mm，测量 $D$ 的绝对误差限 $δ_{D}=$ $0.05mm$．利用公式", "outline_level": -1, "sub_type": "text"}, {"pos": [594, 1059, 673, 1059, 673, 1103, 594, 1103], "type": "textblock", "id": 12, "content": [53], "text": "$$A=\\frac {\\pi }{4}D^{2}$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [231, 1119, 624, 1122, 625, 1144, 232, 1141], "type": "textblock", "id": 13, "content": [54], "text": "计算圆钢的截面积时，试估计面积的误差.", "outline_level": -1, "sub_type": "text"}, {"pos": [232, 1157, 1038, 1157, 1038, 1291, 232, 1291], "type": "textblock", "id": 14, "content": [55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73], "text": "解 如果我们把测量$D$时所产生的误差当作自变量$D$的增量$\\Delta D$，那么，利用公式$A=\\frac {\\pi }{4}D^{2}$来计算$A$时所产生的误差就是函数A的对应增量$\\Delta A.$.当$\\vert \\Delta D\\vert$很小时，可以利用微分dA近似地代替增量$\\Delta A$，即", "outline_level": -1, "sub_type": "text"}, {"pos": [495, 1313, 770, 1317, 769, 1362, 495, 1359], "type": "textblock", "id": 15, "content": [74], "text": "$$\\Delta A\\approx dA=A^{\\prime }\\cdot \\Delta D=\\frac {\\pi }{2}D\\cdot \\Delta D.$$", "outline_level": -1, "sub_type": "formula"}, {"pos": [234, 1375, 636, 1375, 636, 1406, 234, 1406], "type": "textblock", "id": 16, "content": [75, 76, 77, 78, 79], "text": "由于$D$的绝对误差限为$δ_{D}=0.05\\mathrm {\\sim mm}$，所以", "outline_level": -1, "sub_type": "text"}, {"pos": [554, 1415, 719, 1418, 718, 1449, 553, 1446], "type": "textblock", "id": 17, "content": [80, 81], "text": "$\\vert \\Delta D\\vert \\leq \\delta _{D}=0.05$,", "outline_level": -1, "sub_type": "text"}, {"pos": [234, 1452, 261, 1452, 261, 1480, 234, 1480], "type": "textblock", "id": 18, "content": [82], "text": "而", "outline_level": -1, "sub_type": "text"}, {"pos": [186, 1531, 219, 1531, 219, 1547, 186, 1547], "blocks": [{"pos": [186, 1531, 219, 1531, 219, 1547, 186, 1547], "type": "textblock", "id": 19, "content": [83], "text": "116", "outline_level": -1, "sub_type": "text"}], "type": "footer"}], "durations": 1387.1525878906, "image_id": "", "width": 1190}], "valid_page_number": 6, "total_page_number": 6, "total_count": 6, "detail": [{"paragraph_id": 1, "page_id": 1, "tags": [], "outline_level": -1, "text": "**第五节** 函数的微分", "type": "paragraph", "position": [786, 114, 942, 114, 942, 131, 786, 131], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 1, "tags": [], "outline_level": -1, "text": "对照，列表于下：", "type": "paragraph", "position": [143, 210, 299, 207, 299, 230, 142, 232], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 1, "content": 0, "outline_level": -1, "text": "\n| 导数公式 | 微分公式 |\n| --- | --- |\n| $(x^{\\mu })^{\\prime }=\\mu x^{\\mu -1}(\\mu$是任意常数） | $d(x^{\\mu })=\\mu x^{\\mu -1}dx(\\mu$是任意常数） |\n| $(\\sin x)^{\\prime }=\\cos x$ | $d(\\sin x)=\\cos xdx$ |\n| $(\\cos x)^{\\prime }=-\\sin x$ | $d(\\cos x)=-\\sin xdx$ |\n| $(\\tan x)^{\\prime }=\\sec ^{2}x$ | $d(\\tan x)=\\sec ^{2}xdx$ |\n| $(\\cot x)^{\\prime }=-\\csc ^{2}x$ | $d(\\cot x)=-\\csc ^{2}xdx$ |\n| $(\\sec x)^{\\prime }=\\sec x\\tan x$ | $d(\\sec x)=\\sec x\\tan xdx$ |\n| $(\\csc x)^{\\prime }=-\\csc x\\cot x$ | $d(\\csc x)=-\\csc x\\cot xdx$ |\n| $(a^{x})^{\\prime }=a^{x}\\ln a(a>0$且$a\\neq 1)$ | $d(a^{x})=a^{x}\\ln adx(a>0$且$a\\neq 1)$ |\n| $(e^{x})^{\\prime }=e^{x}$ | $d(e^{x})=e^{x}dx$ |\n| $(\\log _{a}x)^{\\prime }=\\frac {1}{x\\ln a}(a>0$且$a\\neq 1)$ | $d(\\log _{a}x)=\\frac {1}{x\\ln a}dx(a>0$且$a\\neq 1)$ |\n| $(\\ln x)^{\\prime }=\\frac {1}{x}$ | $d(\\ln x)=\\frac {1}{x}dx$ |\n| $(\\arcsin x)^{\\prime }=\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arcsin x)=\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arccos x)^{\\prime }=-\\frac {1}{\\sqrt {1-x^{2}}}$ | $d(\\arccos x)=-\\frac {1}{\\sqrt {1-x^{2}}}dx$ |\n| $(\\arctan x)^{\\prime }=\\frac {1}{1+x^{2}}$ | $d(\\arctan x)=\\frac {1}{1+x^{2}}dx$ |\n| $(\\operatorname{arccot}x)^{\\prime }=-\\frac {1}{1+x^{2}}$ | $d(\\operatorname{arccot}x)=-\\frac {1}{1+x^{2}}dx$ |\n", "type": "table", "position": [146, 240, 949, 236, 954, 1065, 150, 1070], "cells": [{"row_span": 1, "type": "cell", "text": "导数公式", "page_id": 1, "col_span": 1, "position": [151, 246, 556, 241, 557, 278, 152, 283], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "微分公式", "page_id": 1, "col_span": 1, "position": [556, 241, 937, 237, 938, 274, 557, 278], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "$(x^{\\mu })^{\\prime }=\\mu x^{\\mu -1}(\\mu$是任意常数）", "page_id": 1, "col_span": 1, "position": [152, 283, 557, 278, 557, 321, 152, 326], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "$d(x^{\\mu })=\\mu x^{\\mu -1}dx(\\mu$是任意常数）", "page_id": 1, "col_span": 1, "position": [557, 278, 938, 274, 938, 317, 557, 321], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "$(\\sin x)^{\\prime }=\\cos x$", "page_id": 1, "col_span": 1, "position": [152, 326, 557, 321, 558, 363, 153, 368], "col": 0, "row": 2}, {"row_span": 1, "type": "cell", "text": "$d(\\sin x)=\\cos xdx$", "page_id": 1, "col_span": 1, "position": [557, 321, 938, 317, 939, 359, 558, 363], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "$(\\cos x)^{\\prime }=-\\sin x$", "page_id": 1, "col_span": 1, "position": [153, 368, 558, 363, 558, 401, 153, 406], "col": 0, "row": 3}, {"row_span": 1, "type": "cell", "text": "$d(\\cos x)=-\\sin xdx$", "page_id": 1, "col_span": 1, "position": [558, 363, 939, 359, 939, 397, 558, 401], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "$(\\tan x)^{\\prime }=\\sec ^{2}x$", "page_id": 1, "col_span": 1, "position": [153, 406, 558, 401, 559, 449, 154, 454], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "$d(\\tan x)=\\sec ^{2}xdx$", "page_id": 1, "col_span": 1, "position": [558, 401, 939, 397, 940, 445, 559, 449], "col": 1, "row": 4}, {"row_span": 1, "type": "cell", "text": "$(\\cot x)^{\\prime }=-\\csc ^{2}x$", "page_id": 1, "col_span": 1, "position": [154, 454, 559, 449, 559, 491, 154, 496], "col": 0, "row": 5}, {"row_span": 1, "type": "cell", "text": "$d(\\cot x)=-\\csc ^{2}xdx$", "page_id": 1, "col_span": 1, "position": [559, 449, 940, 445, 940, 487, 559, 491], "col": 1, "row": 5}, {"row_span": 1, "type": "cell", "text": "$(\\sec x)^{\\prime }=\\sec x\\tan x$", "page_id": 1, "col_span": 1, "position": [154, 496, 559, 491, 560, 539, 155, 544], "col": 0, "row": 6}, {"row_span": 1, "type": "cell", "text": "$d(\\sec x)=\\sec x\\tan xdx$", "page_id": 1, "col_span": 1, "position": [559, 491, 940, 487, 941, 535, 560, 539], "col": 1, "row": 6}, {"row_span": 1, "type": "cell", "text": "$(\\csc x)^{\\prime }=-\\csc x\\cot x$", "page_id": 1, "col_span": 1, "position": [155, 544, 560, 539, 560, 582, 155, 587], "col": 0, "row": 7}, {"row_span": 1, "type": "cell", "text": "$d(\\csc x)=-\\csc x\\cot xdx$", "page_id": 1, "col_span": 1, "position": [560, 539, 941, 535, 941, 578, 560, 582], "col": 1, "row": 7}, {"row_span": 1, "type": "cell", "text": "$(a^{x})^{\\prime }=a^{x}\\ln a(a>0$且$a\\neq 1)$", "page_id": 1, "col_span": 1, "position": [155, 587, 560, 582, 561, 625, 156, 630], "col": 0, "row": 8}, {"row_span": 1, "type": "cell", "text": "$d(a^{x})=a^{x}\\ln adx(a>0$且$a\\neq 1)$", "page_id": 1, "col_span": 1, "position": [560, 582, 941, 578, 942, 621, 561, 625], "col": 1, "row": 8}, {"row_span": 1, "type": "cell", "text": "$(e^{x})^{\\prime }=e^{x}$", "page_id": 1, "col_span": 1, "position": [156, 630, 561, 625, 561, 678, 156, 683], "col": 0, "row": 9}, {"row_span": 1, "type": "cell", "text": "$d(e^{x})=e^{x}dx$", "page_id": 1, "col_span": 1, "position": [561, 625, 942, 621, 942, 674, 561, 678], "col": 1, "row": 9}, {"row_span": 1, "type": "cell", "text": "$(\\log _{a}x)^{\\prime }=\\frac {1}{x\\ln a}(a>0$且$a\\neq 1)$", "page_id": 1, "col_span": 1, "position": [156, 683, 561, 678, 562, 742, 157, 747], "col": 0, "row": 10}, {"row_span": 1, "type": "cell", "text": "$d(\\log _{a}x)=\\frac {1}{x\\ln a}dx(a>0$且$a\\neq 1)$", "page_id": 1, "col_span": 1, "position": [561, 678, 942, 674, 943, 738, 562, 742], "col": 1, "row": 10}, {"row_span": 1, "type": "cell", "text": "$(\\ln x)^{\\prime }=\\frac {1}{x}$", "page_id": 1, "col_span": 1, "position": [157, 747, 562, 742, 563, 801, 158, 806], "col": 0, "row": 11}, {"row_span": 1, "type": "cell", "text": "$d(\\ln x)=\\frac {1}{x}dx$", "page_id": 1, "col_span": 1, "position": [562, 742, 943, 738, 944, 797, 563, 801], "col": 1, "row": 11}, {"row_span": 1, "type": "cell", "text": "$(\\arcsin x)^{\\prime }=\\frac {1}{\\sqrt {1-x^{2}}}$", "page_id": 1, "col_span": 1, "position": [158, 806, 563, 801, 563, 870, 158, 875], "col": 0, "row": 12}, {"row_span": 1, "type": "cell", "text": "$d(\\arcsin x)=\\frac {1}{\\sqrt {1-x^{2}}}dx$", "page_id": 1, "col_span": 1, "position": [563, 801, 944, 797, 944, 866, 563, 870], "col": 1, "row": 12}, {"row_span": 1, "type": "cell", "text": "$(\\arccos x)^{\\prime }=-\\frac {1}{\\sqrt {1-x^{2}}}$", "page_id": 1, "col_span": 1, "position": [158, 875, 563, 870, 564, 934, 159, 939], "col": 0, "row": 13}, {"row_span": 1, "type": "cell", "text": "$d(\\arccos x)=-\\frac {1}{\\sqrt {1-x^{2}}}dx$", "page_id": 1, "col_span": 1, "position": [563, 870, 944, 866, 945, 930, 564, 934], "col": 1, "row": 13}, {"row_span": 1, "type": "cell", "text": "$(\\arctan x)^{\\prime }=\\frac {1}{1+x^{2}}$", "page_id": 1, "col_span": 1, "position": [159, 939, 564, 934, 565, 998, 160, 1003], "col": 0, "row": 14}, {"row_span": 1, "type": "cell", "text": "$d(\\arctan x)=\\frac {1}{1+x^{2}}dx$", "page_id": 1, "col_span": 1, "position": [564, 934, 945, 930, 946, 994, 565, 998], "col": 1, "row": 14}, {"row_span": 1, "type": "cell", "text": "$(\\operatorname{arccot}x)^{\\prime }=-\\frac {1}{1+x^{2}}$", "page_id": 1, "col_span": 1, "position": [160, 1003, 565, 998, 565, 1057, 160, 1062], "col": 0, "row": 15}, {"row_span": 1, "type": "cell", "text": "$d(\\operatorname{arccot}x)=-\\frac {1}{1+x^{2}}dx$", "page_id": 1, "col_span": 1, "position": [565, 998, 946, 994, 946, 1053, 565, 1057], "col": 1, "row": 15}], "sub_type": "bordered"}, {"paragraph_id": 4, "page_id": 1, "tags": [], "outline_level": 0, "text": "2．函数和、差、积、商的微分法则", "type": "paragraph", "position": [197, 1093, 533, 1089, 532, 1111, 196, 1115], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 1, "tags": ["formula"], "outline_level": -1, "text": "由函数和、差、积、商的求导法则，可推得相应的微分法则.为了便于对照，列成下表（表中 $u=u(x),v=v(x)$ 都可导）.", "type": "paragraph", "position": [158, 1139, 955, 1139, 955, 1210, 158, 1210], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 1, "content": 0, "outline_level": -1, "text": "\n| 函数和、差、积、商的求导法则 | 函数和、差、积、商的微分法则 |\n| --- | --- |\n| $(u\\pm v)^{\\prime }=u^{\\prime }\\pm v^{\\prime }$ | $d(u\\pm v)=du\\pm dv$ |\n| $(Cu)^{\\prime }=Cu^{\\prime }$ $(C$是常数） | $d(Cu)=Cdu$（$C$是常数） |\n| $(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }$ | $d(uv)=vdu+udv$ |\n| $(\\frac {u}{v})^{\\prime }=\\frac {u^{\\prime }v-uv^{\\prime }}{v^{2}}$ $(v\\neq 0)$ | $d(\\frac {u}{v})=\\frac {vdu-udv}{v^{2}}$ $(v\\neq 0)$ |\n", "type": "table", "position": [160, 1225, 955, 1216, 959, 1437, 162, 1446], "cells": [{"row_span": 1, "type": "cell", "text": "函数和、差、积、商的求导法则", "page_id": 1, "col_span": 1, "position": [172, 1224, 573, 1219, 574, 1255, 173, 1260], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "函数和、差、积、商的微分法则", "page_id": 1, "col_span": 1, "position": [573, 1219, 940, 1215, 940, 1251, 574, 1255], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "$(u\\pm v)^{\\prime }=u^{\\prime }\\pm v^{\\prime }$", "page_id": 1, "col_span": 1, "position": [173, 1260, 574, 1255, 574, 1291, 173, 1296], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "$d(u\\pm v)=du\\pm dv$", "page_id": 1, "col_span": 1, "position": [574, 1255, 940, 1251, 941, 1287, 574, 1291], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "$(Cu)^{\\prime }=Cu^{\\prime }$ $(C$是常数）", "page_id": 1, "col_span": 1, "position": [173, 1296, 574, 1291, 574, 1333, 173, 1338], "col": 0, "row": 2}, {"row_span": 1, "type": "cell", "text": "$d(Cu)=Cdu$（$C$是常数）", "page_id": 1, "col_span": 1, "position": [574, 1291, 941, 1287, 941, 1329, 574, 1333], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "$(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }$", "page_id": 1, "col_span": 1, "position": [173, 1338, 574, 1333, 575, 1369, 174, 1374], "col": 0, "row": 3}, {"row_span": 1, "type": "cell", "text": "$d(uv)=vdu+udv$", "page_id": 1, "col_span": 1, "position": [574, 1333, 941, 1329, 942, 1365, 575, 1369], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "$(\\frac {u}{v})^{\\prime }=\\frac {u^{\\prime }v-uv^{\\prime }}{v^{2}}$ $(v\\neq 0)$", "page_id": 1, "col_span": 1, "position": [174, 1374, 575, 1369, 575, 1431, 174, 1436], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "$d(\\frac {u}{v})=\\frac {vdu-udv}{v^{2}}$ $(v\\neq 0)$", "page_id": 1, "col_span": 1, "position": [575, 1369, 942, 1365, 942, 1427, 575, 1431], "col": 1, "row": 4}], "sub_type": "bordered"}, {"paragraph_id": 8, "page_id": 1, "tags": [], "outline_level": -1, "text": "111", "type": "paragraph", "position": [977, 1521, 1013, 1521, 1013, 1537, 977, 1537], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 2, "tags": [], "outline_level": -1, "text": "第二章 导数与微分", "type": "paragraph", "position": [221, 121, 379, 123, 380, 139, 222, 137], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 2, "tags": [], "outline_level": -1, "text": "现在我们以乘积的微分法则为例加以证明.", "type": "paragraph", "position": [264, 207, 670, 213, 671, 235, 265, 230], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 2, "tags": [], "outline_level": -1, "text": "根据函数微分的表达式，有", "type": "paragraph", "position": [265, 245, 521, 249, 521, 271, 266, 268], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$d(uv)=(uv)^{\\prime }dx.$$", "type": "paragraph", "position": [543, 285, 701, 285, 701, 314, 543, 314], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 2, "tags": [], "outline_level": -1, "text": "再根据乘积的求导法则，有", "type": "paragraph", "position": [221, 321, 478, 324, 478, 347, 222, 344], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$(uv)^{\\prime }=u^{\\prime }v+uv^{\\prime }.$$", "type": "paragraph", "position": [549, 362, 696, 362, 696, 390, 549, 390], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 2, "tags": [], "outline_level": -1, "text": "于是", "type": "paragraph", "position": [222, 398, 271, 398, 271, 418, 222, 418], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$d(uv)=(u^{\\prime }v+uv^{\\prime })dx=u^{\\prime }vdx+uv^{\\prime }dx.$$", "type": "paragraph", "position": [457, 435, 785, 437, 784, 466, 457, 464], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 2, "tags": [], "outline_level": -1, "text": "由于", "type": "paragraph", "position": [222, 473, 271, 473, 271, 494, 222, 494], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$u^{\\prime }dx=du,v^{\\prime }dx=dv$$", "type": "paragraph", "position": [526, 511, 709, 514, 708, 542, 526, 538], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 2, "tags": [], "outline_level": -1, "text": "所以", "type": "paragraph", "position": [220, 548, 270, 548, 270, 569, 220, 569], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$d(uv)=vdu+udv.$$", "type": "paragraph", "position": [539, 587, 701, 587, 701, 615, 539, 615], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 2, "tags": [], "outline_level": -1, "text": "其他法则都可以用类似方法证明.", "type": "paragraph", "position": [262, 623, 577, 627, 577, 649, 263, 645], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 2, "tags": [], "outline_level": 0, "text": "3．复合函数的微分法则", "type": "paragraph", "position": [261, 680, 488, 682, 488, 704, 261, 702], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 15, "page_id": 2, "tags": [], "outline_level": -1, "text": "与复合函数的求导法则相应的复合函数的微分法则可推导如下：", "type": "paragraph", "position": [264, 735, 868, 742, 868, 765, 264, 758], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "设 $y=f(u)$ 及 $u=g(x)$ 都可导，则复合函数 $y=f[g(x)]$ 的微分为", "type": "paragraph", "position": [263, 771, 860, 771, 860, 806, 263, 806], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$dy=y_{x}^{\\prime }dx\\textcircled {1}=f^{\\prime }(u)g^{\\prime }(x)dx.$$", "type": "paragraph", "position": [493, 810, 744, 814, 743, 845, 493, 841], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "由于 $g^{\\prime }(x)dx=du$ ，所以，复合函数 $y=f[g(x)$ ］的微分公式也可以写成", "type": "paragraph", "position": [263, 847, 923, 847, 923, 880, 263, 880], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$dy=f^{\\prime }(u)du$  或 $dy=y_{u}^{\\prime }du.$ ", "type": "paragraph", "position": [483, 886, 753, 886, 753, 919, 483, 919], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "由此可见，无论 $u$ 是自变量还是中间变量，微分形式 $dy=f^{\\prime }(u)$ u保持不变.这一性质称为微分形式不变性.这性质表示，当变换自变量时，微分形式 $dy=f^{\\prime }(u)du$ 并不改变.", "type": "paragraph", "position": [217, 922, 1022, 922, 1022, 1018, 217, 1018], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "**例3** 设 $y=\\sin (2x+1)$ ，求 $dy.$ ", "type": "paragraph", "position": [261, 1035, 541, 1035, 541, 1066, 261, 1066], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "解把 $2x+1$ 看成中间变量 $u$ ，则", "type": "paragraph", "position": [259, 1071, 569, 1072, 568, 1100, 258, 1098], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$dy=d(\\sin u)=\\cos udu=\\cos (2x+1)d(2x+1)$$", "type": "paragraph", "position": [410, 1109, 824, 1118, 824, 1149, 409, 1141], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$$=\\cos (2x+1)\\cdot 2dx=2\\cos (2x+1)dx.$$", "type": "paragraph", "position": [434, 1148, 767, 1155, 766, 1184, 434, 1177], "content": 0, "sub_type": "text"}, {"paragraph_id": 25, "page_id": 2, "tags": [], "outline_level": -1, "text": "在求复合函数的导数时，可以不写出中间变量.在求复合函数的微分时，类似地也可以不写出中间变量.下面我们用这种方法来求函数的微分.", "type": "paragraph", "position": [216, 1182, 1019, 1199, 1018, 1260, 215, 1243], "content": 0, "sub_type": "text"}, {"paragraph_id": 26, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "例4 设 $y=\\ln (1+e^{x^{2}})$ ，求dy.", "type": "paragraph", "position": [258, 1258, 531, 1258, 531, 1289, 258, 1289], "content": 0, "sub_type": "text"}, {"paragraph_id": 27, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "解 $dy=d(\\ln (1+e^{x2}))=\\frac {1}{1+e^{z^{2}}}d(1+e^{z^{2}})=\\frac {1}{1+e^{z^{2}}}·e^{z^{2}}d(x^{2})=\\frac {e^{z^{2}}}{1+e^{z^{2}}}·2xdx=\\frac {2xe^{z^{2}}}{1+e^{z^{2}}}dx.$ ", "type": "paragraph", "position": [257, 1295, 1015, 1295, 1015, 1371, 257, 1371], "content": 0, "sub_type": "text"}, {"paragraph_id": 28, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "例5 设 $y=e^{1-3x}\\cos x$ ，求dy.", "type": "paragraph", "position": [256, 1367, 523, 1367, 523, 1398, 256, 1398], "content": 0, "sub_type": "text"}, {"paragraph_id": 29, "page_id": 2, "tags": ["formula"], "outline_level": -1, "text": "$\\textcircled {1}$  其中 $y_{x}^{\\prime }$ 2表示 $y$ 对 $x$ 的导数，它也是导数的一种表示方法．", "type": "paragraph", "position": [244, 1446, 679, 1455, 679, 1473, 245, 1464], "content": 0, "sub_type": "text"}, {"paragraph_id": 30, "page_id": 2, "tags": [], "outline_level": -1, "text": "112", "type": "paragraph", "position": [163, 1520, 197, 1520, 197, 1536, 163, 1536], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 3, "tags": [], "outline_level": -1, "text": "**第五节 函数的微分**", "type": "paragraph", "position": [779, 120, 933, 123, 934, 138, 779, 136], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 3, "tags": [], "outline_level": -1, "text": "**解** 应用积的微分法则，得", "type": "paragraph", "position": [177, 201, 433, 204, 434, 227, 177, 224], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$dy=d(e^{1-3x}\\cos x)=\\cos xd(e^{1-3x})+e^{1-3x}d(\\cos x)\\\\ =(\\cos x)e^{1-3x}(-3dx)+e^{1-3x}(-\\sin xdx)=-e^{1-3x}(3\\cos x+\\sin x)dx.$$", "type": "paragraph", "position": [227, 236, 839, 241, 839, 312, 227, 312], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 3, "tags": [], "outline_level": -1, "text": "**例6** 在下列等式左端的括号中填入适当的函数，使等式成立．", "type": "paragraph", "position": [177, 319, 757, 325, 758, 347, 177, 341], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$($ $)$ $($ $)$ (1) $d$  $=xdx;$ (2) $d$  $=\\cos\\omega tdt$  $(\\neq 0)$ ", "type": "paragraph", "position": [181, 354, 752, 354, 752, 390, 181, 390], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 3, "tags": [], "outline_level": -1, "text": "解（1）我们知道，", "type": "paragraph", "position": [178, 396, 367, 396, 367, 420, 178, 420], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$d(x^{2})=2xdx.$$", "type": "paragraph", "position": [470, 434, 595, 434, 595, 462, 470, 462], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 3, "tags": [], "outline_level": -1, "text": "可见", "type": "paragraph", "position": [133, 471, 184, 471, 184, 491, 133, 491], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$xdx=\\frac {1}{2}d(x^{2})=d(\\frac {x^{2}}{2})$$", "type": "paragraph", "position": [421, 510, 638, 510, 638, 572, 421, 572], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 3, "tags": [], "outline_level": -1, "text": "即", "type": "paragraph", "position": [133, 576, 161, 576, 161, 605, 133, 605], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$d(\\frac {x^{2}}{2})=xdx.$$", "type": "paragraph", "position": [472, 617, 592, 617, 592, 679, 472, 679], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 3, "tags": [], "outline_level": -1, "text": "一般地，有", "type": "paragraph", "position": [176, 689, 280, 689, 280, 710, 176, 710], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$d(\\frac {x^{2}}{2}+C)=xdx$  （ $C$ 为任意常数）.", "type": "paragraph", "position": [373, 723, 689, 723, 689, 788, 373, 788], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 3, "tags": [], "outline_level": -1, "text": "（2）因为", "type": "paragraph", "position": [179, 798, 270, 798, 270, 818, 179, 818], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$d(\\sin \\omega t)=\\omega \\cos \\omega tdt,$$", "type": "paragraph", "position": [428, 834, 638, 836, 638, 865, 427, 863], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 3, "tags": [], "outline_level": -1, "text": "可见", "type": "paragraph", "position": [133, 873, 183, 873, 183, 892, 133, 892], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$\\cos \\omega tdt=\\frac {1}{\\omega }d(\\sin \\omega t)=d(\\frac {1}{\\omega }\\sin \\omega t),$$", "type": "paragraph", "position": [355, 903, 702, 903, 702, 965, 355, 965], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 3, "tags": [], "outline_level": -1, "text": "即", "type": "paragraph", "position": [133, 972, 161, 972, 161, 1000, 133, 1000], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$d(\\frac {1}{\\omega }\\sin \\omega t)=\\cos \\omega tdt.$$", "type": "paragraph", "position": [419, 1003, 645, 1003, 645, 1066, 419, 1066], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 3, "tags": [], "outline_level": -1, "text": "一般地，有", "type": "paragraph", "position": [178, 1076, 280, 1076, 280, 1098, 178, 1098], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$d(\\frac {1}{\\omega }\\sin \\omega t+C)=\\cos \\omega tdt$  （ $C$ 为任意常数， $\\neq 0).$ ", "type": "paragraph", "position": [297, 1110, 765, 1110, 765, 1175, 297, 1175], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 3, "tags": [], "outline_level": 0, "text": "四、微分在近似计算中的应用", "type": "paragraph", "position": [178, 1200, 503, 1200, 503, 1226, 178, 1226], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 25, "page_id": 3, "tags": [], "outline_level": 1, "text": "1．函数的近似计算", "type": "paragraph", "position": [176, 1278, 360, 1278, 360, 1299, 176, 1299], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 26, "page_id": 3, "tags": [], "outline_level": -1, "text": "在工程问题中，经常会遇到一些复杂的计算公式.如果直接用这些公式进行计算，那是很费力的.利用微分往往可以把一些复杂的计算公式用简单的近似公式来代替.", "type": "paragraph", "position": [131, 1333, 931, 1333, 931, 1396, 131, 1396], "content": 0, "sub_type": "text"}, {"paragraph_id": 27, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "前面说过，如果 $y=f(x)$ 在点 $x_{0}$ 处的导数 $f^{\\prime }(x_{0})\\neq 0$ ，且1 $\\Delta x\\vert$ 很小时，我们有", "type": "paragraph", "position": [176, 1408, 872, 1408, 872, 1436, 176, 1436], "content": 0, "sub_type": "text"}, {"paragraph_id": 28, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "$$\\Delta y\\approx dy=f^{\\prime }(x_{0})\\Delta x.$$", "type": "paragraph", "position": [442, 1446, 622, 1446, 622, 1473, 442, 1473], "content": 0, "sub_type": "text"}, {"paragraph_id": 29, "page_id": 3, "tags": [], "outline_level": -1, "text": "113", "type": "paragraph", "position": [949, 1528, 982, 1528, 982, 1545, 949, 1545], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 4, "tags": [], "outline_level": -1, "text": "第二章 导数与微分", "type": "paragraph", "position": [234, 129, 388, 129, 388, 145, 234, 145], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 4, "tags": [], "outline_level": -1, "text": "这个式子也可以写为", "type": "paragraph", "position": [233, 218, 436, 216, 435, 237, 233, 239], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$\\Delta y=f(x_{0}+\\Delta x)-f(x_{0})\\approx f^{\\prime }(x_{0})\\Delta x\\tag{5-4}$$", "type": "paragraph", "position": [477, 252, 1036, 252, 1036, 281, 477, 281], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 4, "tags": [], "outline_level": -1, "text": "或", "type": "paragraph", "position": [234, 291, 263, 291, 263, 319, 234, 319], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$f(x_{0}+\\Delta x)\\approx f(x_{0})+f^{\\prime }(x_{0})\\Delta x.\\tag{5-5}$$", "type": "paragraph", "position": [496, 329, 1036, 327, 1036, 357, 497, 359], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "在（5-5）式中令 $x=x_{0}+\\Delta x$ ，即 $\\Delta x=x-x_{0}$ ，那么（5-5）式可改写为", "type": "paragraph", "position": [280, 367, 868, 367, 868, 396, 280, 396], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$f(x)\\approx f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})\\tag{5-6}$$", "type": "paragraph", "position": [498, 405, 1036, 405, 1036, 436, 498, 436], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "如果 $f(x_{0})$ 与 $f^{\\prime }(x_{0})$ 都容易计算，那么可利用（5-4）式来近似计算 $\\Delta y$ ，利用（5-5）式来近似计算 $f(x_{0}+\\Delta x)$ ，或利用（5-6）式来近似计算 $f(x)$ .这种近似计算的实质就是用 $x$ 的线性函数 $f(x_{0})+f^{\\prime }(x_{0})(x-x_{0})$ 来近似表达函数 $f(x)$ .从导数的几何意义可知，这也就是用曲线 $y=f(x)$ 在点 $(x_{0},f(x_{0}))$ 处的切线来近似代替该曲线（就切点邻近部分来说）.", "type": "paragraph", "position": [234, 444, 1041, 444, 1041, 625, 234, 625], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "**例7** 有一批半径为 $1cm$ 的球，为了提高球面的光洁度，要镀上一层铜，厚度定为 $0.01cm$  估计一下镀每只球需用多少克铜（铜的密度是 $8.9g/cm^{3}$ )？", "type": "paragraph", "position": [237, 639, 1041, 639, 1041, 700, 237, 700], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 4, "tags": [], "outline_level": -1, "text": "**解** 先求出镀层的体积，再乘密度就可得到镀每只球需用铜的质量.", "type": "paragraph", "position": [280, 716, 914, 714, 913, 736, 279, 738], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "因为镀层的体积等于镀铜前、后两个球体体积之差，所以它就是球体体积 $V=$ $\\frac {4}{3}\\pi R^{3}$ 当 $R$ 自 $R_{0}$ 取得增量 $\\Delta R$ 时的增量 $\\Delta V.$ .我们求 $A$ 对 $R$ 的导数", "type": "paragraph", "position": [238, 752, 1039, 752, 1039, 848, 238, 848], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$V^{\\prime }\\vert _{R=R_{0}}=$$", "type": "paragraph", "position": [482, 857, 791, 859, 791, 922, 482, 920], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 4, "tags": [], "outline_level": -1, "text": "由（5-4）式得", "type": "paragraph", "position": [242, 932, 372, 932, 372, 954, 242, 954], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$\\Delta V\\approx 4\\pi R_{0}^{2}\\Delta R.$$", "type": "paragraph", "position": [572, 966, 708, 969, 707, 998, 572, 995], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "将 $R_{0}=1,\\Delta R=0.01$ 代入上式，得", "type": "paragraph", "position": [283, 1006, 590, 1006, 590, 1036, 283, 1036], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$\\Delta V\\approx 4\\times 3.14\\times 1^{2}\\times 0.01\\approx 0.13(cm^{3})$$", "type": "paragraph", "position": [474, 1044, 801, 1047, 801, 1078, 473, 1075], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 4, "tags": [], "outline_level": -1, "text": "于是镀每只球需用的铜约为", "type": "paragraph", "position": [241, 1086, 506, 1088, 506, 1110, 242, 1107], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$0.13\\times 8.9\\approx 1.16(g)$$", "type": "paragraph", "position": [546, 1125, 729, 1125, 729, 1153, 546, 1153], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "例8 利用微分计算sin $30^{\\circ }30^{\\prime }$ 的近似值．", "type": "paragraph", "position": [282, 1162, 668, 1164, 669, 1187, 283, 1184], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "解把 $30^{\\circ }30^{\\prime }$ 化为弧度，得", "type": "paragraph", "position": [283, 1198, 539, 1201, 539, 1225, 282, 1221], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "$$30^{\\circ }30^{\\prime }=\\frac {\\pi }{6}+\\frac {\\pi }{360}.$$", "type": "paragraph", "position": [560, 1243, 720, 1248, 718, 1297, 558, 1292], "content": 0, "sub_type": "text"}, {"paragraph_id": 25, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "由于所求的是正弦函数的值，故设 $f(x)=\\sin x.$ 此时f＇ $(x)=\\cos$ x.如果取 $x_{0}=\\frac {\\pi }{6}$ ，那么 $f(\\frac {\\pi }{6})=\\sin \\frac {\\pi }{6}=\\frac {1}{2}$ 与 $f^{\\prime }(\\frac {\\pi }{6})=\\cos \\frac {\\pi }{6}=\\frac {\\sqrt {3}}{2}$ 都容易计算，并且 $\\Delta x=\\frac {\\pi }{360}$ 比较小.应用（5-5）式便得", "type": "paragraph", "position": [243, 1321, 1044, 1321, 1044, 1474, 243, 1474], "content": 0, "sub_type": "text"}, {"paragraph_id": 27, "page_id": 4, "tags": [], "outline_level": -1, "text": "114", "type": "paragraph", "position": [192, 1528, 225, 1528, 225, 1544, 192, 1544], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 5, "tags": [], "outline_level": -1, "text": "第五节 函数的微分", "type": "paragraph", "position": [784, 125, 940, 127, 941, 144, 784, 141], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$\\sin 30^{\\circ }30^{\\prime }=\\sin (\\frac {\\pi }{6}+\\frac {\\pi }{360})\\approx \\sin \\frac {\\pi }{6}+\\cos \\frac {\\pi }{6}\\times \\frac {\\pi }{360}\\\\ =\\frac {1}{2}+\\frac {\\sqrt {3}}{2}\\times \\frac {\\pi }{360}\\approx 0.5000+0.0076=0.5076.$$", "type": "paragraph", "position": [291, 209, 776, 212, 776, 347, 291, 347], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "下面我们来推导一些常用的近似公式．为此，在（5-6）式中取 $x_{0}=0$ ，于是得", "type": "paragraph", "position": [183, 360, 870, 360, 870, 391, 183, 391], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$f(x)\\approx f(0)+f^{\\prime }(0)x.\\tag{5-7}$$", "type": "paragraph", "position": [439, 398, 937, 398, 937, 429, 439, 429], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "应用（5-7）式可以推得以下几个在工程上常用的近似公式（下面都假定 $\\vert x\\vert$ 是较小的数值）：", "type": "paragraph", "position": [140, 437, 941, 437, 941, 499, 140, 499], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "(i) $(1+x)^{\\alpha }\\approx 1+\\alpha x$ $(\\alpha \\in R),$ ", "type": "paragraph", "position": [186, 515, 464, 515, 464, 544, 186, 544], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "(ii)sin $x\\approx x$  （ $x$ 以弧度为单位），", "type": "paragraph", "position": [186, 556, 502, 556, 502, 581, 186, 581], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "(iii)tan $x\\approx x$  （ $x$ 以弧度为单位），", "type": "paragraph", "position": [186, 593, 510, 593, 510, 618, 186, 618], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "(iv) $e^{x}\\approx 1+x,$ ", "type": "paragraph", "position": [186, 625, 323, 632, 321, 661, 185, 654], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "(v) $\\ln (1+x)\\approx x.$ ", "type": "paragraph", "position": [186, 669, 346, 669, 346, 700, 186, 700], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "证（i）在第一章第九节例7中我们已经知道 $(1+x)^{\\alpha }-1\\sim \\alpha x(x\\rightarrow 0)$ ，从而得出这个近似公式.在这里，我们利用微分证明.取 $f(x)=(1+x)^{\\alpha }$ ，那么 $f(0)=1,f^{\\prime }(0)=$ $\\alpha (1+x)^{\\alpha -1}\\vert _{x=0}=\\alpha ,代入(5-7)$ 式便得", "type": "paragraph", "position": [140, 710, 939, 710, 939, 845, 140, 845], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$(1+x)^{\\alpha }\\approx 1+\\alpha x.$$", "type": "paragraph", "position": [467, 852, 612, 852, 612, 881, 467, 881], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "（ii）取 $f(x)=\\sin x$ ，那么 $f(0)=0,f^{\\prime }(0)=\\cos x\\vert _{x=0}=1,代入(5-7):$ 式便得", "type": "paragraph", "position": [187, 895, 877, 895, 877, 944, 187, 944], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$\\sin x\\approx x.$$", "type": "paragraph", "position": [500, 957, 581, 959, 580, 980, 499, 978], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 5, "tags": [], "outline_level": -1, "text": "其他几个近似公式可用类似方法证明，这里从略了.", "type": "paragraph", "position": [184, 992, 663, 992, 663, 1014, 184, 1014], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "例9 计算 $\\sqrt {1.05}$ 的近似值．", "type": "paragraph", "position": [186, 1033, 442, 1033, 442, 1058, 186, 1058], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "解 $\\sqrt {1.05}=\\sqrt {1+0.05}$ ", "type": "paragraph", "position": [184, 1077, 623, 1077, 623, 1108, 184, 1108], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "这里 $x=0.05$ ，其值较小，利用近似公式(i) $(\\alpha =\\frac {1}{2}$ 的情形），便得", "type": "paragraph", "position": [141, 1123, 732, 1123, 732, 1176, 141, 1176], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$\\sqrt {1.05}\\approx 1+\\frac {1}{2}\\times 0.05=1.025.$$", "type": "paragraph", "position": [416, 1201, 671, 1201, 671, 1253, 416, 1253], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 5, "tags": [], "outline_level": -1, "text": "如果直接开方，可得", "type": "paragraph", "position": [186, 1277, 377, 1277, 377, 1298, 186, 1298], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$\\sqrt {1.05}=1.02470.$$", "type": "paragraph", "position": [464, 1322, 625, 1322, 625, 1353, 464, 1353], "content": 0, "sub_type": "text"}, {"paragraph_id": 25, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "将两个结果比较一下，可以看出，用1.025作为 $\\sqrt {1.05}$ 的近似值，其误差不超过0.001，这样的近似值在一般应用上已够精确了.如果开方次数较高，就更能体现出用微分进行近似计算的优越性.", "type": "paragraph", "position": [141, 1375, 944, 1375, 944, 1478, 141, 1478], "content": 0, "sub_type": "text"}, {"paragraph_id": 26, "page_id": 5, "tags": [], "outline_level": -1, "text": "115", "type": "paragraph", "position": [959, 1534, 992, 1534, 992, 1550, 959, 1550], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 6, "tags": [], "outline_level": -1, "text": "第二章 导数与微分", "type": "paragraph", "position": [227, 133, 383, 133, 383, 148, 227, 148], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 6, "tags": [], "outline_level": 1, "text": "＂2．误差估计", "type": "paragraph", "position": [258, 238, 390, 238, 390, 259, 258, 259], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "在生产实践中，经常要测量各种数据.但是有的数据不易直接测量，这时我们就通过测量其他有关数据后，根据某种公式算出所要的数据.例如，要计算圆钢的截面积A，可先用卡尺测量圆钢截面的直径 $D$ ，然后根据公式 $A=\\frac {\\pi }{4}D^{2}$ 算出A.", "type": "paragraph", "position": [226, 291, 1034, 291, 1034, 423, 226, 423], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 6, "tags": [], "outline_level": -1, "text": "由于测量仪器的精度、测量的条件和测量的方法等各种因素的影响，测得的数据往往带有误差，而根据带有误差的数据计算所得的结果也会有误差，我们把它叫做间接测量误差", "type": "paragraph", "position": [229, 439, 1034, 439, 1034, 544, 229, 544], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 6, "tags": [], "outline_level": -1, "text": "下面就讨论怎样利用微分来估计间接测量误差.", "type": "paragraph", "position": [273, 557, 720, 557, 720, 578, 273, 578], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 6, "tags": [], "outline_level": -1, "text": "先说明绝对误差、相对误差的概念.", "type": "paragraph", "position": [273, 596, 602, 596, 602, 617, 273, 617], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "如果某个量的精确值为A，它的近似值为 $a$ ，那么 $\\vert A-a\\vert$ 叫做 $a$ 的绝对误差，而绝对误差与 $\\vert a\\vert$ 的比值 $\\frac {\\vert A-a\\vert }{\\vert a\\vert }$ 叫做 $a$ 的相对误差.", "type": "paragraph", "position": [230, 628, 1035, 628, 1035, 730, 230, 730], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "在实际工作中，某个量的精确值往往是无法知道的，于是绝对误差和相对误差也就无法求得.但是根据测量仪器的精度等因素，有时能够确定误差在某一个范围内.如果某个量的精确值是A，测得它的近似值是 $a$ ，又知道它的误差不超过 $δ_{A}$ ，即", "type": "paragraph", "position": [230, 743, 1036, 743, 1036, 849, 230, 849], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "$$\\vert A-a\\vert \\leq \\delta _{A},$$", "type": "paragraph", "position": [579, 857, 686, 857, 686, 886, 579, 886], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "那么 $δ_{A}$ 叫做测量 $A$ 的绝对误差限，而 $\\frac {\\delta _{A}}{\\vert a\\vert }$ 叫做测量 $A$ 的相对误差限.", "type": "paragraph", "position": [232, 901, 862, 901, 862, 960, 232, 960], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "**例10** 设测得圆钢截面的直径 $D=60.03$ mm，测量 $D$  的绝对误差限 $δ_{D}=$ $0.05mm$ ．利用公式", "type": "paragraph", "position": [234, 974, 1034, 974, 1034, 1034, 234, 1034], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "$$A=\\frac {\\pi }{4}D^{2}$$", "type": "paragraph", "position": [594, 1059, 673, 1059, 673, 1103, 594, 1103], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 6, "tags": [], "outline_level": -1, "text": "计算圆钢的截面积时，试估计面积的误差.", "type": "paragraph", "position": [231, 1119, 624, 1122, 625, 1144, 232, 1141], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "解 如果我们把测量 $D$ 时所产生的误差当作自变量 $D$ 的增量 $\\Delta D$ ，那么，利用公式 $A=\\frac {\\pi }{4}D^{2}$ 来计算 $A$ 时所产生的误差就是函数A的对应增量 $\\Delta A.$ .当 $\\vert \\Delta D\\vert$ 很小时，可以利用微分dA近似地代替增量 $\\Delta A$ ，即", "type": "paragraph", "position": [232, 1157, 1038, 1157, 1038, 1291, 232, 1291], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "$$\\Delta A\\approx dA=A^{\\prime }\\cdot \\Delta D=\\frac {\\pi }{2}D\\cdot \\Delta D.$$", "type": "paragraph", "position": [495, 1313, 770, 1317, 769, 1362, 495, 1359], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "由于 $D$ 的绝对误差限为 $δ_{D}=0.05\\mathrm {\\sim mm}$ ，所以", "type": "paragraph", "position": [234, 1375, 636, 1375, 636, 1406, 234, 1406], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "$\\vert \\Delta D\\vert \\leq \\delta _{D}=0.05$ ,", "type": "paragraph", "position": [554, 1415, 719, 1418, 718, 1449, 553, 1446], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 6, "tags": [], "outline_level": -1, "text": "而", "type": "paragraph", "position": [234, 1452, 261, 1452, 261, 1480, 234, 1480], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 6, "tags": [], "outline_level": -1, "text": "116", "type": "paragraph", "position": [186, 1531, 219, 1531, 219, 1547, 186, 1547], "content": 1, "sub_type": "footer"}]}, "x_request_id": "bdcc9bf08706a0697e1bcdf7b513f8a3", "metrics": [{"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 1, "duration": 1744.1282958984, "page_image_width": 1190, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 2, "duration": 1409.42578125, "page_image_width": 1190, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 3, "duration": 1285.5728759766, "page_image_width": 1190, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 4, "duration": 1598.3387451172, "page_image_width": 1190, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 5, "duration": 1410.1507568359, "page_image_width": 1190, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 6, "duration": 1396.7879638672, "page_image_width": 1190, "page_image_height": 1684}], "duration": 1911, "message": "Success", "version": "3.16.14"}