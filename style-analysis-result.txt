=== Word文档样式分析结果 ===
文档路径: src/test/resources/multi-level-headings.docx
分析时间: <PERSON><PERSON> 24 09:53:27 CST 2025

✅ 文档加载成功
📄 文档元素总数: 18
段落 1: 样式=a3, 文本长度=9, 内容=这是这篇文章的标题
段落 2: 样式=1, 文本长度=8, 内容=这是我的一级标题
段落 3: 样式=Normal, 文本长度=12, 内容=这是我的一级标题下的内容
段落 4: 样式=Normal, 文本长度=0, 内容=
段落 5: 样式=Normal, 文本长度=0, 内容=
段落 6: 样式=2, 文本长度=8, 内容=这是我的二级标题
段落 7: 样式=Normal, 文本长度=6, 内容=二级标题内容
段落 8: 样式=Normal, 文本长度=0, 内容=
段落 9: 样式=Normal, 文本长度=0, 内容=
段落 10: 样式=3, 文本长度=8, 内容=这是我的三级标题
段落 11: 样式=Normal, 文本长度=0, 内容=
段落 12: 样式=Normal, 文本长度=6, 内容=三级标题内容
段落 13: 样式=Normal, 文本长度=0, 内容=
段落 14: 样式=4, 文本长度=8, 内容=这是我的四级标题
段落 15: 样式=Normal, 文本长度=0, 内容=
段落 16: 样式=Normal, 文本长度=6, 内容=四级标题内容
段落 17: 样式=Normal, 文本长度=0, 内容=
段落 18: 样式=Normal, 文本长度=0, 内容=

📊 段落统计:
  - 段落总数: 18
  - 有内容的段落: 9
  - 样式种类: 6

📋 所有样式详情:
--------------------------------------------------------------------------------
📄 样式: Normal               | 数量:  4 | 示例: 这是我的一级标题下的内容
📄 样式: 1                    | 数量:  1 | 示例: 这是我的一级标题
📄 样式: a3                   | 数量:  1 | 示例: 这是这篇文章的标题
📄 样式: 2                    | 数量:  1 | 示例: 这是我的二级标题
📄 样式: 3                    | 数量:  1 | 示例: 这是我的三级标题
📄 样式: 4                    | 数量:  1 | 示例: 这是我的四级标题

🔍 标题样式分析:
--------------------------------------------------
❌ 未发现明显的标题样式

💡 建议:
1. 检查Word文档是否使用了标题样式
2. 在Word中选择文本，查看样式面板中的样式名称
3. 确保使用的是'标题1'、'标题2'或'Heading1'、'Heading2'等标准样式
4. 如果使用的是中文版Word，样式可能是'标题 1'、'标题 2'等

============================================================
