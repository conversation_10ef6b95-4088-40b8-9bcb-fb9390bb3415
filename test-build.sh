#!/bin/bash

# 构建测试脚本
# 用于验证bootJar构建是否正常工作

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

JAR_NAME="ContentManager-0.0.4-SNAPSHOT.jar"

log_info "🔨 开始测试 Spring Boot 项目构建..."

# 检查gradlew是否存在
if [[ ! -f "./gradlew" ]]; then
    log_error "gradlew 文件未找到，请确保在项目根目录执行此脚本"
    exit 1
fi

# 清理并构建
log_info "执行清理任务..."
./gradlew clean

log_info "执行 bootJar 构建..."
./gradlew bootJar

# 检查构建结果
if [[ -f "build/libs/${JAR_NAME}" ]]; then
    jar_size=$(du -h "build/libs/${JAR_NAME}" | cut -f1)
    log_info "✅ 构建成功!"
    log_info "  📦 文件: build/libs/${JAR_NAME}"
    log_info "  📏 大小: ${jar_size}"
    log_info "  🕐 修改时间: $(stat -f "%Sm" "build/libs/${JAR_NAME}" 2>/dev/null || stat -c "%y" "build/libs/${JAR_NAME}" 2>/dev/null)"
    
    # 验证jar文件是否可执行
    log_info "验证jar文件内容..."
    if jar -tf "build/libs/${JAR_NAME}" | grep -q "BOOT-INF/classes"; then
        log_info "✅ 这是一个有效的Spring Boot可执行jar文件"
    else
        log_warn "⚠️  警告：这可能不是一个Spring Boot可执行jar文件"
    fi
    
    # 显示jar文件的主清单属性
    log_info "检查jar文件的主清单属性..."
    if jar -xf "build/libs/${JAR_NAME}" META-INF/MANIFEST.MF 2>/dev/null; then
        if grep -q "Spring-Boot-Version" META-INF/MANIFEST.MF 2>/dev/null; then
            spring_boot_version=$(grep "Spring-Boot-Version" META-INF/MANIFEST.MF | cut -d' ' -f2)
            log_info "  🍃 Spring Boot版本: ${spring_boot_version}"
        fi
        if grep -q "Main-Class" META-INF/MANIFEST.MF 2>/dev/null; then
            main_class=$(grep "Main-Class" META-INF/MANIFEST.MF | cut -d' ' -f2)
            log_info "  🚀 主类: ${main_class}"
        fi
        rm -rf META-INF 2>/dev/null || true
    fi
    
    log_info ""
    log_info "🎉 测试完成！部署脚本应该可以正常工作。"
    log_info "你现在可以运行: ./deploy.sh"
    
else
    log_error "❌ 构建失败，jar文件不存在"
    log_info "检查 build/libs/ 目录内容："
    if [[ -d "build/libs/" ]]; then
        ls -la build/libs/
    else
        log_warn "build/libs/ 目录不存在"
    fi
    exit 1
fi
