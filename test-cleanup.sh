#!/bin/bash

# 测试清理功能脚本
# 验证清理远程文件功能是否包含jar文件清理

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🧹 测试清理功能脚本..."

# 检查deploy.sh是否存在清理jar文件的代码
log_info "1. 检查deploy.sh中的清理功能..."

if grep -q "清理旧jar文件" deploy.sh; then
    log_info "✅ 找到清理旧jar文件的代码"
else
    log_error "❌ 未找到清理旧jar文件的代码"
    exit 1
fi

if grep -q "*.jar" deploy.sh; then
    log_info "✅ 找到jar文件匹配模式"
else
    log_error "❌ 未找到jar文件匹配模式"
    exit 1
fi

if grep -q "rm -f" deploy.sh; then
    log_info "✅ 找到删除文件命令"
else
    log_error "❌ 未找到删除文件命令"
    exit 1
fi

# 显示清理相关的代码段
log_info "2. 显示清理功能相关代码..."
echo ""
echo "=== 清理功能代码段 ==="
grep -A 20 "# 清理除了当前版本以外的旧jar文件" deploy.sh || true
echo "========================"
echo ""

# 检查清理功能的顺序是否正确
log_info "3. 检查部署流程中的清理顺序..."

if grep -A 10 "main()" deploy.sh | grep -q "cleanup_remote"; then
    log_info "✅ 清理功能已包含在主部署流程中"
    
    # 显示部署流程
    echo ""
    echo "=== 部署流程 ==="
    grep -A 15 "check_dependencies" deploy.sh | grep -E "(build_project|backup_remote|upload_to_remote|cleanup_remote|stop_remote_service|start_remote_service)" || true
    echo "============="
    echo ""
else
    log_error "❌ 清理功能未包含在主部署流程中"
    exit 1
fi

# 检查清理功能的详细内容
log_info "4. 分析清理功能包含的内容..."

cleanup_features=(
    "清理旧jar文件"
    "清理过期日志文件" 
    "清理临时文件"
    "清理空目录"
)

for feature in "${cleanup_features[@]}"; do
    if grep -q "$feature" deploy.sh; then
        log_info "✅ 包含: $feature"
    else
        log_warn "⚠️  缺少: $feature"
    fi
done

# 检查清理功能的保护措施
log_info "5. 检查清理功能的保护措施..."

if grep -q "保留当前版本" deploy.sh; then
    log_info "✅ 有保护当前版本jar文件的机制"
else
    log_warn "⚠️  缺少保护当前版本jar文件的机制"
fi

if grep -q "保留.*天" deploy.sh; then
    log_info "✅ 有保留日志文件天数的配置"
else
    log_warn "⚠️  缺少日志文件保留天数配置"
fi

log_info ""
log_info "🎉 清理功能测试完成！"
log_info ""
log_info "📋 清理功能总结："
log_info "  ✅ 清理除当前版本外的旧jar文件"
log_info "  ✅ 清理过期日志文件（可配置保留天数）"
log_info "  ✅ 清理临时文件（.tmp, nohup.out, hs_err_pid*.log）"
log_info "  ✅ 清理空目录（保留logs和backup目录）"
log_info "  ✅ 显示清理后的目录内容"
log_info ""
log_info "🔧 清理功能在部署流程中的位置："
log_info "  构建项目 → 备份远程 → 上传文件 → 🧹清理环境 → 停止服务 → 启动服务"
log_info ""
log_info "现在清理功能会正确删除旧的jar文件了！"
