#!/bin/bash

# 验证脚本 - 确保所有修复都正常工作
set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🔍 开始验证部署脚本修复..."

# 1. 测试脚本帮助功能
log_info "1. 测试帮助功能..."
if ./deploy.sh -h > /dev/null 2>&1; then
    log_info "✅ 帮助功能正常"
else
    log_error "❌ 帮助功能失败"
    exit 1
fi

# 2. 测试配置文件加载
log_info "2. 测试配置文件加载..."
if [[ -f "deploy.config" ]]; then
    # 测试配置加载（使用无效服务器避免实际连接）
    if ./deploy.sh -u test -s test.invalid 2>&1 | grep -q "加载配置文件"; then
        log_info "✅ 配置文件加载正常"
    else
        log_error "❌ 配置文件加载失败"
        exit 1
    fi
else
    log_warn "⚠️  配置文件不存在，跳过此测试"
fi

# 3. 测试构建功能
log_info "3. 测试构建功能..."
if ./test-build.sh > /dev/null 2>&1; then
    log_info "✅ bootJar构建正常"
else
    log_error "❌ bootJar构建失败"
    exit 1
fi

# 4. 验证jar文件名称
log_info "4. 验证jar文件名称..."
expected_jar="ContentManager-0.0.4-SNAPSHOT.jar"
if [[ -f "build/libs/$expected_jar" ]]; then
    log_info "✅ JAR文件名称正确: $expected_jar"
else
    log_error "❌ JAR文件名称不匹配"
    log_info "期望: $expected_jar"
    log_info "实际文件:"
    ls -la build/libs/
    exit 1
fi

# 5. 检查权限设置
log_info "5. 检查配置文件权限..."
if [[ -f "deploy.config" ]]; then
    config_perms=$(stat -f "%A" "deploy.config" 2>/dev/null || stat -c "%a" "deploy.config" 2>/dev/null)
    if [[ "$config_perms" == "600" ]]; then
        log_info "✅ 配置文件权限安全: $config_perms"
    else
        log_warn "⚠️  配置文件权限不安全: $config_perms (建议600)"
    fi
fi

# 6. 检查gitignore设置
log_info "6. 检查gitignore设置..."
if grep -q "deploy.config" .gitignore; then
    log_info "✅ 配置文件已添加到gitignore"
else
    log_warn "⚠️  配置文件未添加到gitignore"
fi

# 7. 检查文件完整性
log_info "7. 检查文件完整性..."
expected_files=(
    "deploy.sh"
    "deploy.config.example"
    "setup-config.sh"
    "test-build.sh"
    "DEPLOY_README.md"
    "DEPLOYMENT_SETUP.md"
)

for file in "${expected_files[@]}"; do
    if [[ -f "$file" ]]; then
        log_info "✅ $file 存在"
    else
        log_error "❌ $file 缺失"
        exit 1
    fi
done

# 8. 验证可执行权限
log_info "8. 验证可执行权限..."
executable_files=("deploy.sh" "setup-config.sh" "test-build.sh" "verify-fixes.sh")
for file in "${executable_files[@]}"; do
    if [[ -x "$file" ]]; then
        log_info "✅ $file 有执行权限"
    else
        log_error "❌ $file 缺少执行权限"
        chmod +x "$file"
        log_info "🔧 已修复 $file 的执行权限"
    fi
done

log_info ""
log_info "🎉 所有验证通过！部署脚本已成功修复并优化。"
log_info ""
log_info "📋 修复内容总结："
log_info "  ✅ 修复了 declare -g 兼容性问题"
log_info "  ✅ 更新构建命令为 bootJar"
log_info "  ✅ 支持密码配置"
log_info "  ✅ 配置文件安全检查"
log_info "  ✅ 完整的文件结构"
log_info ""
log_info "🚀 现在你可以安全地使用部署脚本了："
log_info "   ./deploy.sh                    # 使用配置文件部署"
log_info "   ./deploy.sh -u user -s server  # 使用命令行参数"
log_info "   ./deploy.sh -h                 # 查看帮助信息"
