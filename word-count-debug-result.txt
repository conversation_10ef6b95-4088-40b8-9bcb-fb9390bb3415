=== 字数计算调试 ===
测试时间: <PERSON><PERSON> Jun 24 10:44:42 CST 2025

=== 方法1：getAllElementsFromObject ===
获取到的Object数量: 29
转换后的段落数量: 29
计算的总字数: 0

=== 方法2：直接从documentPart.getContent() ===
documentPart.getContent()数量: 31
直接获取的段落数量: 29
计算的总字数: 0

=== 方法3：使用TextUtils ===
TextUtils提取失败: class org.docx4j.openpackaging.packages.WordprocessingMLPackage以及其任何超类对此上下文都是未知的。

=== 段落内容详细分析 ===
段落 1:
  样式: a3
  原始文本: ''
  去空格后: ''
  字数: 0

段落 2:
  样式: 1
  原始文本: ''
  去空格后: ''
  字数: 0

段落 3:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 4:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 5:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 6:
  样式: 2
  原始文本: ''
  去空格后: ''
  字数: 0

段落 7:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 8:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 9:
  样式: Normal
  原始文本: ''
  去空格后: ''
  字数: 0

段落 10:
  样式: 3
  原始文本: ''
  去空格后: ''
  字数: 0

... 还有 19 个段落

=== 测试实际分割方法 ===
分割结果文件数: 5
  - multi-level-headings_chapter_01_这是这篇文章的标题.docx
  - multi-level-headings_chapter_02_01_这是我的第一个一级标题_这是我的第一个一级标题.docx
  - multi-level-headings_chapter_02_02_这是我的第一个一级标题_这是我的二级标题.docx
  - multi-level-headings_chapter_03_这是我的第二个一级标题.docx
  - multi-level-headings_chapter_04_这是我的第三个一级标题.docx

=== 问题诊断 ===
❌ 两种方法都计算出0字数，问题在于文本提取

============================================================
